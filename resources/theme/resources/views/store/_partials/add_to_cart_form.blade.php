@php
    /** @var \App\Models\Product $product */
    /** @var string|null $cta_label */
    /** @var string|null $cta_action */
    /** @var string|null $cta_classes */
    /** @var array|null $metadata */

     $style = theme('store_button_style', 'btn-brand');
@endphp

@if($cta_action === 'show_details')
    <div class="productListing__addToCartContainer--grid">
        <a href="{{ route('store.show', [$product->slug]) }}" class="tw-w-full tw-no-underline">
            <button type="button" class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif btn {{ $style }} btn-block productListing__addToCartButton--grid">
                {{ $cta_label ?? $product->cartActionLabel() }}
            </button>
        </a>
    </div>
@elseif($product->setting('links_externally'))
    <a href="{{ $product->setting('links_externally:url', url('/store/product/' . $product->slug)) }}" target="_blank" class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif btn {{ $style }} productListing__addToCartButton--grid">
        {{ $product->setting('links_externally:text', 'More Info') }}
    </a>
@elseif($product->hasVariants())
    <livewire:theme.add-variant-button
            :product="$product"
            :has_subscription="false"
            :has_order="false"
            :cta_label="$cta_label ?? null"
            :cta_classes="$cta_classes ?? null"
            :metadata="$metadata ?? []"
    />
@elseif($product->isOutOfStock())
    <div class="productListing__addToCartContainer--grid">
        <button class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif btn {{ $style }} productListing__addToCartButton--grid" type="button" disabled>
            Sold Out
        </button>
    </div>
@elseif(auth()->guest())
    <div class="productListing__addToCartContainer--grid">
        <button type="button" x-data x-on:click="$dispatch('open-modal-register-and-add-product', { product_id: {{ $product->id }}, product_metadata: @js($metadata) })" class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif btn {{ $style }} btn-block productListing__addToCartButton--grid">
            {{ $cta_label ?? $product->cartActionLabel() }}
        </button>
    </div>

@elseif($product->isPreOrder() || ($product->isGiftCard() && $product->isFulfilledVirtually()))
    <div class="productListing__addToCartContainer--grid">
        <a href="{{ $product->checkoutPath() }}" class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif btn {{ $style }} productListing__addToCartButton--grid">{{ $product->cartActionLabel() }}</a>
    </div>
@else
    <div class="productListing__productAddToCartForm--grid" id="shoppingCartForm_{{ $product->slug }}">
        <div class="productListing__addToCartContainer--grid">
            <livewire:theme.add-product-button
                    :product="$product"
                    :has_subscription="false"
                    :has_order="false"
                    :cta_label="$cta_label ?? null"
                    :cta_classes="$cta_classes ?? null"
                    :metadata="$metadata ?? []"
            />
        </div>
    </div>
@endif
