@php
    /** @var \App\Models\Product $product */
	/** @var \App\Models\Order|null $order */
    /** @var \App\Contracts\Cartable $cart */
    /** @var Illuminate\Support\Collection $products */
    /** @var bool $has_subscription */

    $is_brand_style = str(theme('store_button_style', 'btn-brand'))->contains('brand');

     // Spelling out all classes here so that they are picked up by the tailwind compiler
	$callout_border_class = ! $is_brand_style ? 'tw-border-theme-brand-color/70' : 'tw-border-theme-action-color/70';
	$background_class = ! $is_brand_style ? 'tw-bg-theme-brand-color' : 'tw-bg-theme-action-color';
	$light_background_class = ! $is_brand_style ? 'tw-bg-theme-brand-color/5' : 'tw-bg-theme-action-color/5';
	$text_class = ! $is_brand_style ? 'tw-text-theme-brand-color' : 'tw-text-theme-action-color';

    $store_service = app(\App\Services\StoreService::class);
    $delivery_method = $store_service->deliveryMethod(request()->cookie('shopping_delivery_method_id'));

    $photos = $product->photos()->get(['id', 'path', 'thumbnail_path', 'title']);
    $standard_settings = $product->setting('standard_template');

     $related_products = $standard_settings?->related_products
        ? app(\App\Repositories\StoreRepository::class)->getRelatedProducts($standard_settings->related_products)
        : collect();

     $related_product_columns = match($related_products->count()) {
        5 => 'tw-grid-cols-1 sm:tw-grid-cols-3 lg:tw-grid-cols-5',
        4 => 'tw-grid-cols-1 sm:tw-grid-cols-2 lg:tw-grid-cols-4',
        3 => 'tw-grid-cols-1 sm:tw-grid-cols-3 ',
        2 => 'tw-grid-cols-1 sm:tw-grid-cols-2',
        default => 'tw-grid-cols-1',
    };

     $related_recipes = $standard_settings?->related_recipes
        ? app(\App\Repositories\StoreRepository::class)->getRelatedRecipes($standard_settings->related_recipes)
        : collect();
@endphp

<div class="tw-reset">
    <div class="tw-bg-white">
        <div class="tw-mx-auto tw-max-w-2xl tw-px-4 tw-pt-6 sm:tw-px-6 sm:tw-pt-8 lg:tw-max-w-7xl lg:tw-px-8">
            <x-theme::breadcrumbs
                    :category="$product->category?->parentCategory ? $product->category->parentCategory : $product->category"
                    :subcategory="$product->category?->parentCategory ? $product->category : null"
                    :product="$product"
            />
        </div>
        <section
                itemscope itemtype="https://schema.org/Product"
                itemid="{{ route('store.show', [$product->slug]) }}"
                id="product_{{ $product->slug }}"
        >
            <meta itemprop="brand" content="Seven Sons Farm"/>

            @if(!is_null($product->category?->parentCategory))
                <meta itemprop="category" content="{{ $product->category->parentCategory . ' > ' . $product->category }}"/>

            @else
                <meta itemprop="category" content="{{ $product->category }}"/>
            @endif
            <div class="tw-mx-auto tw-max-w-2xl tw-px-4 tw-pb-4 sm:tw-px-6 sm:tw-pb-6 lg:tw-max-w-7xl lg:tw-px-8 lg:tw-pb-8">
                <div class="tw-mt-8 lg:tw-grid lg:tw-grid-cols-2 lg:tw-items-start lg:tw-gap-x-12">
                    <!-- Image gallery -->
                    <div x-data="{ active_photo_id: @json($photos->first()?->id) }" class="tw-flex tw-flex-col-reverse">
                        <!-- Image selector -->
                        <div class="tw-mx-auto tw-mt-6 tw-w-full tw-max-w-2xl lg:tw-max-w-none">
                            <div class="tw-flex tw-gap-4 sm:tw-gap-6" aria-orientation="horizontal" role="tablist">
                                @foreach($photos as $index => $media)
                                    @php /** @var \App\Models\Media $media */ @endphp

                                    <div class="tw-w-1/5">
                                        <button x-on:click="active_photo_id = {{ $media->id }}" id="tabs-1-tab-{{ $index }}" class="tw-relative tw-flex tw-w-full tw-aspect-w-1 tw-aspect-h-1 tw-cursor-pointer tw-items-center tw-justify-center tw-rounded-md tw-bg-white tw-text-sm tw-font-medium tw-uppercase tw-text-gray-900 hover:tw-bg-gray-50 focus:tw-outline-none" aria-controls="tabs-1-panel-{{ $index }}" role="tab" type="button">
                                            <span class="tw-absolute tw-inset-0 tw-overflow-hidden tw-rounded-md">
                                                <img src="{{ \App\Models\Media::s3ToCloudfront($media->thumbnail_path) }}" alt="{{ $media->title }}" class="tw-h-full tw-w-full tw-object-cover tw-object-center">
                                            </span>
                                            <span x-bind:class="active_photo_id === {{ $media->id }} ? 'tw-ring-theme-action-color' : 'tw-ring-transparent'" class="tw-pointer-events-none tw-absolute tw-inset-0 tw-rounded-md tw-ring-2 tw-ring-offset-2" aria-hidden="true"></span>
                                        </button>
                                    </div>

                                @endforeach
                            </div>

                        </div>

                        <div class="tw-w-full tw-aspect-h-1 tw-aspect-w-1">
                            @foreach($photos as $index => $media)
                                <!-- Tab panel, show/hide based on tab state. -->
                                <div x-show="active_photo_id === {{ $media->id }}"
                                     x-transition:enter="tw-transition tw-ease-out tw-duration-200"
                                     x-transition:enter-start="tw-opacity-0"
                                     x-transition:enter-end="tw-opacity-100"
                                     x-transition:leave="tw-transition tw-ease-in tw-duration-200"
                                     x-transition:leave-start="tw-opacity-100"
                                     x-transition:leave-end="tw-opacity-0"
                                     class="tw-h-full tw-w-full "
                                     id="tabs-1-panel-{{ $index }}"
                                     aria-labelledby="tabs-1-tab-{{ $index }}"
                                     role="tabpanel"
                                     tabindex="0"
                                >
                                    <img itemprop="image" src="{{ \App\Models\Media::s3ToCloudfront($media->path) }}" alt="{{ $media->title }}" class="tw-h-full tw-w-full tw-object-cover tw-object-center sm:tw-rounded-lg">
                                    @if($product->calloutMessage())
                                        <div class="tw-absolute tw-top-0 tw-right-0 tw-p-2">
                                            <span class="tw-inline-flex tw-items-center tw-bg-white tw-gap-x-1.5 tw-rounded-md tw-px-3 tw-py-1.5 tw-text-sm tw-font-semibold tw-text-theme-action-color tw-shadow-md tw-ring-1 tw-ring-inset tw-ring-gray-200">
                                                {{ $product->calloutMessage() }}
                                            </span>
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Product info -->
                    <div class="tw-mt-10 sm:tw-mt-16 lg:tw-mt-0">
                        <h1 itemprop="name" class="tw-m-0 tw-text-3xl tw-font-body tw-font-bold tw-tracking-tight tw-text-gray-900 sm:tw-text-4xl">{{ $product->title }}</h1>
                        <meta itemprop="sku" content="{{ $product->sku }}"/>
                        <section aria-labelledby="information-heading" class="tw-mt-4">
                            <h2 id="information-heading" class="tw-m-0 tw-sr-only">Product information</h2>

                            <div class="tw-flex tw-flex-col sm:tw-items-center sm:tw-flex-row">
                                <div class="tw-flex tw-items-baseline tw-space-x-2"
                                     itemprop="offers"
                                     itemscope
                                     itemtype="https://schema.org/Offer"
                                >

                                    @if($product->isOnSale())
                                        <p class="tw-m-0 tw-text-2xl tw-font-semibold tw-text-theme-brand-color sm:tw-text-3xl">
                                            &#36;<span itemprop="price">{{ money($product->getPrice()) }}</span>
                                        </p>
                                        <p class="tw-m-0 tw-text-base tw-text-gray-500 tw-line-through sm:tw-text-lg">
                                            &#36;{{ money($product->getRegularPrice()) }}
                                        </p>
                                    @else
                                        <p class="tw-m-0 tw-text-2xl tw-font-semibold tw-text-gray-700 sm:tw-text-3xl">
                                            &#36;<span itemprop="price">{{ money($product->getPrice()) }}</span>
                                        </p>
                                    @endif

                                    <meta itemprop="priceCurrency" content="{{ setting('currency', 'USD') }}"/>
                                    <meta itemprop="url" content="{{ route('store.show', [$product->slug]) }}"/>

                                </div>

                                <div itemprop="aggregateRating"
                                     itemscope
                                     itemtype="https://schema.org/AggregateRating"
                                     class="tw-mt-1 sm:tw-mt-0 sm:tw-ml-4 sm:tw-pl-4 sm:tw-border-l sm:tw-border-gray-300"
                                >
                                    <h2 class="tw-m-0 tw-sr-only">Reviews</h2>
                                    <a href="#reviews" class="tw-flex tw-items-center tw-no-underline">
                                        <div>
                                            <div class="tw-flex tw-items-center">
                                                <!-- Active: "text-yellow-400", Default: "text-gray-300" -->
                                                <svg class="tw-h-5 tw-w-5 tw-shrink-0 tw-text-[#E9AE20]" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                                    <path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401Z" clip-rule="evenodd"></path>
                                                </svg>
                                                <svg class="tw-h-5 tw-w-5 tw-shrink-0 tw-text-[#E9AE20]" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                                    <path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401Z" clip-rule="evenodd"></path>
                                                </svg>
                                                <svg class="tw-h-5 tw-w-5 tw-shrink-0 tw-text-[#E9AE20]" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                                    <path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401Z" clip-rule="evenodd"></path>
                                                </svg>
                                                <svg class="tw-h-5 tw-w-5 tw-shrink-0 tw-text-[#E9AE20]" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                                    <path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401Z" clip-rule="evenodd"></path>
                                                </svg>
                                                <svg class="tw-h-5 tw-w-5 tw-shrink-0 tw-text-[#E9AE20]" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                                    <path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401Z" clip-rule="evenodd"></path>
                                                </svg>
                                            </div>
                                            <p class="tw-sr-only"><span itemprop="ratingValue">{{ $store_service->rating() }}</span> out of 5 stars</p>
                                        </div>
                                        <p class="tw-m-0 tw-ml-2 tw-text-sm tw-text-gray-500">
                                            <span itemprop="reviewCount">{{ $store_service->reviewCount() }}</span> reviews</p>
                                    </a>
                                </div>
                            </div>

                            @if(!empty($product->unit_description))
                                <p class="tw-m-0 tw-mt-2 tw-text-sm tw-text-gray-600">
                                    {{ $product->unit_description }}
                                </p>
                            @endif

                            <div class="tw-mt-6 tw-space-y-6">
                                <div class="tw-prose tw-prose-sm tw-pb-6 tw-text-base tw-text-gray-600">
                                    {!! $product->summary !!}
                                </div>
                            </div>
                            <div>
                                <p class="tw-m-0 tw-text-gray-700">
                                    Subscribe & Save at checkout for
                                    <span class="tw-font-bold">bonus 5% savings</span>
                                    <span class="tw-italic">and</span> your choice of ground beef, bacon-infused burgers, or breakfast sausage
                                    <span class="tw-font-bold">free in every order</span>.
                                </p>
                            </div>

                            <div class="tw-mt-2 tw-flex tw-flex-col tw-space-y-4 sm:tw-mt-6 sm:tw-flex-row sm:tw-items-center sm:tw-space-y-0">
                                @if($product->isOutOfStock())
                                    <link itemprop="availability" href="https://schema.org/OutOfStock"/>
                                    <div class="tw-flex-auto tw-flex tw-items-center">
                                        <svg class="tw-h-5 tw-w-5 tw-shrink-0 tw-text-[#E9AE20]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"/>
                                        </svg>
                                        <p class="tw-m-0 tw-ml-2 tw-text-sm tw-text-gray-500">Currently out of stock</p>
                                    </div>
                                @else
                                    <link itemprop="availability" href="https://schema.org/InStock"/>
                                    <div class="tw-flex-auto tw-flex tw-items-center">
                                        <svg class="tw-h-5 tw-w-5 tw-shrink-0 tw-text-theme-brand-color" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd"></path>
                                        </svg>
                                        <p class="tw-m-0 tw-ml-2 tw-text-sm tw-text-gray-500">In stock and ready to {{ ($delivery_method?->isDeliveryZone() ?? true) ? 'ship' : 'pickup' }}</p>
                                    </div>
                                @endif

                                @if(!is_null($delivery_method) && ($product->getPrice() >= $delivery_method->delivery_total_threshold))
                                    <div class="tw-flex-auto tw-flex tw-items-center">
                                        <svg class="tw-h-5 tw-w-5 tw-shrink-0 tw-text-theme-brand-color" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd"></path>
                                        </svg>
                                        <p class="tw-m-0 tw-ml-2 tw-text-sm tw-text-gray-500">Qualifies for free shipping</p>
                                    </div>
                                @endif
                            </div>
                        </section>

                        <div class="tw-mt-8" x-data="{ shown: false }" x-intersect:enter="shown ? $dispatch('add-to-cart-entered') : () => {}" x-intersect:leave="$dispatch('add-to-cart-left')">
                            <x-theme::form.add-product-counter-button
                                    :product="$product"
                                    :order="$openOrder"
                                    :has_subscription="$has_subscription"
                                    :cta_label="$cta_label ?? null"
                                    :cta_action="$cta_action ?? null"
                                    cta_classes="tw-px-8 tw-py-3 tw-text-base tw-font-medium tw-rounded-lg tw-font-semibold tw-bg-theme-brand-color tw-text-white"
                                    variant_layout="expanded"
                                    :metadata="[
                                        'item_category' => $product->category?->parentCategory?->name ?? $product->category?->name,
                                        'item_category2' => ! is_null($product->category?->parentCategory) ? $product->category?->name : null,
                                        'item_list_id' => 'standard_' . $product->slug . '_page',
                                        'item_list_name' => 'Standard ' . $product->title . ' Page',
                                    ]"
                            />
                        </div>

                        <section aria-labelledby="details-heading" class="tw-mt-10">
                            <h2 id="details-heading" class="tw-sr-only">Additional details</h2>

                            <div class="tw-divide-y tw-divide-gray-200 tw-border-t">
                                <!-- Start Product Details -->
                                <div x-data="{ open: false }">
                                    <h3 class="tw-m-0">
                                        <!-- Expand/collapse question button -->
                                        <button type="button" x-on:click="open = !open" class="tw-group tw-relative tw-flex tw-w-full tw-items-center tw-justify-between tw-px-4 tw-py-6 tw-text-left tw-transition-all tw-duration-150 hover:tw-bg-gray-50" aria-controls="disclosure-3" aria-expanded="false">
                                            <!-- Open: "text-indigo-600", Closed: "text-gray-900" -->
                                            <div class="tw-flex tw-items-center tw-space-x-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="tw-size-6">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"/>
                                                </svg>

                                                <span class="tw-text-sm tw-font-semibold tw-font-body tw-text-gray-900">Product Details</span>

                                            </div>
                                            <span class="tw-ml-6 tw-flex tw-items-center">
                                                <!-- Open: "hidden", Closed: "block" -->
                                                <svg x-bind:class="open && 'tw-hidden'" class="tw-block tw-h-6 tw-w-6 tw-text-gray-400 group-hover:tw-text-gray-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15"></path>
                                                </svg>
                                                <!-- Open: "block", Closed: "hidden" -->
                                                <svg x-bind:class="!open && 'tw-hidden'" class="tw-block tw-h-6 tw-w-6 tw-text-theme-brand-color group-hover:tw-text-theme-brand-color/80" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h14"></path>
                                                </svg>
                                            </span>
                                        </button>
                                    </h3>
                                    <div x-show="open" x-cloak class="tw-bg-gray-50 tw-shadow-inner tw-w-full tw-px-6 tw-pt-6 tw-pb-8" id="disclosure-4">
                                        <div itemprop="description" class="tw-prose tw-prose-sm">
                                            {!! $product->description !!}
                                        </div>
                                    </div>
                                </div>
                                <!-- End Product Details -->

                                <!-- Start Weird Free -->
                                <div x-data="{ open: false }">
                                    <h3 class="tw-m-0">
                                        <!-- Expand/collapse question button -->
                                        <button type="button" x-on:click="open = !open" class="tw-group tw-relative tw-flex tw-w-full tw-items-center tw-justify-between tw-px-4 tw-py-6 tw-text-left tw-transition-all tw-duration-150 hover:tw-bg-gray-50" aria-controls="disclosure-2" aria-expanded="false">
                                            <!-- Open: "text-indigo-600", Closed: "text-gray-900" -->
                                            <div class="tw-flex tw-items-center tw-space-x-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="tw-size-6">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"/>
                                                </svg>

                                                <span class="tw-text-sm tw-font-semibold tw-font-body tw-text-gray-900">100% Weird Free Guarantee</span>
                                            </div>
                                            <span class="tw-ml-6 tw-flex tw-items-center">
                                                <!-- Open: "hidden", Closed: "block" -->
                                                <svg x-bind:class="open && 'tw-hidden'" class="tw-block tw-h-6 tw-w-6 tw-text-gray-400 group-hover:tw-text-gray-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15"></path>
                                                </svg>
                                                <!-- Open: "block", Closed: "hidden" -->
                                                <svg x-bind:class="!open && 'tw-hidden'" class="tw-block tw-h-6 tw-w-6 tw-text-theme-brand-color group-hover:tw-text-theme-brand-color/80" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h14"></path>
                                                </svg>
                                            </span>
                                        </button>
                                    </h3>
                                    <div x-show="open" x-cloak class="tw-bg-gray-50 tw-shadow-inner tw-w-full tw-px-6 tw-pt-6 tw-pb-8" id="disclosure-4">
                                        <div class="tw-prose tw-prose-sm">
                                            <p class="tw-m-0 tw-text-xl tw-font-bold">100% Brothers' Guarantee</p>
                                            <p class="tw-m-0 tw-mt-2">We treat our customers like family. If you are not satisfied, we’ll refund your money back, and you keep the product.</p>

                                            <ul class="tw-pl-1 tw-space-y-2 tw-list-none">
                                                <li class="tw-flex tw-items-center tw-text-base tw-space-x-1">
                                                    <svg class="tw-m-0 tw-text-theme-action-color tw-size-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M18.364 18.364A9 9 0 0 0 5.636 5.636m12.728 12.728A9 9 0 0 1 5.636 5.636m12.728 12.728L5.636 5.636"/>
                                                    </svg>
                                                    <span class="tw-m-0 tw-inline-block">NO mRNA or glyphosate</span>
                                                </li>
                                                <li class="tw-flex tw-items-center tw-text-base tw-space-x-1">
                                                    <svg class="tw-m-0 tw-text-theme-action-color tw-size-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M18.364 18.364A9 9 0 0 0 5.636 5.636m12.728 12.728A9 9 0 0 1 5.636 5.636m12.728 12.728L5.636 5.636"/>
                                                    </svg>
                                                    <span class="tw-m-0 tw-inline-block">NO hormones or steroids</span>
                                                </li>
                                                <li class="tw-flex tw-items-center tw-text-base tw-space-x-1">
                                                    <svg class="tw-m-0 tw-text-theme-action-color tw-size-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M18.364 18.364A9 9 0 0 0 5.636 5.636m12.728 12.728A9 9 0 0 1 5.636 5.636m12.728 12.728L5.636 5.636"/>
                                                    </svg>
                                                    <span class="tw-m-0 tw-inline-block">NO GMOs or antibiotics</span>
                                                </li>
                                                <li class="tw-flex tw-items-center tw-text-base tw-space-x-1">
                                                    <svg class="tw-m-0 tw-text-theme-action-color tw-size-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M18.364 18.364A9 9 0 0 0 5.636 5.636m12.728 12.728A9 9 0 0 1 5.636 5.636m12.728 12.728L5.636 5.636"/>
                                                    </svg>
                                                    <span class="tw-m-0 tw-inline-block">NO MSG, synthetic nitrates, or sugar</span>
                                                </li>
                                            </ul>
                                            <p class="tw-m-0">
                                                <span class="tw-font-bold">Lastly, we treat our customers like family</span>. If there's something that doesn't meet your expectations, let us know, and we'll make it right.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <!-- End Weird Free -->

                                <!-- Start Delivery Details -->
                                <div x-data="{ open: false }">
                                    <h3 class="tw-m-0">
                                        <!-- Expand/collapse question button -->
                                        <button type="button" x-on:click="open = !open" class="tw-group tw-relative tw-flex tw-w-full tw-items-center tw-justify-between tw-px-4 tw-py-6 tw-text-left tw-transition-all tw-duration-150 hover:tw-bg-gray-50" aria-controls="disclosure-4" aria-expanded="false">
                                            <!-- Open: "text-indigo-600", Closed: "text-gray-900" -->
                                            <div class="tw-flex tw-items-center tw-space-x-3">

                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="tw-size-6">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"/>
                                                </svg>

                                                <span class="tw-text-sm tw-font-semibold tw-font-body tw-text-gray-900">Delivery Details</span>
                                            </div>
                                            <span class="tw-ml-6 tw-flex tw-items-center">
                                                <!-- Open: "hidden", Closed: "block" -->
                                                <svg x-bind:class="open && 'tw-hidden'" class="tw-block tw-h-6 tw-w-6 tw-text-gray-400 group-hover:tw-text-gray-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15"></path>
                                                </svg>
                                                <!-- Open: "block", Closed: "hidden" -->
                                                <svg x-bind:class="!open && 'tw-hidden'" class="tw-block tw-h-6 tw-w-6 tw-text-theme-brand-color group-hover:tw-text-theme-brand-color/80" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h14"></path>
                                                </svg>
                                            </span>
                                        </button>
                                    </h3>
                                    <div x-show="open" x-cloak class="tw-bg-gray-50 tw-shadow-inner tw-w-full tw-px-6 tw-pt-6 tw-pb-8" id="disclosure-4">
                                        <div class="tw-prose tw-prose-sm">
                                            <h4 class="tw-text-xl tw-font-body tw-font-semibold tw-text-gray-900">Fast Home Delivery</h4>

                                            @if(!is_null($delivery_method) && ($product->getPrice() >= $delivery_method->delivery_total_threshold))
                                                <p class="tw-text-base tw-text-gray-700">
                                                    Get your order in as little as two days with our weekly deliveries. We deliver everywhere within the continental US.
                                                </p>
                                            @else
                                                <p class="tw-text-base tw-text-gray-700">
                                                    Free delivery starting at
                                                    <a href="{{ url('/how-home-delivery-works') }}">$159+ in many states</a>! Plus, get your order in as little as two days with our weekly deliveries. We deliver everywhere within the continental US.
                                                </p>
                                            @endif

                                            <p class="tw-text-base tw-text-gray-700">Before confirming your purchase, you'll receive an estimated delivery date so you can plan accordingly.</p>

                                            <h4 class=" tw-text-xl tw-font-body tw-font-semibold tw-text-gray-900">
                                                Seven Sons' Five Steps for Fast and Safe Delivery
                                            </h4>
                                            <ol class="tw-text-base tw-text-gray-700">
                                                <li> Items are vacuumed sealed in BPA-free packaging for safe transit.</li>
                                                <li>Extra insulated boxes are designed to withstand hot temperatures.</li>
                                                <li>Vapor liners are used to reduce airflow & keep your food cold.</li>
                                                <li>Extra dry ice to keep food safe for at least an extra 24 hours.</li>
                                                <li>SMS notifications to update you about the status of your order.</li>
                                            </ol>
                                            <p class="tw-text-base tw-text-gray-700">To learn more about our home delivery service, please visit our
                                                <a href="{{ url('/how-home-delivery-works') }}">delivery FAQ page</a>.</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- End Delivery Details -->
                            </div>
                        </section>
                    </div>
                </div>
            </div>


            <!-- Start Score Section -->
            <div class="tw-bg-gray-50 tw-py-16 sm:tw-py-24">
                <!-- Start "The take" -->
                @if(($standard_settings?->heading ?? false) || ($standard_settings?->subheading ?? false))
                    <div class="tw-px-4 sm:tw-px-0 @if($standard_settings?->featured_image_path ?? false) tw-pb-10 sm:tw-pb-16 @else tw-pb-16 sm:tw-pb-24 @endif">
                        <div class="tw-mx-auto tw-max-w-4xl">
                            @if($standard_settings?->heading ?? false)
                                <div class="tw-m-0 tw-text-xl tw-font-body tw-text-gray-900 lg:tw-text-3xl">{!! $standard_settings->heading !!}</div>
                            @endif
                            @if($standard_settings?->subheading ?? false)
                                <div class="tw-m-0 tw-mt-3 tw-max-w-4xl tw-text-base tw-text-gray-600 lg:tw-text-xl">
                                    {!! $standard_settings->subheading !!}
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
                <!-- End "The take" -->

                @if($standard_settings?->featured_image_path ?? false)
                    <div class="tw-mx-auto tw-px-4 sm:tw-px-6 lg:tw-max-w-7xl lg:tw-px-8">
                        <div class="tw-grid tw-grid-cols-1 tw-gap-x-8 tw-gap-y-16 sm:tw-gap-y-20 lg:tw-grid-cols-2 lg:tw-items-start">
                            <div class="tw-px-6 md:tw-px-0 lg:tw-pr-4">
                                <div class="tw-px-6 sm:tw-mx-auto sm:tw-max-w-2xl sm:tw-rounded-3xl sm:tw-pl-16 sm:tw-pr-0 lg:tw-mx-0 lg:tw-max-w-none">
                                    <h3 class="tw-sr-only">Product cut</h3>
                                    <div class="tw-mt-4 tw-relative tw-isolate tw-mx-auto tw-w-full tw-max-w-[400px] tw-rounded-xl tw-shadow-xl lg:tw-mt-0" id="tolstoy-container" style="line-height:0;overflow:hidden;height:100%;width:100%;text-align:center">
                                        <img src="{{ $standard_settings?->featured_image_path }}" alt=""/>
                                    </div>
                                </div>
                            </div>
                            <div class="tw-h-full tw-flex tw-items-center tw-px-4 lg:tw-px-0">
                                <div class="tw-mx-auto tw-w-full tw-max-w-2xl lg:tw-mx-0 lg:tw-max-w-sm">
                                    <h3 class="tw-sr-only">Product score</h3>
                                    <div class="tw-space-y-10">
                                        <div>
                                            <h4 class="tw-m-0 tw-font-body tw-text-base tw-font-semibold">Tenderness</h4>
                                            <div class="tw-mt-3">
                                                <x-theme::product-score :score="$standard_settings?->tenderness_score ?? null"/>
                                            </div>
                                            <div class="tw-mt-2 tw-flex tw-items-center tw-justify-between">
                                                <p class="tw-m-0 tw-text-sm tw-text-gray-500">Hearty</p>
                                                <p class="tw-m-0 tw-text-sm tw-text-gray-500">Buttery Soft</p>
                                            </div>
                                        </div>
                                        <div>
                                            <h4 class="tw-m-0 tw-font-body tw-text-base tw-font-semibold">Cookability</h4>
                                            <div class="tw-mt-3">
                                                <x-theme::product-score :score="$standard_settings?->cookability_score ?? null"/>
                                            </div>
                                            <div class="tw-mt-2 tw-flex tw-items-center tw-justify-between">
                                                <p class="tw-m-0 tw-text-sm tw-text-gray-500">More Challenging</p>
                                                <p class="tw-m-0 tw-text-sm tw-text-gray-500">Pretty Simple</p>
                                            </div>
                                        </div>
                                        <div>
                                            <h4 class="tw-m-0 tw-font-body tw-text-base tw-font-semibold">Cost</h4>
                                            <div class="tw-mt-3">
                                                <x-theme::product-score :score="$standard_settings?->cost_score ?? null"/>
                                            </div>
                                            <div class="tw-mt-2 tw-flex tw-items-center tw-justify-between">
                                                <p class="tw-m-0 tw-text-sm tw-text-gray-500">Budget-friendly</p>
                                                <p class="tw-m-0 tw-text-sm tw-text-gray-500">Premium</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
                <!-- Start Farming Practices -->
                <div @if($standard_settings?->featured_image_path ?? false) class="tw-pt-12 sm:tw-pt-20" @endif>
                    <div class="tw-mx-auto tw-max-w-7xl tw-px-6 lg:tw-px-8">
                        <div class="tw-mx-auto tw-max-w-2xl lg:tw-mx-0 lg:tw-max-w-none">
                            <h3 class="tw-m-0 tw-text-balance tw-font-display tw-text-gray-900">Farming Practices</h3>
                        </div>
                        @php
                            $farming_practices = $standard_settings?->farming_practices ?? null;
                        @endphp
                        @include('theme::store.products.partials.farming-practices')
                    </div>
                </div>
                <!-- End Farming Practices -->
            </div>
            <!-- End What's Inside -->

            <!-- Start Reviews -->
            <div id="reviews" class="tw-bg-white tw-py-16 sm:tw-py-24">
                <div class="tw-mx-auto tw-max-w-7xl tw-px-6 lg:tw-px-8">
                    <div class="tw-mx-auto tw-max-w-2xl lg:tw-mx-0 lg:tw-max-w-none">
                        <h3 class="tw-m-0 tw-text-balance tw-font-display tw-text-gray-900">Real Customer Reviews</h3>
                    </div>
                    <div class="tw-mx-auto tw-mt-2 tw-max-w-2xl lg:tw-mx-0 lg:tw-max-w-none">
                        <x-theme::customer-reviews/>
                        <div class="tw-mt-4 embedsocial-hashtag" data-ref="4f23e66c4387c829e1a917dd3fc9a33fc6b25329"></div>
                        <script> (function(d, s, id) {
                                var js;
                                if (d.getElementById(id)) {
                                    return;
                                }
                                js = d.createElement(s);
                                js.id = id;
                                js.src = 'https://embedsocial.com/cdn/ht.js';
                                d.getElementsByTagName('head')[0].appendChild(js);
                            }(document, 'script', 'EmbedSocialHashtagScript')); </script>

                    </div>
                </div>
            </div>
            <!-- End Reviews -->

            @if($related_products->isNotEmpty())
                <!-- Start Related Products -->
                <div class="tw-bg-theme-brand-color/25">
                    <div class="tw-mx-auto tw-max-w-2xl tw-px-4 tw-py-16 sm:tw-px-6 sm:tw-py-24 lg:tw-max-w-7xl lg:tw-px-8">
                        <div class="tw-mx-auto tw-max-w-2xl lg:tw-mx-0 lg:tw-max-w-none">
                            <h3 class="tw-m-0 tw-text-balance tw-font-display tw-text-gray-900">You May Also Like</h3>
                        </div>
                        <div class="tw-mt-6 tw-px-6 lg:tw-hidden">
                            <div id="related-products-swiper" class="swiper tw-relative tw-isolate tw-w-full tw-max-w-lg tw-pr-4 lg:tw-pr-0 ">
                                <div class="swiper-wrapper">
                                    @foreach($related_products as $index => $related_product)
                                        @php /** @var \App\Models\Product $related_product */ @endphp
                                        <x-theme::product-card
                                                :product="$related_product"
                                                :order="$order"
                                                class="swiper-slide"
                                                :list_details="[
                                                    'item_category' => null,
                                                    'item_category2' => null,
                                                    'item_list_id' => 'standard_related_products_'. $product->id,
                                                    'item_list_name' => 'Standard Related Products',
                                                ]"
                                                :index="$index"
                                        />
                                    @endforeach
                                </div>
                                <div id="related-products-swiper-button-next" class="tw-absolute tw-inset-y-1/2 tw--mt-8 tw--right-3 tw-z-10 tw-bg-white tw-h-10 tw-w-10 tw-rounded-full tw-border tw-border-theme-brand-color tw-shadow-md tw-flex tw-items-center tw-justify-center tw-text-theme-brand-color hover:tw-bg-gray-100 lg:tw--right-6">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="tw-size-5">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5"/>
                                    </svg>
                                </div>
                                <div id="related-products-swiper-button-prev" class="tw-absolute tw-inset-y-1/2 tw--mt-8 tw--left-3 tw-z-10 tw-bg-white tw-h-10 tw-w-10 tw-rounded-full tw-border tw-border-theme-brand-color tw-shadow-md tw-flex tw-items-center tw-justify-center tw-text-theme-brand-color hover:tw-bg-gray-100 lg:tw--left-6">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="tw-size-5">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5"/>
                                    </svg>
                                </div>
                                <div id="related-products-swiper-pagination" class="swiper-pagination"></div>
                            </div>
                        </div>


                        <div class="tw-hidden tw-mt-6 lg:tw-grid {{ $related_product_columns }} tw-gap-y-4 sm:tw-gap-x-6 sm:tw-gap-y-10 lg:tw-gap-x-8">
                            @foreach($related_products as $index => $related_product)
                                @php /** @var \App\Models\Product $related_product */ @endphp
                                <x-theme::product-card
                                        :product="$related_product"
                                        :order="$order"
                                        :list_details="[
                                            'item_category' => null,
                                            'item_category2' => null,
                                            'item_list_id' => 'standard_related_products_'. $product->id,
                                            'item_list_name' => 'Standard Related Products',
                                        ]"
                                        :index="$index"
                                />
                            @endforeach
                        </div>
                    </div>
                </div>
                <!-- End Related Products -->
            @endif

            @if ($related_recipes->isNotEmpty())
                <!-- Start Recipes -->
                <div class="tw-bg-white tw-py-16 sm:tw-py-24">
                    <div class="tw-mx-auto tw-max-w-7xl tw-px-6 lg:tw-px-8">
                        <div class="tw-mx-auto tw-max-w-2xl lg:tw-mx-0 lg:tw-max-w-none">
                            <h3 class="tw-m-0 tw-text-balance tw-font-display tw-text-gray-900">Our Favorite Recipes</h3>
                        </div>
                        <div class="tw-mx-auto tw-mt-6 tw-grid tw-max-w-2xl tw-grid-cols-1 tw-gap-x-8 tw-gap-y-12 lg:tw-mx-0 lg:tw-max-w-none lg:tw-grid-cols-2">
                            @foreach($related_recipes as $recipe)
                                @php /** @var \App\Models\Recipe $recipe */ @endphp
                                <article class="tw-flex tw-flex-col tw-items-start tw-justify-between">
                                    <div class="tw-relative tw-w-full">
                                        <div class="tw-aspect-w-16 tw-aspect-h-9 sm:tw-aspect-w-2 sm:tw-aspect-h-1 lg:tw-aspect-w-3 lg:tw-aspect-h-2">
                                            <img src="{{ \App\Models\Media::s3ToCloudfront($recipe->cover_photo) }}" alt="{{ $recipe->title }}" class="tw-w-full tw-h-full tw-rounded-2xl tw-bg-gray-100 tw-object-cover">
                                        </div>
                                        <div class="tw-absolute tw-inset-0 tw-rounded-2xl tw-ring-1 tw-ring-inset tw-ring-gray-900/10"></div>
                                    </div>
                                    <div class="tw-flex-1 tw-max-w-xl">
                                        <div class="tw-mt-8 tw-group tw-relative">
                                            <h3 class="tw-m-0 tw-mt-3 tw-text-lg/6 tw-text-gray-900">
                                                <a href="{{ route('recipes.show', [$recipe->slug]) }}" class="tw-text-gray-900 hover:tw-text-gray-600 tw-no-underline">
                                                    <span class="tw-absolute tw-inset-0"></span>
                                                    {{ $recipe->title }}
                                                </a>
                                            </h3>
                                            <p class="tw-m-0 tw-mt-5 tw-line-clamp-3 tw-text-sm/6 tw-text-gray-600">
                                                {{ strip_tags($recipe->description) }}
                                            </p>
                                        </div>
                                    </div>
                                    <a href="{{ route('recipes.show', [$recipe->slug]) }}" class="tw-mt-4 tw-text-base tw-text-theme-link-color hover:tw-text-theme-link-color/90 tw-no-underline ">
                                        Read more
                                    </a>
                                </article>
                            @endforeach
                        </div>
                        <div class="tw-mt-12 tw-mx-auto tw-w-full tw-text-center lg:tw-max-w-none lg:tw-mt-12">
                            <a href="{{ url('/recipes') }}" class="tw-inline-block tw-no-underline tw-py-2 tw-px-3 tw-rounded-xl tw-text-sm tw-bg-theme-brand-color tw-font-semibold tw-text-white hover:tw-bg-theme-brand-color/70 ">
                                View All Recipes &rarr;
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End Recipes -->
            @endif
        </section>

    </div>
</div>
