@php
    /** @var \App\Models\Product $product */
	/** @var \App\Models\Order|null $order */
    /** @var \App\Contracts\Cartable $cart */
    /** @var Illuminate\Support\Collection $products */
    /** @var bool $has_subscription */

 	$is_brand_style = str(theme('store_button_style', 'btn-brand'))->contains('brand');

     // Spelling out all classes here so that they are picked up by the tailwind compiler
	$callout_border_class = ! $is_brand_style ? 'tw-border-theme-brand-color/70' : 'tw-border-theme-action-color/70';
	$background_class = ! $is_brand_style ? 'tw-bg-theme-brand-color' : 'tw-bg-theme-action-color';
	$light_background_class = ! $is_brand_style ? 'tw-bg-theme-brand-color/5' : 'tw-bg-theme-action-color/5';
	$text_class = ! $is_brand_style ? 'tw-text-theme-brand-color' : 'tw-text-theme-action-color';
@endphp

<div class="tw-reset">
    <div class="tw-mx-auto tw-max-w-6xl tw-bg-white tw-py-3 tw-px-4 sm:tw-px-6 lg:tw-px-8">
        @if(!is_null($product->category))
            <x-theme::breadcrumbs
                    :category="$product->category?->parentCategory ? $product->category->parentCategory : $product->category"
                    :subcategory="$product->category?->parentCategory ? $product->category : null"
                    :product="$product"
            />
        @else
            <div class="tw-py-3">
                @php
                    $previous_url = previousProductUrl();
                    if (request()->url() === $previous_url) {
                        $previous_url = route('store.index');
                    }
                @endphp
                <a href="{{ $previous_url }}"><i class="fa fa-chevron-left"></i> {{ __('messages.cart.keep_shopping') }}</a>
            </div>
        @endif
    </div>


    <section
            class="tw-mx-auto tw-bg-white tw-w-full tw-max-w-6xl tw-px-4 sm:tw-px-6 lg:tw-px-8 productPage"
            itemscope itemtype="https://schema.org/Product"
            itemid="{{ url('/store/product/'.$product->slug) }}"
            id="product_{{ $product->slug }}"
    >
        <div class="lg:tw-grid lg:tw-grid-cols-2 lg:tw-items-start lg:tw-gap-x-8 productPage__productDetailsRow">
            <!-- Start Product Photo -->

            <div class="tw-relative productPage__photo">

                @if($product->calloutMessage())
                    <div class="tw-absolute tw--rotate-3 tw-z-10 tw-top-0 tw-left-0 tw--mt-1 tw--ml-2 tw-border tw-rounded-md {{ $callout_border_class }} tw-bg-white tw-shadow-lg productListing__calloutTag @if($product->isOnSale()) productListing__saleCalloutTag @else productListing__standardCalloutTag @endif">
                        <div class="tw-pl-4 tw-pr-4 tw-py-1 tw-bg-white tw-rounded-md">
                            <div class="tw-flex tw-items-center">
                                <p class="tw-m-0 tw-whitespace-nowrap tw-text-base {{ $text_class }}">{{ $product->calloutMessage() }}</p>
                            </div>
                        </div>

                    </div>
                @endif

                <div class="tw-aspect-h-3 tw-aspect-w-4 tw-w-full">
                    @if($product->mainPhoto)
                        <img src="{{ \App\Models\Media::s3ToCloudfront($product->mainPhoto->path) }}" alt="{{ $product->title }}" itemprop="image" class="tw-h-full tw-w-full tw-object-cover tw-object-center tw-rounded-lg"/>
                    @else
                        <div class="tw-h-full tw-w-full">
                            <img src="{{ theme('logo_src') }}" alt="{{ $product->title }}"
                                 class="tw-absolute tw-top-1/2 tw-left-1/2 tw-transform tw--translate-x-1/2 tw--translate-y-1/2 tw-w-1/2 ">
                            <div class="tw-h-full tw-w-full tw-object-cover tw-object-center tw-rounded tw-bg-gray-900 tw-opacity-10 group-hover:tw-opacity-30"></div>
                        </div>
                    @endif

                </div>
            </div>
            <!-- End Product Photo -->

            <!-- Start Product Details Section -->
            <div class="tw-mt-4 tw-px-4 lg:tw-mt-0 productPage__details">
                <div>
                    <h1 class="tw-m-0 tw-text-2xl tw-font-body tw-font-semibold tw-tracking-tight tw-text-gray-900 sm:tw-text-3xl productPage__heading " itemprop="name">
                        {{ $product->title }}
                    </h1>

                    @if ( ! empty($product->unit_description))
                        <div class="tw-mt-1 tw-text-sm tw-text-gray-500 sm:tw-text-base productPage__unitDescription">
                            {!! $product->unit_description !!}
                            <meta itemprop="sku" content="{{ $product->sku }}"/>
                        </div>
                    @endif

                    @if ( ! empty($product->vendor_id))
                        <div itemprop="brand" itemscope itemtype="http://schema.org/Brand" class="tw-mt-2 tw-text-sm tw-text-theme-link-color productPage__vendorLink ">
                            {!! $product->present()->vendorLink() !!}
                        </div>
                    @endif
                </div>


                <div class="tw-pb-2">
                    <div
                            itemprop="offers"
                            itemscope
                            itemtype="https://schema.org/Offer"
                    >
                        <!-- Start Price Section -->
                        <div class="tw-mt-4 productListing__price">
                            @include('theme::store.products.price')
                        </div>
                        <!-- End Price Section -->

                        <!-- Start CTA Section -->
                        <div class="tw-mb-8 productListing__addToCart">
                            <div class="tw-mt-2">
                                <x-theme::form.add-product-counter-button
                                        :product="$product"
                                        :order="$openOrder"
                                        :has_subscription="$has_subscription"
                                        :cta_label="$cta_label ?? null"
                                        :cta_action="$cta_action ?? null"
                                        cta_classes="tw-px-8 tw-py-3 tw-text-base tw-font-medium tw-rounded-lg tw-font-semibold tw-bg-theme-brand-color tw-text-white"
                                        variant_layout="expanded"
                                        :metadata="[
                                            'item_category' => $product->category?->parentCategory?->name ?? $product->category?->name,
                                            'item_category2' => ! is_null($product->category?->parentCategory) ? $product->category?->name : null,
                                            'item_list_id' => 'standard_' . $product->slug . '_page',
                                            'item_list_name' => 'Standard ' . $product->title . ' Page',
                                        ]"
                                />

                            </div>
                        </div>
                        <!-- End CTA Section -->

                        <!-- Start Summary Section -->
                        @if( ! empty($product->summary))
                            <div class="tw-mt-8">
                                {{--                            <h2 class="tw-m-0 tw-font-body tw-text-sm tw-font-medium tw-text-gray-900">Summary</h2>--}}

                                <div class="tw-prose tw-prose-sm tw-text-gray-600 productPage__summary" style="font-size: {{ theme('paragraph_font_size', '16px') }};">
                                    {!! $product->summary !!}
                                </div>
                            </div>
                        @endif
                        <!-- End Summary Section -->

                    </div>
                </div>
            </div>
            <!-- End Product Details Section -->
        </div>
        <!-- Start Product Detail Row-->

        <!-- Start Product Detail Description -->
        <div class="tw-mt-8 productPage__productDescription">
            <div class="tw-max-w-3xl tw-mx-auto tab-content">
                <!-- Start Product Description -->
                <div role="tabpanel" class="tab-pane active productPage__productDescriptionTab" id="productDescriptionTab">
                    <div class="productPage__descriptionText" itemprop="description" @if($product->protocols->isEmpty() && empty($product->ingredients)) style="width: 100%" @endif>
                        <div class="tw-prose tw-max-w-3xl" style="font-size: {{ theme('paragraph_font_size', '16px') }};">
                            {!! $product->description !!}
                        </div>
                    </div>
                    @if($product->protocols->isNotEmpty() || $product->ingredients)
                        <div class="productPage__descriptionSidebar">
                            @if(!$product->protocols->isEmpty())
                                <section class="productPage__protocols">
                                    <h2 class="productPage__protocolsHeading h4">Protocols</h2>
                                    <ul class="productPage__protocolsList">
                                        @foreach($product->protocols as $protocol)
                                            <li class="productPage__protocolsListItem">
                                                <a href="/protocols#{{ $protocol->slug }}">{{ $protocol->title }}</a>
                                            </li>
                                        @endforeach
                                    </ul>
                                </section>
                            @endif
                            @if($product->ingredients)
                                <section class="productPage__productIngredients">
                                    <h2 class="productPage__ingredientsHeading h4">Ingredients</h2>
                                    <div class="tw-reset">
                                        <div class="tw-prose" style="font-size: {{ theme('paragraph_font_size', '16px') }};">
                                            {!! $product->ingredients !!}
                                        </div>
                                    </div>
                                </section>
                            @endif
                        </div>
                    @endif
                </div>
                <!-- End Product Description -->
            </div>

        </div>
        <!-- End Product Detail Description -->
    </section>
</div>
