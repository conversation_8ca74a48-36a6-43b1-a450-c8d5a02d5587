@php
    /**
     * @var \App\Models\Order|null $openOrder
     * @var \App\Contracts\Cartable $cart
     * @var \App\Models\Category $category
     * @var \App\Models\Category $active_subcategory
     */

    $category_menu = categoryMenu();
    $subcategories = $category->subcategories()
        ->whereHas('products', fn($q) =>
            $q->where('visible', true)->whereNot('type_id', \App\Support\Enums\ProductType::PREORDER->value)
        )
        ->get();
    $has_subcategories = $subcategories->isNotEmpty();
@endphp

@extends('theme::_layouts.main', [
	'pageTitle' => $category->extra_attributes->seo_meta_title ?? $category->name,
	'pageDescription' => $category->extra_attributes->seo_meta_description
        ?? setting('store_page_description'),
	'pageCanonical' => $is_subcategory
	    ? route('store.subcategories.show', [$category->parentCategory->slug, $category->slug])
	    : route('store.categories.show', [$category->slug]),
	'robots' => $category->robots(),
])

@section('pageMetaTags')
    <style>
        ::-webkit-scrollbar {
            height: 8px; /* height of horizontal scrollbar */
        }
    </style>
@stop

@section('content')
    <section class="tw-reset">
        <div class="tw-sticky tw-z-[11] tw-top-[100px] tw-w-full md:tw-top-[56px]">
            <div class="tw-bg-white tw-border-b tw-border-gray-200 tw-shadow-sm">
                <div class="tw-mx-auto tw-max-w-7xl tw-py-2 tw-px-4 sm:tw-px-6 lg:tw-px-8">
                    <x-theme::breadcrumbs
                            :category="$is_subcategory ? $category->parentCategory : $category"
                            :subcategory="$is_subcategory ? $category : null"
                    />
                </div>
            </div>
        </div>


        <div class="">
            <div class="tw-max-w-7xl sm:tw-text-center sm:tw-mx-auto">
                <div class="tw-pt-8 tw-pb-4 tw-px-4 sm:tw-pt-16 sm:tw-pb-8 sm:tw-px-6 lg:tw-px-8">
                    <h1 class="tw-text-4xl tw-font-bold tw-text-gray-900">
                        {{ $category->extra_attributes->display_name ?? $category->name }}
                    </h1>
                    <div class="tw-mt-4 tw-max-w-xl tw-mx-auto tw-text-base tw-text-gray-500 tw-prose ">
                        {!! $category->extra_attributes->summary !!}
                    </div>
                </div>
            </div>

            @if($has_subcategories)
                <div class="tw-pt-4 tw-mx-auto tw-max-w-7xl tw-bg-white tw-space-y-8">
                    @foreach($subcategories as $index => $subcategory)
                        <livewire:theme.category-product-list
                                :category="$subcategory"
                                :lazy="$index > 1"
                                :type="$category->slug === 'value-bundles' ? 'bundle' : 'standard' "
                                :show_about="true"
                        />
                    @endforeach
                </div>
            @else

                <section class="tw-pt-4 tw-bg-white tw-mx-auto tw-max-w-7xl tw-px-4 sm:tw-px-6 lg:tw-px-8">
                    <!-- End Category Banner -->

                    <div class="tw-pt-12 tw-pb-8 tw-grid tw-grid-cols-2 tw-gap-y-12 lg:tw-gap-x-6 lg:tw-grid-cols-5 productListing--grid">
                        @include('theme::store.grid.product-list', [
                            'products' => $products,
                            'category' => $category,
                            'collection' => null,
                            'order' => $openOrder,
                            'cart' => $cart,
                            'has_subscription' => $has_subscription,
                        ])
                    </div>

                    @if($products->isEmpty())
                        @if(request('q'))
                            <div class="tw-py-8 tw-mx-auto tw-max-w-lg">
                                <div class="tw-text-center">
                                    <svg class="tw-mx-auto tw-h-12 tw-w-12 tw-text-gray-400"
                                         xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                         stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                              d="M21 7.5l-2.25-1.313M21 7.5v2.25m0-2.25l-2.25 1.313M3 7.5l2.25-1.313M3 7.5l2.25 1.313M3 7.5v2.25m9 3l2.25-1.313M12 12.75l-2.25-1.313M12 12.75V15m0 6.75l2.25-1.313M12 21.75V19.5m0 2.25l-2.25-1.313m0-16.875L12 2.25l2.25 1.313M21 14.25v2.25l-2.25 1.313m-13.5 0L3 16.5v-2.25"/>
                                    </svg>

                                    <h2 class="tw-mt-2 tw-text-base tw-font-semibold tw-leading-6 tw-text-gray-900">
                                        No products found</h2>
                                    <p class="tw-mt-1 tw-text-sm tw-text-gray-500">Your search for
                                        <span
                                                class="tw-font-bold">{{ request('q') }}</span>
                                        did not match any
                                        products. Try searching for something else.
                                    </p>
                                </div>
                                <form action="{{ route('store.index') }}" method="GET" class="tw-mt-6 tw-flex">
                                    <label for="q" class="tw-sr-only">Search term</label>
                                    <input type="text" name="q" placeholder="{{ __('Search store') }}..."
                                           value="{{ request('q') }}" id="q"
                                           class="tw-block tw-w-full tw-rounded-md tw-border-0 tw-py-1.5 tw-text-gray-900 tw-shadow-sm tw-ring-1 tw-ring-inset tw-ring-gray-300 placeholder:tw-text-gray-400 focus:tw-ring-2 focus:tw-ring-inset focus:tw-ring-theme-action-color sm:tw-text-sm sm:tw-leading-6">
                                    <button type="submit"
                                            class="tw-ml-4 tw-flex-shrink-0 tw-rounded-md tw-bg-theme-action-color tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color/70 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color">
                                        Search
                                    </button>
                                </form>
                            </div>
                        @else
                            <div class="tw-py-8 tw-mx-auto tw-max-w-lg">
                                <div class="tw-text-center">
                                    <svg class="tw-mx-auto tw-h-12 tw-w-12 tw-text-gray-400"
                                         xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                         stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                              d="M21 7.5l-2.25-1.313M21 7.5v2.25m0-2.25l-2.25 1.313M3 7.5l2.25-1.313M3 7.5l2.25 1.313M3 7.5v2.25m9 3l2.25-1.313M12 12.75l-2.25-1.313M12 12.75V15m0 6.75l2.25-1.313M12 21.75V19.5m0 2.25l-2.25-1.313m0-16.875L12 2.25l2.25 1.313M21 14.25v2.25l-2.25 1.313m-13.5 0L3 16.5v-2.25"/>
                                    </svg>

                                    <h2 class="tw-m-0 tw-mt-2 tw-text-base tw-font-semibold tw-leading-6 tw-text-gray-900">
                                        No products</h2>
                                    <p class="tw-m-0 tw-mt-1 tw-text-sm tw-text-gray-500">There are no matching products.</p>
                                </div>
                                <a href="{{ route('store.index') }}"
                                   class="tw-flex tw-justify-center tw-mt-6 tw-no-underline">
                                    <button type="button"
                                            class="tw-rounded-md tw-bg-theme-action-color tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color/70 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color">
                                        Go to storefront
                                    </button>
                                </a>
                            </div>
                        @endif
                    @else
                        <div class="storeIndex__pagination">
                            {!! $products->appends(['q' => request()->get('q'), 'tag' => request()->get('tag'), 'sale' => request()->get('sale')])->render() !!}
                        </div>
                    @endif
                </section>
            @endif
            @if($category->description)
                <section class="tw-bg-gray-100">
                    <div class="tw-mt-6 tw-mx-auto tw-max-w-7xl tw-px-4 sm:tw-mt-8  sm:tw-px-6 lg:tw-px-8">
                        <div class="tw-py-16 sm:tw-py-24">
                            <div class="tw-mx-auto tw-grid tw-max-w-2xl tw-grid-cols-1 tw-items-start tw-gap-x-8 tw-gap-y-16 sm:tw-gap-y-24 lg:tw-mx-0 lg:tw-max-w-none lg:tw-grid-cols-2">
                                <div class="lg:tw-pr-4">
                                    <div class="tw-relative tw-overflow-hidden tw-rounded-3xl tw-bg-gray-900 tw-px-6 tw-pb-9 tw-pt-64 tw-shadow-2xl sm:tw-px-12 lg:tw-max-w-lg lg:tw-px-8 lg:tw-pb-8 xl:tw-px-10 xl:tw-pb-10">
                                        <img class="tw-absolute tw-inset-0 tw-h-full tw-w-full tw-object-cover tw-brightness-125 tw-saturate-50"
                                             src="{{ \App\Models\Media::s3ToCloudfront('https://s3.amazonaws.com/sevensonsfarms/sevensonsfarms/images/1727886955_66fd766b4ea9b.webp') }}"
                                             alt="Seven Sons Farm">
                                        <div class="tw-absolute tw-inset-0 tw-bg-gray-300 tw-mix-blend-multiply"></div>
                                        <div class="tw-absolute tw-left-1/2 tw-top-1/2 tw--ml-16 tw--translate-x-1/2 tw--translate-y-1/2 tw-transform-gpu tw-blur-3xl"
                                             aria-hidden="true">
                                            <div class="tw-aspect-[1097/845] tw-w-[68.5625rem] tw-bg-gradient-to-tr tw-from-theme-brand-color tw-to-theme-brand-color/70 tw-opacity-40"
                                                 style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"></div>
                                        </div>
                                        <figure class="tw-relative tw-isolate">
                                            <img src="{{ \App\Models\Media::s3ToCloudfront('https://s3.amazonaws.com/sevensonsfarms/sevensonsfarms/images/1727886954_66fd766aebd46.webp') }}"
                                                 alt="Subscribe & Save logo"
                                                 class="tw-w-auto tw--ml-5 tw-h-24 lg:tw--ml-6 ">
                                            <blockquote class="tw-mt-6 tw-italic tw-text-xl tw-font-semibold tw-leading-8 tw-text-white">
                                                <p class="tw-m-0">Save 5% on every order</p>
                                                <p class="tw-m-0">Free item with each order</p>
                                                <p class="tw-m-0">100% customizable</p>

                                            </blockquote>
                                        </figure>
                                    </div>
                                </div>
                                <div>
                                    <div class="tw-text-base tw-leading-7 tw-text-gray-700 lg:tw-max-w-lg">
                                        <p class="tw-text-base tw-font-semibold tw-leading-7 tw-text-theme-brand-color">
                                            Our goal</p>
                                        <h3 class="tw-mt-2 tw-text-3xl tw-font-bold tw-tracking-tight tw-text-gray-900 sm:tw-text-4xl">
                                            To provide the peace of mind that comes from having a direct relationship
                                            with a farmer
                                        </h3>
                                        <div class="tw-max-w-xl">
                                            <div class="tw-mt-6 tw-prose">
                                                {!! $category->description !!}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            @endif
        </div>
    </section>
@endsection

@push('scripts')
    <script>
        if (typeof fbq !== 'undefined') {
            fbq('track', 'ViewContent', {
                content_name: 'Store',
                content_category: '{{ request()->segment(2, 'storefront') }}'
            });
        }
    </script>
@endpush
