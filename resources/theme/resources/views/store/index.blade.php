@extends('theme::_layouts.main', [
	'pageTitle' => $pageTitle ?? 'Store',
	'pageDescription' => isset($pageDescription) ? $pageDescription : '',
	'pageCanonical' => $pageCanonical ?? null,
	'robots' => $robots ?? null,
])

@php
    /**
     * @var \App\Models\Order|null $openOrder
     * @var \App\Contracts\Cartable $cart
     */
@endphp

@section('pageMetaTags')
    {!! $headTags ?? null !!}
@stop

@section('content')

    @php
        $view = config('grazecart.new_storefront_enabled')
            ? 'theme::store._layouts.new-no-menu'
            : 'theme::store._layouts.no-menu';
    @endphp

    @include($view, [
        'html' => $html ?? null,
        'order' => $openOrder,
        'cart' => $cart
    ])

    @if(auth()->check())
        <ul class="cartMenu">
            @if(auth()->user()->hasRecurringOrder())
                <li class="tw-reset" id="cartContainer">
                    <a @click="$dispatch('openPanel', { title: 'Subscription', component: 'theme.subscription-side-panel' });" class="tw-cursor-pointer hover:tw-no-underline">
                        <i class="fa fa-shopping-basket" style="margin-right: 0.25rem;"></i>
                        <livewire:theme.subscription-item-count/>
                    </a>
                </li>
                <li>
                    <a @click="$dispatch('openPanel', { title: 'Subscription', component: 'theme.subscription-side-panel' });" class="btn btn-brand btn-sm">
                        Edit Subscription <i class="fa fa-chevron-right hidden-xs"></i>
                    </a>
                </li>
            @elseif( ! is_null($openOrder))
                <li class="tw-reset" id="cartContainer">
                    <a @click="$dispatch('openPanel', { title: 'Order {{ $openOrder->id }}', component: 'theme.order-side-panel' });" class="tw-cursor-pointer hover:tw-no-underline">
                        <i class="fa fa-shopping-basket" style="margin-right: 0.25rem;"></i>
                        <livewire:theme.order-item-count/>
                        <small>(edit)</small>
                    </a>
                </li>
            @else
                <li class="tw-reset" id="cartContainer">
                    <a @click="$dispatch('openPanel', { title: 'Shopping cart', component: 'theme.cart-side-panel' });" class="tw-cursor-pointer hover:tw-no-underline">
                        <i class="fa fa-shopping-basket" style="margin-right: 0.25rem;"></i>
                        <livewire:theme.cart-item-count/>
                    </a>
                </li>
                <li>
                    <a href="/checkout" class="btn btn-action btn-sm">
                        @lang('Checkout') <i class="fa fa-chevron-right hidden-xs"></i>
                    </a>
                </li>
            @endif
        </ul>
    @else
        <ul class="cartMenu">
            <li id="cartContainer">
                <a href="/login">Sign-in</a>
            </li>
            <li>
                <a href="/register" class="btn btn-action btn-sm">Create Account</a>
            </li>
        </ul>
    @endif
@endsection

@push('scripts')
    {!! $bodyTags ?? null !!}
    <script>
        if (typeof fbq !== 'undefined') {
            fbq('track', 'ViewContent', {
                content_name: 'Store',
                content_category: '{{ request()->segment(2, 'storefront') }}'
            });
        }
    </script>
@endpush
