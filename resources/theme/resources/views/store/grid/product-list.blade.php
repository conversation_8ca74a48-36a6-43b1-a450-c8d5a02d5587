@php
    /** @var \App\Models\Order|null $order */
    /** @var \App\Contracts\Cartable $cart */
    /** @var Illuminate\Support\Collection $products */
    /** @var \App\Models\Category|null $category */
    /** @var \App\Models\Collection|null $collection */
    /** @var bool $has_subscription */

     $is_brand_style = str(theme('store_button_style', 'btn-brand'))->contains('brand');

     // Spelling out all classes here so that they are picked up by the tailwind compiler
    $callout_border_class = ! $is_brand_style ? 'tw-border-theme-brand-color/70' : 'tw-border-theme-action-color/70';
    $background_class = ! $is_brand_style ? 'tw-bg-theme-brand-color' : 'tw-bg-theme-action-color';
    $light_background_class = ! $is_brand_style ? 'tw-bg-theme-brand-color/5' : 'tw-bg-theme-action-color/5';
    $text_class = ! $is_brand_style ? 'tw-text-theme-brand-color' : 'tw-text-theme-action-color';

@endphp

@foreach($products as $index => $product)
    @php
        /** @var \App\Models\Product $product */

    $view = config('grazecart.new_storefront_enabled')
            ? 'theme::store.grid.new-product'
            : 'theme::store.grid.product';

    @endphp

            <!-- Start {{ $product->title }} -->
    @include($view, [
        'product' => $product,
        'category' => $category ?? null,
        'collection' => $collection ?? null,
        'index' => $index,
        'type' => 'retail',
    ])
    <!-- End {{ $product->title }} -->

@endforeach
