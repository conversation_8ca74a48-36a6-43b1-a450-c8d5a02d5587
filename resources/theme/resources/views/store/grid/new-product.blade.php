@php
    /** @var \App\Models\Product $product */
    /** @var \App\Models\Category|null $category */
    /** @var \App\Models\Collection|null $collection */
    /** @var int|null $index */

    $list_details = [
        'item_category' => $category?->parentCategory?->name ?? $category?->name,
        'item_category2' => ! is_null($category?->parentCategory) ? $category?->name : null,
        'item_list_id' => 'category_' . $category?->slug . '_page',
        'item_list_name' => 'Category ' . $category?->name . ' Page',
    ];

    if (!is_null($collection)) {
        $list_details = [
            'item_category' => $collection->title,
            'item_category2' => null,
            'item_list_id' => 'collection_' . $collection->slug . '_page',
            'item_list_name' => 'Collection ' . $collection->title . ' Page',
        ];


    }

    $product_details = [
        'currency' => 'USD',
        'value' => $product->getPrice() / 100,
        'items' => [
            array_merge([
                'item_id' => $product->id,
                'item_name' => $product->title,
                'index' => $index ?? 0,
                'price' => $product->getPrice() / 100,
                'quantity' => 1
            ], $list_details)
        ]
    ];
@endphp

<section
        itemscope itemtype="https://schema.org/Product"
        itemid="{{ url('/store/product/'.$product->slug) }}"
        id="product_{{ $product->slug }}"
        class="tw-flex tw-flex-col"
        x-data="tracksProductViews(@js($product_details))"
        x-intersect.half.once="trackProductView"
>
    <div class="tw-relative ">
        <div class="tw-relative tw-w-full tw-group tw-rounded-t-lg">
            <a href="/store/product/{{ $product->slug }}"
               title="{{ $product->title }}"
               class="productListing__photoLink--grid"
            >
                <div class="tw-relative tw-w-full">
                    <div class="tw-aspect-h-4 tw-aspect-w-4 tw-w-full tw-group">
                        @if($product->mainPhoto)
                            <img src="{{ \App\Models\Media::s3ToCloudfront($product->mainPhoto->path) }}" alt="{{ $product->title }}" itemprop="image"
                                 class="tw-transition-opacity group-hover:tw-opacity-75 tw-rounded-md tw-h-full tw-w-full tw-object-cover tw-object-center">
                        @else
                            <div class="tw-h-full tw-w-full">
                                <img src="{{ \App\Models\Media::s3ToCloudfront(theme('logo_src')) }}" alt="{{ $product->title }}"
                                     class="tw-absolute tw-top-1/2 tw-left-1/2 tw-transform tw--translate-x-1/2 tw--translate-y-1/2 tw-w-1/2 ">
                                <div class="tw-h-full tw-w-full tw-object-cover tw-object-center tw-rounded-md tw-bg-gray-900 tw-opacity-10 group-hover:tw-opacity-30"></div>
                            </div>
                        @endif
                    </div>
                    @if($product->calloutMessage())
                        <div class="tw-absolute tw-p-2 tw-rotate-2 @if($type === 'bundle') tw--top-3 tw--right-5  @else tw--top-3 tw--right-4  @endif">
                            <span class="tw-inline-flex tw-items-center tw-bg-keppel-700 tw-gap-x-1.5 tw-rounded-md tw-px-2 tw-py-1 tw-text-xs tw-font-semibold tw-text-white tw-shadow-md tw-ring-1 tw-ring-inset tw-ring-keppel-700">
                                {{ $product->calloutMessage() }}
                            </span>
                        </div>
                    @endif
                    <div class="tw-hidden tw-absolute tw-bottom-0 tw-w-full tw-p-4 lg:tw-block ">
                        <button type="button"
                                class="tw-relative tw-z-10 tw-w-full tw-rounded-md tw-bg-white tw-transition-opacity tw-bg-opacity-75 tw-px-4 tw-py-2 tw-text-sm tw-text-gray-900 tw-opacity-0 focus:tw-opacity-100 group-hover:tw-opacity-100">
                            View Details
                            <span class="tw-sr-only">, {{ $product->title }}</span>
                        </button>
                    </div>
                </div>

            </a>
        </div>
    </div>

    <div class="tw-relative tw-flex-1">
        <div class="tw-mt-3">
            <p class="tw-m-0 @if($type === 'bundle') tw-text-base @else tw-text-sm @endif tw-leading-6 tw-font-semibold">
                <a href="{{ route('store.show', [$product->slug]) }}" title="{{ $product->title }}" class="tw-text-gray-700 hover:tw-text-gray-700">
                    <span class="tw-absolute tw-inset-0"></span>
                    <span itemprop="name">{{ $product->title }}</span>
                </a>
            </p>
            <div itemprop="description">
                @if ( ! empty($product->unit_description))
                    <p class="tw-m-0 tw-mt-0.5 tw-leading-4 @if($type === 'bundle') tw-text-sm @else tw-text-xs @endif  tw-text-gray-500">{!! $product->unit_description !!}</p>
                @endif
                <meta itemprop="sku" content="{{ $product->sku }}"/>
            </div>
            @if( ! empty($product->vendor_id))
                <div itemprop="brand" itemscope itemtype="http://schema.org/Brand" class="tw-hidden">
                    {!! $product->present()->vendorLink() !!}
                </div>
            @endif
            @if(!empty($product->setting('tagline')))
                <p class="tw-m-0 tw-mt-2 tw-text-xs tw-text-gray-600">
                    {{ $product->setting('tagline') }}
                </p>
            @endif
        </div>

        <a href="/store/product/{{ $product->slug }}"
           title="{{ $product->title }}"
           class="tw-mt-4 tw-block tw-text-sm tw-text-gray-700 lg:tw-hidden"
        >
            <span class="tw-m-0 tw-text-sm tw-font-medium ">View Details
                <span aria-hidden="true">→</span>
            </span>
        </a>
    </div>


    <div class="tw-mt-4 tw-space-y-2">
        @if($product->prices->count() > 1)
            <div class="tw-text-xs tw-inline-block tw-font-semibold tw-p-px tw-bg-gradient-to-r tw-from-buttercup-300 tw-to-chestnut-rose-400 tw-text-gray-900 tw-rounded-md">
                <div class="tw-bg-white tw-px-1.5 tw-py-1 tw-rounded-md tw-inline-flex tw-items-center tw-space-x-1">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="tw-size-4 tw--scale-x-100 tw-text-gray-500 tw-transform-gpu">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 6h.008v.008H6V6Z"/>
                    </svg>
                    <div>
                        Buy {{ $product->prices[1]->quantity }}, Save 5%
                    </div>
                </div>

            </div>
        @endif

        <div class="tw-flex tw-items-baseline tw-space-x-2"
             itemprop="offers"
             itemscope
             itemtype="https://schema.org/Offer"
        >
            @if($product->isOnSale())
                <p class="tw-m-0 tw-font-semibold tw-text-theme-brand-color @if($type === 'bundle') tw-text-base sm:tw-text-lg @else tw-text-sm sm:tw-text-base @endif">
                    &#36;<span itemprop="price">{{ money($product->getPrice()) }}</span>
                </p>
                <p class="tw-m-0 tw-text-gray-500 tw-line-through @if($type === 'bundle') tw-text-sm @else tw-text-xs  @endif">
                    &#36;{{ money($product->getRegularPrice()) }}
                </p>
            @else
                <p class="tw-m-0 tw-font-semibold tw-text-gray-700 @if($type === 'bundle') tw-text-base sm:tw-text-lg @else tw-text-sm sm:tw-text-base @endif">
                    &#36;<span itemprop="price">{{ money($product->getPrice()) }}</span>
                </p>
            @endif
        </div>

        <x-theme::form.add-product-counter-button
                :product="$product"
                :order="$openOrder"
                :has_subscription="$has_subscription"
                :cta_label="$cta_label ?? null"
                :cta_action="$cta_action ?? null"
                cta_classes="tw-mt-2 tw-block tw-w-full tw-px-3 tw-py-2 tw-rounded-lg tw-text-sm tw-font-semibold tw-bg-theme-brand-color tw-text-white"
                :metadata="[
                        'item_category' => $category?->parentCategory?->name ?? $category?->name,
                        'item_category2' => ! is_null($category?->parentCategory) ? $category?->name : null,
                        'item_list_id' => 'bundle_' . $product->slug . '_page',
                        'item_list_name' => 'Bundle ' . $product->title . ' Page',
                    ]"
        />
    </div>

</section>
