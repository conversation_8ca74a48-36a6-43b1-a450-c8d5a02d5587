@props([
    'product',
    'order' => null,
    'has_subscription' => false,
    'cta_label' => null,
    'cta_action' => null,
    'cta_classes' => null,
    'variant_layout' => 'popper',
    'metadata' => [],
    'confirm_delivery_method' => false
])

@php
    /**
     * @var \App\Models\Product $product
     * @var \App\Models\Order|null $order
     */

    $style = theme('store_button_style', 'btn-brand');
@endphp

@if($cta_action === 'show_details')
    <a href="{{ route('store.show', [$product->slug]) }}" class="tw-w-full tw-no-underline">
        <button type="button" class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif">
            {{ $cta_label ?? (!is_null($order) ? $product->orderActionLabel($order->isFromBlueprint()) : $product->cartActionLabel()) }}
        </button>
    </a>
@elseif($product->setting('links_externally'))
    <a href="{{ $product->setting('links_externally:url', url('/store/product/' . $product->slug)) }}" target="_blank" class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif">
        {{ $product->setting('links_externally:text', 'More Info') }}
    </a>
@elseif($product->hasVariants())
    <button type="button" x-data x-on:click="$dispatch('open-modal-product-quickview', { product_id: {{ $product->id }}, metadata: @js($metadata) })" class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif">
        <div class="tw-flex tw-items-center tw-justify-center tw-space-x-1">
            <div>Select Option</div>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="tw-text-gray-100 tw-size-4">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"/>
            </svg>
        </div>
    </button>
@elseif($product->isOutOfStock())
    <button type="button" disabled class=" !tw-cursor-not-allowed !tw-bg-gray-200 !tw-text-gray-700 @if($cta_classes ?? false) {{ $cta_classes }} @endif ">
        Sold Out
    </button>
@else
    <livewire:theme.add-product-counter-button
            wire:key="category-product-counter-{{ $product->slug }}"
            :product="$product"
            :has_subscription="$has_subscription"
            :has_order="!is_null($order)"
            :cta_label="$cta_label ?? null"
            :cta_classes="$cta_classes ?? null"
            :metadata="$metadata ?? []"
    />
@endif

