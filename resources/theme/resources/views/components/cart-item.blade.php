@php use App\Services\SubscriptionSettingsService; @endphp
@props(['item', 'can_be_modified' => false, 'intends_to_subscribe' => false])

@php
    /** @var \App\Cart\Item $item */
@endphp

<div class="tw-flex tw-py-4">
    @if($item->product->mainPhoto?->thumbnail_path)
        <img src="{{ \App\Models\Media::s3ToCloudfront($item->product->mainPhoto?->thumbnail_path) }}" alt="{{ $item->product->title }}"
             class="tw-h-16 tw-w-16 tw-flex-none tw-rounded-lg tw-object-center tw-object-cover tw-border tw-border-gray-200"/>
    @endif
    <div class="tw-ml-4 tw-grid tw-flex-auto tw-grid-cols-1 tw-grid-rows-1 tw-items-start tw-gap-x-5 tw-gap-y-3">
        <div class="tw-row-end-1 tw-flex-auto">
            <h3 class="tw-m-0 tw-font-body tw-text-sm tw-font-medium">
                <a href="/store/product/{{ $item->product->slug }}"
                   class="tw-m-0 tw-text-gray-900 hover:tw-text-gray-500">{{ $item->product->title }}</a>
            </h3>
            @if($item->product->isPricedByWeight() && setting('show_price_per_pound', true))
                <div class="tw-mt-1 tw-flex tw-items-center tw-space-x-2 tw-text-sm tw-text-gray-700">
                    <span>${{ money($item->product->getUnitPrice()) }}/{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.</span>
                    <span class="tw-text-gray-500">Avg. {{ weight($item->product->weight) }}</span>
                </div>
            @else
                <div class=" tw-mt-1 tw-flex tw-items-baseline tw-space-x-2">
                    @php
                        $has_quantity_discounts = $item->priceCount() > 1;
                        $is_discounted = $item->price() !== $item->defaultPrice();
                    @endphp
                    <p class="tw-m-0tw-text-sm @if($is_discounted) tw-text-keppel-500 @else tw-text-gray-500 @endif">&#36;{{ money($item->price()) }}</p>
                    @if($is_discounted)
                        <p class="tw-m-0 tw-text-xs tw-text-gray-500 tw-line-through">&#36;{{ money($item->defaultPrice()) }}</p>
                    @endif
                </div>

            @endif
        </div>
        <p class="tw-m-0 tw-m-1 tw-row-span-2 tw-row-end-2 tw-m-0 tw-text-sm font-medium tw-text-gray-900">
            &#36;{{ money($item->subtotal()) }}
        </p>

        @if($can_be_modified)
            <div class="tw-mt-4 tw-flex tw-items-center tw-space-x-4">
                <span class="tw-isolate tw-inline-flex tw-rounded-md tw-shadow-sm">
                    <button type="button" wire:click="decrementItemQuantity('{{ $item->id }}')"
                            class="tw-relative tw-inline-flex tw-items-center tw-rounded-l-md tw-bg-white tw-px-3 tw-py-2 tw-font-semibold tw-text-gray-900 tw-ring-1 tw-ring-inset tw-ring-gray-300 hover:tw-bg-gray-50 focus:tw-z-10">
                        <span class="tw-sr-only">Decrement</span>
                        <svg class="tw-w-4 tw-h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                             stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 12h-15"/>
                        </svg>
                    </button>
                    <button type="button" disabled
                            class="tw-relative tw-w-12 tw--ml-px tw-inline-flex tw-items-center tw-justify-center tw-bg-white tw-px-3 tw-py-2 tw-font-normal tw-text-gray-900 tw-ring-1 tw-ring-inset tw-ring-gray-300">{{ $item->quantity }}</button>
                    <button type="button" wire:click="incrementItemQuantity('{{ $item->id }}')"
                            class="tw-relative tw--ml-px tw-inline-flex tw-items-center tw-rounded-r-md tw-bg-white tw-px-3 tw-py-2 tw-font-semibold tw-text-gray-900 tw-ring-1 tw-ring-inset tw-ring-gray-300 hover:tw-bg-gray-50 focus:tw-z-10">
                        <span class="tw-sr-only">Increment</span>
                        <svg class="tw-w-4 tw-h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                             stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15"/>
                        </svg>
                    </button>
                </span>

                <div class="tw-flex tw-items-center">
                    <button type="button" wire:click="removeItem('{{ $item->id }}')"
                            class="tw-ml-4 tw-text-sm tw-font-semibold tw-text-gray-500 hover:tw-text-gray-400">
                        <svg class="tw-w-5 tw-h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                             stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"/>
                        </svg>
                    </button>
                </div>
            </div>
        @endif

        @php
            $alerts = collect();

            if ($intends_to_subscribe && app(SubscriptionSettingsService::class)->excludedProductIds()->contains($item->product->id)) {
                $alerts->push('Unavailable for subscription');
            }

            if ($item->product->hasLimitPerCustomer()) {
                $alerts->push("Limit {$item->product->limitPerCustomer()}");
            }

            if ($item->product->hasOrderMinimum()) {
                $minimum = money($item->product->orderMinimum());
                $alerts->push('$'.$minimum.' minimum required');
            }
        @endphp

        @if($alerts->isNotEmpty())
            <div class="tw-flex tw-items-center">
                <p class="tw-m-0 tw-text-sm tw-text-gray-500">{{ $alerts->join(' | ') }}</p>
            </div>
        @endif
    </div>
</div>

