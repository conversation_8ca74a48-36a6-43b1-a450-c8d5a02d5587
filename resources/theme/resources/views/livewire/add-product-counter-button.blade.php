@php /** @var App\Models\Product $product */ @endphp
@php /** @var string $style */ @endphp
@php /** @var bool|null $rounded */ @endphp

<div>
    @if($count > 0)
        <div class="tw-flex tw-items-center tw-justify-between tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-text-sm tw-font-semibold @endif">

            <button
                    type="button"
                    wire:loading.attr="disabled"
                    wire:click="decrement"
                    class="tw-rounded-md tw-bg-transparent hover:tw-bg-gray-900/10 tw-px-3 tw-py-1"
            >
                &minus;
            </button>
            <div>
                <span wire:loading.remove>
                    {{ $count }}
                </span>
                <svg wire:loading style="display: none;" class="tw-animate-spin tw-h-5 tw-w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>

            <button
                    type="button"
                    wire:loading.attr="disabled"
                    wire:click="add"
                    class="tw-rounded-md tw-bg-transparent hover:tw-bg-gray-900/10 tw-px-3 tw-py-1"
            >
                &plus;
            </button>
        </div>
    @else
        <button
                type="button"
                wire:loading.attr="disabled"
                wire:click="add"
                class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-text-sm tw-font-semibold @endif"
        >
            <span wire:loading.remove>
                @if($has_subscription)
                    {{ $cta_label ?? $product->orderActionLabel(true) }}
                @elseif($has_order)
                    {{ $cta_label ??  $product->orderActionLabel() }}
                @else
                    {{ $cta_label ?? $product->cartActionLabel() }}
                @endif
            </span>
            <span wire:loading style="display:none;" class="tw-text-base">
                <svg class="tw-animate-spin tw-h-5 tw-w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </span>
        </button>
    @endif
</div>
