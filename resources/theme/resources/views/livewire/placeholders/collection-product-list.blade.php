<div x-data="scrollableProductList" class="tw-bg-white">
    <div class="tw-py-16 sm:tw-py-24 lg:tw-mx-auto lg:tw-max-w-7xl lg:tw-px-8">
        <div class="tw-flex tw-items-center tw-justify-between tw-px-4 sm:tw-px-6 lg:tw-px-0">
            <div class="tw-flex tw-place-items-baseline tw-space-x-3">
                <h2 class="tw-text-2xl tw-font-normal tw-text-gray-900">{{ $collection->title }}</h2>
                <div>
                    <svg class="tw-animate-spin tw--ml-1 tw-mr-3 tw-h-5 tw-w-5 tw-text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="tw-relative tw-mt-8">
            <div x-ref="container" class="tw-relative tw--mb-6 tw-w-full tw-overflow-x-auto tw-pb-6">
                <ul role="list" x-ref="list" class="tw-mx-4 tw-inline-flex tw-scroll-smooth @if($type === 'bundle') tw-space-x-8 @else tw-space-x-6 @endif sm:tw-mx-6 lg:tw-mx-0">
                    @foreach(range(1, 10) as $index)
                        <li class="collection-product tw-inline-flex @if($type === 'bundle') tw-w-64 @else tw-w-48 @endif tw-flex-col tw-text-center">
                            <div class="tw-aspect-h-1 tw-aspect-w-1 tw-w-full tw-rounded-md tw-bg-gray-100 tw-object-cover group-hover:tw-opacity-75">
                                <div class="tw-w-full tw-flex tw-items-center tw-justify-center">
                                    <svg class="tw-animate-spin tw--ml-1 tw-mr-3 tw-h-8 tw-w-8 tw-text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </div>
                            </div>
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>
    </div>
</div>
