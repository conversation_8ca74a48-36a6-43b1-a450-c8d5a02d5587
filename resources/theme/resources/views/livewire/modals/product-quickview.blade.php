<x-theme::modal class="tw-w-full">
    @php
        /** @var \App\Models\Product|null $product */
        $store_service = app(\App\Services\StoreService::class);
        $delivery_method = $store_service->deliveryMethod(request()->cookie('shopping_delivery_method_id'));
    @endphp
    @if($open)
        <form wire:submit.prevent="add"
              x-trap="open"
              class="tw-relative tw-transform tw-overflow-hidden tw-rounded-lg tw-mx-auto tw-bg-white tw-text-left tw-shadow-xl sm:tw-my-8 sm:tw-w-full md:tw-max-w-2xl lg:tw-max-w-4xl"
        >
            <div class="tw-flex tw-w-full tw-transform tw-text-left tw-text-base tw-transition">
                <div class="tw-relative tw-flex tw-w-full tw-items-center tw-overflow-hidden tw-bg-white tw-px-4 tw-pb-8 tw-pt-14 tw-shadow-2xl sm:tw-px-6 sm:tw-pt-8 md:tw-p-6 lg:tw-p-8">
                    <button type="button" x-on:click="$dispatch('modal-escaped')" class="tw-absolute tw-right-4 tw-top-4 tw-text-gray-400 hover:tw-text-gray-500 sm:tw-right-6 sm:tw-top-8 md:tw-right-6 md:tw-top-6 lg:tw-right-8 lg:tw-top-8">
                        <span class="tw-sr-only">Close</span>
                        <svg class="tw-size-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12"></path>
                        </svg>
                    </button>

                    <div class="tw-grid tw-w-full tw-grid-cols-1 tw-items-start tw-gap-x-6 tw-gap-y-8 sm:tw-grid-cols-12 lg:tw-gap-x-8">
                        <div class="sm:tw-col-span-4 lg:tw-col-span-5">
                            <div class="tw-aspect-w-1 tw-aspect-h-1">
                                <img src="{{ \App\Models\Media::s3ToCloudfront($product->mainPhoto->path) }}" alt="z" class="tw-aspect-square tw-w-full tw-rounded-lg tw-bg-gray-100 tw-object-cover">
                            </div>
                            <p class="tw-absolute tw-left-4 tw-top-4 tw-text-center sm:tw-static sm:tw-mt-6">
                                <a href="{{ route('store.show', [$product->slug]) }}" class="tw-font-medium tw-text-keppel-600 hover:tw-text-keppel-500">View full details</a>
                            </p>
                        </div>
                        <div class="sm:tw-col-span-8 lg:tw-col-span-7">
                            <h4 class="tw-text-2xl tw-font-bold tw-text-gray-900 sm:tw-pr-12">
                                {{ $product->title }}
                            </h4>

                            <section aria-labelledby="information-heading" class="tw-mt-4">
                                <h2 id="information-heading" class="tw-m-0 tw-sr-only">Product information</h2>

                                <div class="tw-flex tw-flex-col sm:tw-items-center sm:tw-flex-row">
                                    <div class="tw-flex tw-items-baseline tw-space-x-2"
                                         itemprop="offers"
                                         itemscope
                                         itemtype="https://schema.org/Offer"
                                    >

                                        @if($product->isOnSale())
                                            <p class="tw-m-0 tw-text-2xl tw-font-semibold tw-text-theme-brand-color sm:tw-text-3xl">
                                                &#36;<span itemprop="price">{{ money($product->getPrice()) }}</span>
                                            </p>
                                            <p class="tw-m-0 tw-text-base tw-text-gray-500 tw-line-through sm:tw-text-lg">
                                                &#36;{{ money($product->getRegularPrice()) }}
                                            </p>
                                        @else
                                            <p class="tw-m-0 tw-text-2xl tw-font-semibold tw-text-gray-700 sm:tw-text-3xl">
                                                &#36;<span itemprop="price">{{ money($product->getPrice()) }}</span>
                                            </p>
                                        @endif

                                        <meta itemprop="priceCurrency" content="{{ setting('currency', 'USD') }}"/>
                                        <meta itemprop="url" content="{{ route('store.show', [$product->slug]) }}"/>

                                    </div>

                                    <div itemprop="aggregateRating"
                                         itemscope
                                         itemtype="https://schema.org/AggregateRating"
                                         class="tw-mt-1 sm:tw-mt-0 sm:tw-ml-4 sm:tw-pl-4 sm:tw-border-l sm:tw-border-gray-300"
                                    >

                                        <h2 class="tw-m-0 tw-sr-only">Reviews</h2>
                                        <a href="#reviews" class="tw-flex tw-items-center tw-no-underline">
                                            <div>
                                                <div class="tw-flex tw-items-center">
                                                    <!-- Active: "text-yellow-400", Default: "text-gray-300" -->
                                                    <svg class="tw-h-5 tw-w-5 tw-shrink-0 tw-text-[#E9AE20]" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                                        <path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401Z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    <svg class="tw-h-5 tw-w-5 tw-shrink-0 tw-text-[#E9AE20]" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                                        <path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401Z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    <svg class="tw-h-5 tw-w-5 tw-shrink-0 tw-text-[#E9AE20]" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                                        <path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401Z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    <svg class="tw-h-5 tw-w-5 tw-shrink-0 tw-text-[#E9AE20]" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                                        <path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401Z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    <svg class="tw-h-5 tw-w-5 tw-shrink-0 tw-text-[#E9AE20]" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                                        <path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401Z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </div>
                                                <p class="tw-sr-only"><span itemprop="ratingValue">{{ $store_service->rating() }}</span> out of 5 stars</p>
                                            </div>
                                            <p class="tw-m-0 tw-ml-2 tw-text-sm tw-text-gray-500">
                                                <span itemprop="reviewCount">{{ $store_service->reviewCount() }}</span> reviews</p>
                                        </a>
                                    </div>
                                </div>

                                @if(!empty($product->unit_description))
                                    <p class="tw-m-0 tw-mt-2 tw-text-sm tw-text-gray-600">
                                        {{ $product->unit_description }}
                                    </p>
                                @endif
                            </section>

                            <div class="tw-mt-8" x-data="{ shown: false }" x-intersect:enter="shown ? $dispatch('add-to-cart-entered') : () => {}" x-intersect:leave="$dispatch('add-to-cart-left')">
                                <x-theme::form.add-product-button
                                        :product="$product"
                                        cta_classes="tw-px-8 tw-py-3 tw-text-base tw-font-medium tw-rounded-lg tw-font-semibold"
                                        variant_layout="expanded"
                                        :metadata="[
                                            'item_category' => $product->category?->parentCategory?->name ?? $product->category?->name,
                                            'item_category2' => ! is_null($product->category?->parentCategory) ? $product->category?->name : null,
                                            'item_list_id' => 'product_quickview',
                                            'item_list_name' => 'Product Quickview',
                                        ]"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </form>
    @endif
</x-theme::modal>

