<div x-data="scrollableProductList" class="tw-bg-white">
    <div class="tw-sticky tw-bg-white tw-z-10 tw-top-[148px] tw-w-full md:tw-top-[105px] ">
        <div class="tw-bg-white">
            <div class="tw-mx-auto tw-max-w-7xl tw-py-1 tw-px-4 sm:tw-px-6 lg:tw-px-8">
                <div class="tw-flex tw-flex-col tw-place-items-baseline tw-py-1 sm:tw-space-x-3 sm:tw-flex-row sm:tw-px-6 lg:tw-px-0">
                    <h2 class="tw-m-0 tw-text-xl tw-font-bold tw-font-body tw-tracking-tight tw-text-gray-900 sm:tw-text-2xl">{{ $collection->title }}</h2>
                    <p class="tw-m-0 tw-text-sm tw-text-gray-700 ">({{ $total }} results)</p>
                </div>
            </div>
        </div>
    </div>
    <div class="tw-px-4 sm:tw-px-6 lg:tw-mx-auto lg:tw-max-w-7xl lg:tw-px-8">
        <div class="tw-relative">
            <div x-ref="container"
                 class="tw-relative tw-group tw-w-full tw-overflow-x-auto tw-py-3"
                 x-bind:class="{
                    'tw-bg-inner-shadow-l': hasShadows && !atStart,
                    'tw-bg-inner-shadow-r': hasShadows && !atEnd,
                    'tw-bg-inner-shadow-x': hasShadows && !atStart && !atEnd
                }"
            >
                <ul role="list"
                    x-ref="list"
                    x-resize="handleProductLoad"
                    class="tw-inline-flex tw-scroll-smooth @if($type === 'bundle') tw-space-x-8 @else tw-space-x-6 @endif"
                >
                    @if($show_about)
                        <li class="collection-product tw-inline-flex @if($type === 'bundle') tw-w-64 @else tw-w-48 @endif tw-flex-col">
                            <div class="tw-flex-1 tw-flex tw-flex-col">
                                <div class="tw-flex-1 tw-flex tw-flex-col tw-p-4 tw-bg-keppel-50 tw-rounded-md ">
                                    <div class="tw-flex-1 tw-text-sm tw-prose">
                                        {!! $collection->setting('summary') !!}
                                    </div>
                                    <a href="{{ !is_null($url) ? url($url) : route('store.collections.show', [$collection->slug]) }}" class="tw-mt-2 tw-no-underline tw-text-center tw-block tw-w-full tw-px-3 tw-py-2 tw-rounded-lg tw-text-sm tw-font-semibold tw-ring-1 tw-ring-gray-200 tw-bg-white tw-text-theme-brand-color">
                                        Shop All
                                    </a>
                                </div>
                                <div class="tw-h-12"></div>
                            </div>

                        </li>
                    @endif
                    @foreach($products as $product)
                        @php
                            /** @var \App\Models\Product $product */
                            $list_details = [
                                'item_collection' => $collection->title,
                                'item_collection2' => null,
                                'item_list_id' => 'collection_' . $collection?->slug . '_page',
                                'item_list_name' => 'Collection ' . $collection->title . ' Page',
                            ];

                            $product_details = [
                                'currency' => 'USD',
                                'value' => $product->getPrice() / 100,
                                'items' => [
                                    array_merge([
                                        'item_id' => $product->id,
                                        'item_name' => $product->title,
                                        'index' => $index ?? 0,
                                        'price' => $product->getPrice() / 100,
                                        'quantity' => 1
                                    ], $list_details)
                                ]
                            ];
                        @endphp

                        <li wire:key="collection-product-{{ $product->slug }}" class="collection-product tw-inline-flex tw-flex-col @if($type === 'bundle') tw-w-64 @else tw-w-48 @endif ">
                            <div
                                    itemscope itemtype="https://schema.org/Product"
                                    itemid="{{ route('store.show' , [$product->slug]) }}"
                                    class="tw-flex-1 tw-flex tw-flex-col tw-group/product tw-relative"
                                    x-data="tracksProductViews(@js($product_details))"
                                    x-intersect.half.once="trackProductView"
                            >
                                <div class="tw-relative tw-flex-1">
                                    <div class="">
                                        @if($product->mainPhoto)
                                            <div class="tw-aspect-h-1 tw-aspect-w-1">
                                                <img src="{{ \App\Models\Media::s3ToCloudfront($type === 'bundle' ? $product->mainPhoto->path : $product->mainPhoto->thumbnail_path) }}" alt="{{ $product->title }}" itemprop="image" class="tw-w-full tw-rounded-md tw-bg-gray-200 tw-object-cover">
                                            </div>
                                        @else
                                            <div class="tw-h-full tw-w-full">
                                                <img src="{{ \App\Models\Media::s3ToCloudfront(theme('logo_src')) }}" alt="{{ $product->title }}"
                                                     class="tw-absolute tw-top-1/2 tw-left-1/2 tw-transform tw--translate-x-1/2 tw--translate-y-1/2 tw-w-1/2 ">
                                            </div>
                                        @endif
                                        @if($product->calloutMessage())
                                            <div class="tw-absolute tw-p-2 tw-rotate-2 @if($type === 'bundle') tw--top-3 tw--right-5  @else tw--top-3 tw--right-4  @endif">
                                                <span class="tw-inline-flex tw-items-center tw-bg-keppel-700 tw-gap-x-1.5 tw-rounded-md tw-px-2 tw-py-1 tw-text-xs tw-font-semibold tw-text-white tw-shadow-md tw-ring-1 tw-ring-inset tw-ring-keppel-700">
                                                    {{ $product->calloutMessage() }}
                                                </span>
                                            </div>
                                        @endif
                                    </div>

                                    <div class="tw-mt-3">
                                        <p class="tw-m-0 @if($type === 'bundle') tw-text-base @else tw-text-sm @endif tw-leading-6 tw-font-semibold">
                                            <a href="{{ route('store.show', [$product->slug]) }}" title="{{ $product->title }}" class="tw-text-gray-700 hover:tw-text-gray-700">
                                                <span class="tw-absolute tw-inset-0"></span>
                                                <span itemprop="name">{{ $product->title }}</span>
                                            </a>
                                        </p>
                                        <div itemprop="description">
                                            @if ( ! empty($product->unit_description))
                                                <p class="tw-m-0 tw-mt-0.5 tw-leading-4 @if($type === 'bundle') tw-text-sm @else tw-text-xs @endif  tw-text-gray-500">{!! $product->unit_description !!}</p>
                                            @endif
                                            <meta itemprop="sku" content="{{ $product->sku }}"/>
                                        </div>
                                        @if( ! empty($product->vendor_id))
                                            <div itemprop="brand" itemscope itemtype="http://schema.org/Brand" class="tw-hidden">
                                                {!! $product->present()->vendorLink() !!}
                                            </div>
                                        @endif
                                        @if(!empty($product->setting('tagline')))
                                            <p class="tw-m-0 tw-mt-2 tw-text-xs tw-text-gray-600">
                                                {{ $product->setting('tagline') }}
                                            </p>
                                        @endif
                                    </div>
                                </div>

                                @if($type === 'bundle')
                                    <div class="tw-mt-4 tw-flex tw-justify-between tw-space-x-2">
                                        <div class="">
                                            <a href="{{ route('store.show', [$product->slug]) }}" class="tw-mt-2 tw-no-underline tw-block tw-text-center tw-w-full tw-px-4 tw-py-2 tw-rounded-lg tw-text-base tw-leading-5 tw-font-semibold tw-bg-theme-brand-color tw-text-white hover:tw-bg-theme-brand-color/90">
                                                View Bundle
                                            </a>
                                        </div>
                                        <div class="tw-flex-auto tw-flex tw-flex-col tw-items-end tw-justify-end"
                                             itemprop="offers"
                                             itemscope
                                             itemtype="https://schema.org/Offer"
                                        >
                                            @if($product->isOnSale())
                                                <p class="tw-m-0  tw-text-gray-500 tw-line-through tw-text-xs ">
                                                    &#36;{{ money($product->getRegularPrice()) }}
                                                </p>
                                                <p class="tw-m-0 tw-font-bold tw-text-theme-brand-color tw-text-base/4 sm:tw-text-lg/5">
                                                    &#36;<span itemprop="price">{{ money($product->getPrice()) }}</span>
                                                </p>
                                            @else
                                                <p class="tw-m-0 tw-font-semibold tw-text-gray-700 tw-text-sm sm:tw-text-base">
                                                    &#36;<span itemprop="price">{{ money($product->getPrice()) }}</span>
                                                </p>
                                            @endif
                                        </div>
                                    </div>
                                @else
                                    <div class="tw-mt-4 tw-space-y-2">
                                        @if($product->prices->count() > 1)
                                            <div class="tw-text-xs tw-inline-block tw-font-semibold tw-p-px tw-bg-gradient-to-r tw-from-buttercup-300 tw-to-chestnut-rose-400 tw-text-gray-900 tw-rounded-md">
                                                <div class="tw-bg-white tw-px-1.5 tw-py-1 tw-rounded-md tw-inline-flex tw-items-center tw-space-x-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="tw-size-4 tw--scale-x-100 tw-text-gray-500 tw-transform-gpu">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z"/>
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 6h.008v.008H6V6Z"/>
                                                    </svg>
                                                    <div>
                                                        Buy {{ $product->prices[1]->quantity }}, Save 5%
                                                    </div>
                                                </div>

                                            </div>
                                        @endif

                                        <div class="tw-flex tw-items-baseline tw-space-x-2"
                                             itemprop="offers"
                                             itemscope
                                             itemtype="https://schema.org/Offer"
                                        >
                                            @if($product->isOnSale())
                                                <p class="tw-m-0 tw-font-semibold tw-text-theme-brand-color @if($type === 'bundle') tw-text-base sm:tw-text-lg @else tw-text-sm sm:tw-text-base @endif">
                                                    &#36;<span itemprop="price">{{ money($product->getPrice()) }}</span>
                                                </p>
                                                <p class="tw-m-0 tw-text-gray-500 tw-line-through @if($type === 'bundle') tw-text-sm @else tw-text-xs  @endif">
                                                    &#36;{{ money($product->getRegularPrice()) }}
                                                </p>
                                            @else
                                                <p class="tw-m-0 tw-font-semibold tw-text-gray-700 @if($type === 'bundle') tw-text-base sm:tw-text-lg @else tw-text-sm sm:tw-text-base @endif">
                                                    &#36;<span itemprop="price">{{ money($product->getPrice()) }}</span>
                                                </p>
                                            @endif
                                        </div>


                                        <x-theme::form.add-product-counter-button
                                                :product="$product"
                                                :order="$openOrder"
                                                :has_subscription="$has_subscription"
                                                :cta_label="$cta_label ?? null"
                                                :cta_action="$cta_action ?? null"
                                                cta_classes="tw-mt-2 tw-block tw-h-10 tw-w-full tw-px-3 tw-py-2 tw-rounded-lg tw-text-sm tw-font-semibold tw-bg-theme-brand-color tw-text-white"
                                                :metadata="[
                                                    'item_collection' => $collection?->parentCategory?->name ?? $collection?->name,
                                                    'item_collection2' => ! is_null($collection?->parentCategory) ? $collection?->name : null,
                                                    'item_list_id' => 'bundle_' . $product->slug . '_page',
                                                    'item_list_name' => 'Bundle ' . $product->title . ' Page',
                                                ]"
                                        />


                                    </div>
                                @endif
                            </div>
                        </li>
                    @endforeach
                    @if($has_more)
                        <li x-intersect="$wire.loadMoreProducts()" class="collection-product tw-inline-flex @if($type === 'bundle') tw-w-64 @else tw-w-48 @endif tw-flex-col tw-text-center">
                            <div class="tw-group/product tw-relative">
                                <div class="tw-aspect-h-1 tw-aspect-w-1 tw-w-full tw-rounded-md tw-bg-gray-200 tw-object-cover group-hover/product:tw-opacity-75">
                                    <div class="tw-w-full tw-flex tw-items-center tw-justify-center">
                                        <svg class="tw-animate-spin tw--ml-1 tw-mr-3 tw-h-8 tw-w-8 tw-text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </li>
                    @endif
                </ul>
            </div>
            <div class="tw-absolute tw-pointer-events-none tw-inset-0 tw-w-full tw-px-2 tw-flex tw-justify-between tw-items-center">
                <div>
                    <button x-show="!atStart" x-on:click="scrollLeft('.collection-product')" class="tw-pointer-events-auto tw-hidden tw-bg-gray-600 tw-ring-2 tw-ring-white tw-shadow-md tw-rounded-full tw-h-12 tw-w-12 sm:tw-flex sm:tw-items-center sm:tw-justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor" class="tw-text-gray-100 hover:tw-text-gray-100 tw-size-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5"/>
                        </svg>
                    </button>
                </div>

                <div>
                    <button x-show="!atEnd" x-on:click="scrollRight('.collection-product')" class="tw-pointer-events-auto tw-hidden tw-bg-gray-600 tw-ring-2 tw-ring-white tw-shadow-md tw-rounded-full tw-h-12 tw-w-12 sm:tw-flex sm:tw-items-center sm:tw-justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor" class="tw-text-gray-100 hover:tw-text-gray-100 tw-size-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5"/>
                        </svg>
                    </button>
                </div>

            </div>
        </div>
    </div>
</div>
