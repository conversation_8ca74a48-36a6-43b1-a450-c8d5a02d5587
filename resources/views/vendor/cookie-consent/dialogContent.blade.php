<div class="tw-reset">
    <div class="js-cookie-consent cookie-consent tw-fixed tw-inset-x-0 tw-bottom-0 tw-z-50 tw-bg-white tw-border-t tw-border-gray-200 tw-shadow-lg">
        <div class="tw-max-w-7xl tw-mx-auto sm:tw-px-6 lg:tw-px-6">
            <div class="tw-p-4 md:tw-p-2 tw-rounded-lg tw-bg-yellow-100">
                <div class="tw-flex tw-items-center tw-justify-between tw-flex-wrap">
                    <div class="tw-max-w-full tw-flex-1 tw-items-center md:tw-w-0 md:tw-inline">
                        <p class="md:tw-ml-3 tw-text-black cookie-consent__message">
                            {!! trans('cookie-consent::texts.message') !!}
                        </p>
                    </div>
{{--                    <div class="tw-mt-2 tw-flex-shrink-0 tw-w-full sm:tw-mt-0 sm:tw-w-auto">--}}
                    <div class="tw-flex tw-items-center tw-gap-3 tw-w-full sm:tw-w-auto">
                        <button class="js-cookie-consent-agree cookie-consent__agree tw-cursor-pointer tw-flex tw-items-center tw-justify-center tw-px-4 tw-py-2 tw-rounded-md tw-text-sm tw-font-medium tw-text-yellow-800 tw-bg-yellow-400 hover:tw-bg-yellow-300">
                            {{ trans('cookie-consent::texts.agree') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{{--<div class="tw-js-cookie-consent tw-cookie-consent tw-fixed tw-bottom-0 tw-inset-x-0 tw-pb-2 tw-z-50">--}}
{{--    <div class="tw-max-w-7xl tw-mx-auto tw-px-6">--}}
{{--        <div class="tw-p-4 tw-md:p-2 tw-rounded-lg tw-bg-yellow-100">--}}
{{--            <div class="tw-flex tw-items-center tw-justify-between tw-flex-wrap">--}}
{{--                <div class="tw-max-w-full tw-flex-1 tw-items-center tw-md:w-0 tw-md:inline">--}}
{{--                    <p class="tw-md:ml-3 tw-text-black tw-cookie-consent__message">--}}
{{--                        {!! trans('cookie-consent::texts.message') !!}--}}
{{--                    </p>--}}
{{--                </div>--}}
{{--                <div class="tw-mt-2 tw-flex-shrink-0 tw-w-full tw-sm:mt-0 tw-sm:w-auto">--}}
{{--                    <button class="tw-js-cookie-consent-agree tw-cookie-consent__agree tw-cursor-pointer tw-flex tw-items-center tw-justify-center tw-px-4 tw-py-2 tw-rounded-md tw-text-sm tw-font-medium tw-text-yellow-800 tw-bg-yellow-400 tw-hover:bg-yellow-300">--}}
{{--                        {{ trans('cookie-consent::texts.agree') }}--}}
{{--                    </button>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    </div>--}}
{{--</div>--}}

    <div class="tw-max-w-7xl tw-mx-auto tw-px-4 tw-py-4 sm:tw-px-6 lg:tw-px-8">
        <div class="tw-flex tw-flex-col tw-items-start tw-gap-4 sm:tw-flex-row sm:tw-items-center sm:tw-justify-between">
            <div class="tw-flex-1">
                <h3 id="cookie-consent-title" class="tw-sr-only">Cookie Consent</h3>
                <p id="cookie-consent-description" class="tw-text-sm tw-text-gray-700 tw-leading-relaxed">
                    We use cookies to make your visit to our farm store even better. By continuing to browse, you're agreeing to our use of cookies. You can read more in our
                    <a href="{{ route('privacy-policy') }}" class="tw-text-theme-action-color tw-underline hover:tw-text-theme-action-color/80 tw-transition-colors">Privacy Policy</a>.
                </p>
            </div>
            <div class="tw-flex tw-items-center tw-gap-3 tw-w-full sm:tw-w-auto">
                <button
                        type="button"
                        wire:click="acceptCookies"
                        wire:loading.attr="disabled"
                        wire:target="acceptCookies"
                        class="tw-inline-flex tw-items-center tw-justify-center tw-px-6 tw-py-2 tw-bg-theme-action-color tw-text-white tw-text-sm tw-font-semibold tw-rounded-md tw-shadow-sm hover:tw-bg-theme-action-color/90 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-theme-action-color focus:tw-ring-offset-2 tw-transition-colors tw-w-full sm:tw-w-auto"
                        aria-label="Accept cookies and close banner"
                >
                    <span wire:loading.remove wire:target="acceptCookies">Got it!</span>
                    <svg wire:loading.inline wire:target="acceptCookies" class="tw-animate-spin tw-h-4 tw-w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
</div>