{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^8.2", "ext-simplexml": "*", "bugsnag/bugsnag-laravel": "^2.27", "cviebrock/eloquent-sluggable": "*", "danharrin/livewire-rate-limiting": "^2.1", "firebase/php-jwt": "^6.10", "giggsey/libphonenumber-for-php": "^8.13", "guzzlehttp/guzzle": "^7.2", "html2text/html2text": "^4.3", "http-interop/http-factory-guzzle": "^1.2", "inertiajs/inertia-laravel": "*", "intervention/image": "^2.7", "laracasts/presenter": "0.2.8", "laravel-notification-channels/twilio": "^4.1", "laravel/framework": "^12.0", "laravel/horizon": "^5.22", "laravel/sanctum": "^4.0", "laravel/scout": "^10.11", "laravel/slack-notification-channel": "^3.2", "laravel/socialite": "^5.11", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.12", "livewire/livewire": "^3.6", "maatwebsite/excel": "^3.1", "mailgun/mailgun-php": "^3.5", "matanyadaev/laravel-eloquent-spatial": "^4.5", "meilisearch/meilisearch-php": "^1.10", "picqer/php-barcode-generator": "^3.2", "predis/predis": "^2.1", "propaganistas/laravel-disposable-email": "^2.3", "spatie/laravel-cookie-consent": "^3.3", "spatie/laravel-html": "^3.9", "spatie/laravel-schemaless-attributes": "^2.5", "stripe/stripe-php": "*", "symfony/http-client": "^7.1", "symfony/mailgun-mailer": "^7.1", "tucker-eric/eloquentfilter": "^3.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.10", "barryvdh/laravel-ide-helper": "^3.0", "fakerphp/faker": "^1.23", "larastan/larastan": "^3.0", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "laravel/telescope": "^5.7", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1"}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "allow-plugins": {"php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": ["itsgoingd/clockwork", "laravel/telescope"]}}, "autoload": {"files": ["app/Helpers/functions.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan vendor:publish --force --tag=livewire:assets --ansi", "@php artisan ide-helper:generate", "@php artisan ide-helper:meta"]}}