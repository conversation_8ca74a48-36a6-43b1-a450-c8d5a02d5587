<?php

namespace App\Models;

use App\Actions\Cart\ConfirmDatabaseCartWithCart;
use App\Actions\Cart\ConfirmDatabaseCartWithParams;
use App\Cart\Item;
use App\Cart\Subscription;
use App\Contracts\Cartable;
use App\Events\Cart\CartUpdated;
use App\Events\Cart\ExcludedProductsRemovedFromCart;
use App\Exceptions\BackOrderException;
use App\Exceptions\ExclusivityException;
use App\Models\Concerns\HasCartQuantitiesByProductIdMap;
use App\Services\DeliveryMethodService;
use App\Services\SettingsService;
use App\Services\SubscriptionSettingsService;
use App\Traits\PickupDateSelectBuilder;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;
use Spatie\SchemalessAttributes\Casts\SchemalessAttributes;

/**
 * App\Models\Cart
 *
 * @property \Spatie\SchemalessAttributes\SchemalessAttributes $extra_attributes
 *
 * @method static \Database\Factories\CartFactory factory($count = null, $state = [])
 * @method static Builder|Cart newModelQuery()
 * @method static Builder|Cart newQuery()
 * @method static Builder|Cart query()
 * @method static Builder|Cart withExtraAttributes()
 *
 * @property string $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $shopper_type
 * @property string|null $shopper_id
 * @property string $type
 *
 * @method static Builder|Cart whereCreatedAt($value)
 * @method static Builder|Cart whereExtraAttributes($value)
 * @method static Builder|Cart whereId($value)
 * @method static Builder|Cart whereShopperId($value)
 * @method static Builder|Cart whereShopperType($value)
 * @method static Builder|Cart whereType($value)
 * @method static Builder|Cart whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Cart extends Model implements Cartable
{
    use HasCartQuantitiesByProductIdMap, HasFactory, HasUuids, PickupDateSelectBuilder;

    protected $guarded = ['id', 'extra_attributes'];

    protected $dispatchesEvents = [
        'saved' => CartUpdated::class,
        'deleted' => CartUpdated::class,
    ];

    public static function newFromOrder(Order $order): Cart
    {
        $cart = new Cart([
            'shopper_type' => User::class,
            'shopper_id' => $order->customer_id,
        ]);

        $cart->extra_attributes->set([
            'date_id' => null,
            'delivery_method_id' => $order->pickup?->id,
            'items' => $order->items
                ->filter(fn (OrderItem $item) => $item->type !== 'promo')
                ->map(fn (OrderItem $item) => [
                    'id' => $item->id,
                    'product' => ['id' => $item->product_id],
                    'quantity' => $item->qty,
                    'price' => null,
                    'weight' => null,
                ])
                ->values()
                ->toArray(),
            'subscription' => $order->cartSubscription()?->toArray(),
            'discounts' => [
                'coupons' => $order->discounts->map(fn (Coupon $coupon) => $coupon->toCartCoupon()->toArray())->toArray(),
                'gift_card' => [
                    'name' => '',
                    'code' => '',
                    'amount' => 0,
                ],
                'store_credit' => [
                    'amount' => $order->customer?->credit,
                ],
            ],
            'contact' => [
                'first_name' => $order->customer?->first_name,
                'last_name' => $order->customer?->last_name,
                'email' => $order->customer?->email,
                'phone' => $order->customer?->phone,
                'save_for_later' => true,
                'opt_in_to_sms' => ! is_null($order->customer?->subscribed_to_sms_marketing_at),
                'subscribed_to_sms' => ! is_null($order->customer?->subscribed_to_sms_marketing_at),
            ],
            'shipping' => [
                'address_id' => null,
                'street' => $order->shipping_street ?: $order->customer?->street,
                'street_2' => $order->shipping_street_2 ?: $order->customer?->street_2,
                'city' => $order->shipping_city ?: $order->customer?->city,
                'state' => $order->shipping_state ?: $order->customer?->state,
                'zip' => $order->shipping_zip ?: $order->customer?->zip,
                'country' => $order->customer->country ?? app(SettingsService::class)->farmCountry(),
                'save_for_later' => true,
            ],
            'billing' => [
                'method' => null,
                'source_id' => $order->customer?->checkout_card_id ?: null,
                'save_for_later' => true,
            ],
        ]);

        return $cart;
    }

    public function cartSubscription(): ?Subscription
    {
        return match ($this->extra_attributes->subscription) {
            null => null,
            default => new Subscription(
                frequency: $this->extra_attributes->get('subscription.frequency'),
                product_incentive_id: $this->extra_attributes->get('subscription.product_incentive_id')
            )
        };
    }

    public static function initialAttributesForShopper(User $shopper): array
    {
        $default_shipping_attributes = $shopper->defaultShippingAttributes();

        return [
            'purchase_type_id' => Cartable::ONE_TIME_PURCHASE,
            'date_id' => null,
            'delivery_method_id' => $shopper->pickup_point,
            'items' => [],
            'subscription' => null,
            'discounts' => [
                'coupons' => [],
                'conditional_coupons' => [],
                'gift_card' => [
                    'name' => '',
                    'code' => '',
                    'amount' => 0,
                ],
                'store_credit' => [
                    'amount' => $shopper->credit,
                ],
            ],
            'contact' => [
                'first_name' => $shopper->first_name,
                'last_name' => $shopper->last_name,
                'email' => $shopper->email,
                'phone' => $shopper->phone,
                'save_for_later' => true,
                'opt_in_to_sms' => ! is_null($shopper->subscribed_to_sms_marketing_at),
                'subscribed_to_sms' => ! is_null($shopper->subscribed_to_sms_marketing_at),
            ],
            'shipping' => [
                'address_id' => $default_shipping_attributes['address_id'],
                'street' => $default_shipping_attributes['street'],
                'street_2' => $default_shipping_attributes['street_2'],
                'city' => $default_shipping_attributes['city'],
                'state' => $default_shipping_attributes['state'],
                'zip' => $default_shipping_attributes['postal_code'],
                'country' => $default_shipping_attributes['country'],
                'save_for_later' => true,
            ],
            'billing' => [
                'method' => null,
                'source_id' => $shopper->checkout_card_id,
                'save_for_later' => true,
            ],
        ];
    }

    public static function stub(?User $user = null): Cart
    {
        $cart = new Cart;

        if (is_null($user)) {
            return $cart;
        }

        $cart->shopper_type = User::class;
        $cart->shopper_id = (string) $user->id;

        $default_address = $user->addresses()->default()->first();

        if (is_null($default_address)) {
            return $cart;
        }

        $shipping_info = $default_address->toCartShippingInfo();
        $delivery_method = app(DeliveryMethodService::class)
            ->deliveryZones()
            ->find($default_address->toGeocodedAddress())
            ->first();

        $cart->extra_attributes->delivery_method_id = $delivery_method?->id;
        $cart->extra_attributes->set(['shipping' => [
            'address_id' => $shipping_info['address_id'] ?? null,
            'street' => $shipping_info['street'] ?? '',
            'street_2' => $shipping_info['street_2'] ?? '',
            'city' => $shipping_info['city'] ?? '',
            'state' => $shipping_info['state'] ?? '',
            'zip' => $shipping_info['postal_code'] ?? $shipping_info['zip'] ?? '',
            'country' => app(SettingsService::class)->farmCountry(),
            'save_for_later' => true,
        ]]);

        return $cart;
    }

    protected static function booted()
    {
        parent::booted(); // TODO: Change the autogenerated stub
    }

    public function updateCartLocation(Pickup $location, bool $remove_excluded_items = true): Cartable
    {
        $this->extra_attributes->delivery_method_id = $location->id;
        $this->save();

        if ($remove_excluded_items) {
            $this->removeExcludedProducts($location);
        }

        // change cart to one time purchase if needed
        if ($this->isRecurring()) {
            $settings = app(SubscriptionSettingsService::class);

            if (($location->schedule?->isCustom() ?? true)) {
                $this->setCartAsOneTimePurchase();
            }
        }

        return $this;
    }

    private function removeExcludedProducts(Pickup $location): void
    {
        $excluded_products = $location->products()->select(['products.id', 'products.title'])->get();

        $removed_products = collect();

        foreach ($excluded_products as $excluded_product) {
            /**
             * @var Product $excluded_product
             * @var array|null $item_to_remove
             */
            $item_to_remove = $this->rawCartItems()
                ->first(fn (array $item) => $item['product']['id'] === $excluded_product->id);

            if (! is_null($item_to_remove)) {
                $removed_products->push($excluded_product);
                $this->removeCartItem($item_to_remove['id']);
            }
        }

        if ($removed_products->isNotEmpty()) {
            event(new ExcludedProductsRemovedFromCart($removed_products));
        }
    }

    private function rawCartItems(): Collection
    {
        return collect($this->extra_attributes->items ?? []);
    }

    public function removeCartItem($id): Cartable
    {
        $index = $this->cartItemIndex($id);

        if ($index === false) {
            return $this;
        }

        $items = collect($this->extra_attributes->get('items', []))
            ->forget($index)
            ->values();

        $this->extra_attributes->set('items', $items->toArray());

        $this->save();

        return $this;
    }

    private function cartItemIndex(string $id): int|bool
    {
        return collect($this->extra_attributes->get('items', []))
            ->search(fn (array $item) => (string) $item['id'] === $id);
    }

    public function isRecurring(): bool
    {
        return ! is_null($this->cartSubscription());
    }

    public function setCartAsOneTimePurchase(): Cartable
    {
        $this->extra_attributes->subscription = null;
        $this->save();

        return $this;
    }

    public function setShippingInfo(array $shipping_info): Cartable
    {
        $current_attributes = $this->getShippingInfo();

        foreach ($shipping_info as $attribute => $value) {
            $this->extra_attributes->set("shipping.{$attribute}", $value);
        }

        $this->save();

        return $this;
    }

    public function getShippingInfo(): array
    {
        $shipping = $this->extra_attributes->get('shipping');

        if (! is_null($shipping)) {
            return $shipping;
        }

        $default_address = $this->cartCustomer()?->defaultShippingAttributes();

        return [
            'address_id' => $default_address['address_id'] ?? null,
            'street' => $default_address['street'] ?? '',
            'street_2' => $default_address['street_2'] ?? '',
            'city' => $default_address['city'] ?? '',
            'state' => $default_address['state'] ?? '',
            'zip' => $default_address['postal_code'] ?? '',
            'postal_code' => $default_address['postal_code'] ?? '',
            'country' => $default_address['country'] ?? 'USA',
            'save_for_later' => true,
        ];
    }

    public function cartCustomer(): ?User
    {
        return once(function () {
            if ($this->shopper_type !== User::class) {
                return null;
            }

            return User::find((int) $this->shopper_id);
        });

    }

    public function scopeWithExtraAttributes(): Builder
    {
        return $this->extra_attributes->modelScope();
    }

    public function cartId(): string
    {
        return $this->id;
    }

    public function cartIsEmpty(): bool
    {
        return $this->rawCartItems()->isEmpty();
    }

    public function cartPricingGroup(): ?ProductPriceGroup
    {
        return once(function () {
            $group_id = $this->cartPricingGroupId();

            if (is_null($group_id)) {
                return null;
            }

            return ProductPriceGroup::find($group_id);
        });
    }

    public function cartPricingGroupId(): ?int
    {
        return once(function () {
            $customer = $this->cartCustomer();

            if ($customer?->pricing_group_id) {
                return (int) $customer->pricing_group_id;
            }

            $location = $this->cartLocation();

            if ($location?->pricing_group_id) {
                return (int) $location->pricing_group_id;
            }

            return null;
        });
    }

    public function cartLocation(): ?Pickup
    {
        return once(function () {
            if (! $this->extra_attributes->delivery_method_id) {
                return null;
            }

            return Pickup::find($this->extra_attributes->delivery_method_id);
        });
    }

    /**
     * @throws Exception
     */
    public function incrementCartItemQuantity($id): Cartable
    {
        $index = $this->cartItemIndex($id);

        if ($index === false) {
            return $this;
        }

        $items = collect($this->extra_attributes->get('items', []));

        /** @var array $item */
        $item = $items->get($index);

        return $this->updateCartItemQuantity($id, $item['quantity'] + 1);
    }

    /**
     * @throws Exception
     */
    public function updateCartItemQuantity($id, int $quantity): Cartable
    {
        $index = $this->cartItemIndex($id);

        if ($index === false) {
            return $this;
        }

        $items = collect($this->extra_attributes->get('items', []));

        /** @var array $item */
        $item = $items->get($index);

        $this->validateChangeInQuantity($item, $quantity);

        $item['quantity'] = $quantity;

        $this->extra_attributes->set('items', $items->put($index, $item)->toArray());

        $this->save();

        return $this;
    }

    /**
     * @throws ExclusivityException
     * @throws BackOrderException
     */
    public function validateChangeInQuantity($item, int $desired_quantity): void
    {
        /** @var array $item */

        /** @var Product $product */
        $product = Product::find($item['product']['id']);

        if ($this->cartLocation()?->excludesProduct($product)) {
            throw new ExclusivityException($this->cartLocation(), [$product]);
        }

        if ($product->hasLimitPerCustomer() && $desired_quantity > $product->limitPerCustomer()) {
            throw new BackOrderException("There is a limit of {$product->limitPerCustomer()} per customer for this product.");
        }

        $is_increase = $item['quantity'] < $desired_quantity;
        if ($is_increase && ! $product->canAddAmountToCart($desired_quantity - $item['quantity'], $this)) {
            throw new BackOrderException($product->getOutOfStockMessage());
        }
    }

    /**
     * @throws Exception
     */
    public function decrementCartItemQuantity($id): Cartable
    {
        $index = $this->cartItemIndex($id);

        if ($index === false) {
            return $this;
        }

        $items = collect($this->extra_attributes->get('items', []));

        /** @var array $item */
        $item = $items->get($index);

        $new_quantity = $item['quantity'] - 1;

        if ($new_quantity <= 0) {
            return $this->removeCartItem($id);
        }

        return $this->updateCartItemQuantity($id, $new_quantity);
    }

    public function stubCartLocation(Pickup $location): Cartable
    {
        $this->extra_attributes->delivery_method_id = $location->id;

        return $this;
    }

    public function updateCartDate(?Date $date): Cartable
    {
        $this->extra_attributes->date_id = $date?->id;
        $this->save();

        return $this;
    }

    public function setCartAsSubscriptionPurchase(?int $frequency = null, ?int $product_incentive_id = null): Cartable
    {
        $this->extra_attributes->subscription = (new Subscription($frequency, $product_incentive_id))->toArray();
        $this->save();

        return $this;
    }

    public function applyCouponToCart(Coupon $coupon): Cartable
    {
        $coupons = $this->cartCoupons();

        $coupons->push([
            'name' => $coupon->description,
            'code' => $coupon->code,
            'amount' => $coupon->valueForCart($this),
        ]);

        $this->extra_attributes->set('discounts.coupons', $coupons->toArray());
        $this->save();

        return $this;
    }

    public function cartCoupons(): Collection
    {
        return collect($this->extra_attributes->get('discounts.coupons', []))
            ->map(fn (array $coupon) => new \App\Cart\Coupon(
                name: $coupon['name'],
                amount: $coupon['amount'],
                code: $coupon['code']
            ));
    }

    public function applyConditionalCouponToCart(Coupon $coupon): Cartable
    {
        $coupons = $this->cartConditionalCoupons();

        $coupons->push([
            'name' => $coupon->description,
            'code' => $coupon->code,
            'amount' => $coupon->valueForCart($this),
        ]);

        $this->extra_attributes->set('discounts.conditional_coupons', $coupons->toArray());
        $this->save();

        return $this;
    }

    public function cartConditionalCoupons(): Collection
    {
        return collect($this->extra_attributes->get('discounts.conditional_coupons', []))
            ->map(fn (array $coupon) => new \App\Cart\Coupon(
                name: $coupon['name'],
                amount: $coupon['amount'],
                code: $coupon['code']
            ));
    }

    public function removeCouponFromCart(string $code): Cartable
    {
        $coupons = $this->cartCoupons()
            ->filter(fn (\App\Cart\Coupon $coupon) => $coupon->code !== $code);

        $this->extra_attributes->set('discounts.coupons', $coupons->toArray());
        $this->save();

        return $this;
    }

    public function removeConditionalCouponFromCart(string $code): Cartable
    {
        $coupons = $this->cartConditionalCoupons()
            ->filter(fn (\App\Cart\Coupon $coupon) => $coupon->code !== $code);

        $this->extra_attributes->set('discounts.conditional_coupons', $coupons->toArray());
        $this->save();

        return $this;
    }

    public function hasCouponApplied(string $code): bool
    {
        return $this->cartCoupons()
            ->filter(fn (\App\Cart\Coupon $coupon) => $coupon->code === $code)
            ->isNotEmpty();
    }

    public function hasConditionalCouponApplied(string $code): bool
    {
        return $this->cartConditionalCoupons()
            ->filter(fn (\App\Cart\Coupon $coupon) => $coupon->code === $code)
            ->isNotEmpty();
    }

    public function cartWeight(): float
    {
        return $this->itemsInCart()
            ->sum(fn (Item $item) => $item->weight());
    }

    /**
     * @return Collection<Item>
     */
    public function itemsInCart(): Collection
    {
        return once(function () {
            $items = $this->rawCartItems();

            $products = Product::query()
                ->select(Product::attributesForCart())
                ->with([
                    'defaultPrice',
                    'prices',
                    'price' => fn ($q) => $q->where('group_id', $this->cartPricingGroupId() ?? 0),
                    'category.parentCategory',
                ])
                ->findMany($this->cartProductIds());

            return $this->rawCartItems()
                ->map(function (array $item) use ($products) {
                    /** @var Product|null $product */
                    $product = $products->firstWhere('id', (int) $item['product']['id']);

                    if (is_null($product)) {
                        $this->removeCartItem($item['id']);
                        return null;
                    }

                    return new Item(
                        id: $item['id'],
                        product: $product,
                        quantity: $item['quantity'],
                        price: $item['price'] ?? null,
                        weight: $item['weight'] ?? null,
                    );
                })
                ->filter();
        });
    }

    public function cartProductIds(): Collection
    {
        return $this->rawCartItems()
            ->map(fn (array $item) => (int) $item['product']['id']);
    }

    public function itemCount(): int
    {
        return $this->itemsInCart()->sum(fn(Item $item) => $item->quantity);
    }

    public function cartTotal(): int
    {
        return max(0, $this->cartTotalBeforeStoreCredit() - $this->cartStoreCreditTotal());
    }

    public function cartTotalBeforeStoreCredit(): int
    {
        return $this->cartSubtotal()
            + $this->cartLocationFeeTotal()
            + $this->cartDeliveryTotal()
            + $this->cartTaxTotal()
            - $this->cartSubscriptionSavingsTotal()
            - $this->cartCouponTotal();
    }

    public function cartSubtotal(): int
    {
        return $this->itemsInCart()
            ->sum(fn (Item $item) => $item->subtotal())
            + ($this->cartSubscriptionProductIncentiveItem()?->subtotal() ?? 0);
    }

    public function cartSubscriptionProductIncentiveItem(): ?Item
    {
        $product = $this->cartSubscriptionProductIncentive();

        if (is_null($product)) {
            return null;
        }

        return new Item(id: 'promo', product: $product);
    }

    public function cartSubscriptionProductIncentive(): ?Product
    {
        if ($this->purchaseType() !== Cartable::SUBSCRIPTION_PURCHASE) {
            return null;
        }

        $id = $this->cartSubscription()?->product_incentive_id;

        if (is_null($id)) {
            return null;
        }

        return Product::query()
            ->select(Product::attributesForCart())
            ->with(['price' => fn ($q) => $q->where('group_id', $this->cartPricingGroupId())])
            ->find($id);
    }

    public function purchaseType(): int
    {
        return ! is_null($this->extra_attributes->subscription)
            ? Cartable::SUBSCRIPTION_PURCHASE
            : Cartable::ONE_TIME_PURCHASE;
    }

    public function cartLocationFeeTotal(): int
    {
        if ($this->cartCustomer()?->exemptFromFees()) {
            return 0;
        }

        return $this->cartLocation()?->fees()->sum('amount') ?? 0;
    }

    public function cartDeliveryTotal(): int
    {
        $location = $this->cartLocation();

        if (is_null($location) || ($this->cartCustomer()?->exemptFromFees() ?? false)) {
            return 0;
        }

        return $location->deliveryFeeForCart($this);
    }

    public function cartTaxTotal(): int
    {
        if ($this->cartCustomer()?->exemptFromTax()) {
            return 0;
        }

        $tax_rate = $this->cartLocation()->tax_rate ?? 0.0;

        if ($tax_rate <= 0) {
            return 0;
        }

        $taxable_subtotal = $this->itemsInCart()
            ->filter(fn (Item $item) => $item->isTaxable())
            ->sum(fn (Item $item) => $item->subtotal());

        $taxable_fee_total = $this->cartLocationTaxableFeeTotal();
        if ($this->cartCustomer()?->exemptFromFees()) {
            $taxable_fee_total = 0;
        }

        return (int) round($taxable_subtotal * $tax_rate)
            + (int) round($taxable_fee_total * $tax_rate)
            + $this->cartDeliveryTaxTotal();
    }

    private function cartLocationTaxableFeeTotal(): int
    {
        return $this->cartLocation()
            ?->fees()
            ->where('taxable', true)
            ->sum('amount')
            ?? 0;
    }

    private function cartDeliveryTaxTotal(): int
    {
        $location = $this->cartLocation();

        if (is_null($location)) {
            return 0;
        }

        $tax_rate = $location->tax_rate ?? 0;

        if ($tax_rate <= 0 || ! $location->tax_delivery_fee) {
            return 0;
        }

        return (int) round($this->cartDeliveryTotal() * $tax_rate);
    }

    public function cartSubscriptionSavingsTotal(): int
    {
        if ($this->purchaseType() === Cartable::ONE_TIME_PURCHASE) {
            return 0;
        }

        return $this->cartPotentialSubscriptionSavingsTotal();
    }

    public function cartPotentialSubscriptionSavingsTotal(): int
    {
        $eligible_subtotal = $this->itemsInCart()
            ->filter(fn (Item $item) => $item->isEligibleForSubscriptionSavings())
            ->sum(fn (Item $item) => $item->subtotal());

        return (int) round($eligible_subtotal * (app(SubscriptionSettingsService::class)->discountIncentive() / 100));
    }

    public function cartCouponTotal(): int
    {
        return $this->cartCoupons()->sum('amount');
    }

    public function cartStoreCreditTotal(): int
    {
        return $this->cartCustomer()->credit ?? 0;
    }

    public function cartIsEligibleForSubscription(): bool
    {
        return ! ($this->cartCustomer()?->hasRecurringOrder() ?? false)
            && $this->cartLocation()?->schedule?->isRepeating()
            && count($this->cartLocation()->schedule->reorder_frequency) > 0
            && $this->hasSubscriptionEligibleItems();
    }

    public function hasSubscriptionEligibleItems(): bool
    {
        $excluded_ids = app(SubscriptionSettingsService::class)->excludedProductIds();

        return collect($this->extra_attributes->get('items'))
            ->reject(fn (array $item) => $excluded_ids->contains($item['product']['id']))
            ->isNotEmpty();
    }

    /**
     * @throws Exception
     */
    public function addProduct(Product $product, int $quantity = 1): Item
    {
        $item = new Item(
            id: Uuid::uuid4(),
            product: $product,
            quantity: $quantity,
        );

        $this->addItemToCart($item);

        return $item;
    }

    /**
     * @throws Exception
     */
    public function addItemToCart(Item $item): Cartable
    {
        $existing_cart_item = $this->itemForProduct($item->product->id);

        if (! is_null($existing_cart_item)) {
            return $this->updateCartItemQuantity($existing_cart_item->id, $existing_cart_item->quantity + 1);
        }

        $this->validateAddToCart($item);

        $items = $this->extra_attributes->get('items');

        $items[] = $item->toArray();

        $this->extra_attributes->set(compact('items'));

        $this->save();

        return $this;
    }

    private function itemForProduct(int $product_id): ?Item
    {
        return $this->itemsInCart()
            ->first(fn (Item $item) => $item->product->id === $product_id);
    }

    /**
     * @throws BackOrderException
     * @throws ExclusivityException
     */
    public function validateAddToCart($item): void
    {
        /** @var Item $item */
        if ($this->cartLocation()?->excludesProduct($item->product)) {
            throw new ExclusivityException($this->cartLocation(), [$item->product]);
        }

        if ($item->product->hasLimitPerCustomer() && $item->quantity > $item->product->limitPerCustomer()) {
            throw new BackOrderException("There is a limit of {$item->product->limitPerCustomer()} per customer for this product.");
        }

        if (! $item->product->canAddAmountToCart($item->quantity, $this)) {
            throw new BackOrderException($item->product->getOutOfStockMessage());
        }
    }

    public function meetsOrderMinimum(): bool
    {
        $location = $this->cartLocation();

        if (is_null($location)) {
            return true;
        }

        return $this->cartSubtotal() >= $location->min_customer_orders;
    }

    public function oneTimeItems(): Collection
    {
        $settings = app(SubscriptionSettingsService::class);

        return $this->itemsInCart()
            ->filter(fn (Item $item) => $settings->excludedProductIds()->contains($item->product->id));
    }

    public function subscriptionItems(): Collection
    {
        $settings = app(SubscriptionSettingsService::class);

        return $this->itemsInCart()
            ->filter(fn (Item $item) => $settings->excludedProductIds()->doesntContain($item->product->id));
    }

    public function cartPotentialSubscriptionProductValue(): int
    {
        $id = $this->cartSubscriptionProductIncentive()->id
            ?? app(SubscriptionSettingsService::class)->defaultProductIncentiveId();

        if (is_null($id)) {
            return 0;
        }

        $product = Product::query()
            ->select(Product::attributesForCart())
            ->with([
                'price' => fn ($q) => $q->where('group_id', $this->cartPricingGroupId()),
            ])
            ->find($id);

        if (is_null($product)) {
            return 0;
        }

        return $product->getRegularUnitPrice() - $product->getUnitPrice();
    }

    public function getFreeItemSavings(): int
    {
        $incentive = $this->cartSubscriptionProductIncentive();

        if (is_null($incentive)) {
            return 0;
        }

        return (new Item(
            'promo',
            product: $incentive,
            quantity: 1
        ))->subtotal();
    }

    public function toCartArray(): array
    {
        $customer = $this->cartCustomer();

        return [
            'type' => 'database',
            'id' => $this->id,
            'purchase_type' => $this->purchaseType() === Cartable::SUBSCRIPTION_PURCHASE
                ? 'recurring'
                : 'one_time_purchase',
            'date_id' => $this->cartDate()?->id,
            'delivery_method_id' => $this->cartLocation()?->id,
            'items' => $this->itemsInCart()->map(function (Item $item) {

                // This prevents bundle products from being a part of the cart array
                $item->product->setVisible(array_merge(Product::attributesForCart(), ['price', 'category']));

                return [
                    'id' => $item->id,
                    'product' => $item->product->toArray(),
                    'quantity' => $item->quantity,
                    'price' => $item->price(),
                    'weight' => $item->weight(),
                ];
            })->toArray(),
            'subscription' => $this->cartSubscription()?->toArray(),
            'discounts' => [
                'coupons' => $this->extra_attributes->get('discounts.coupons') ?? [],
                'gift_card' => [
                    'name' => '',
                    'code' => '',
                    'amount' => 0,
                ],
                'store_credit' => [
                    'amount' => min($customer->credit ?? 0, $this->cartSubtotal()),
                ],
            ],
            'customer' => [
                'id' => $customer?->id,
                'first_name' => $customer?->first_name,
                'last_name' => $customer?->last_name,
                'email' => $customer?->email,
                'phone' => $customer?->phone,
                'save_for_later' => true,
                'opt_in_to_sms' => ! is_null($customer?->subscribed_to_sms_marketing_at),
                'subscribed_to_sms' => ! is_null($customer?->subscribed_to_sms_marketing_at),
            ],
            //todo: customer address pull from customer or default address
            'shipping' => [
                'address_id' => $this->extra_attributes->get('shipping.address_id'),
                'street' => $this->extra_attributes->get('shipping.street'),
                'street_2' => $this->extra_attributes->get('shipping.street_2'),
                'city' => $this->extra_attributes->get('shipping.city'),
                'state' => $this->extra_attributes->get('shipping.state'),
                'zip' => $this->extra_attributes->get('shipping.zip'),
                'country' => app(SettingsService::class)->farmCountry(),
                'save_for_later' => true
            ],
            'billing' => [
                'method' => null,
                'source_id' => null,
                'save_for_later' => true,
            ],
        ];
    }

    public function cartDate(): ?Date
    {
        return $this->extra_attributes->date_id
            ? Date::find($this->extra_attributes->date_id)
            : null;
    }

    public function cartProductsUnderRequiredOrderMinimum(): Collection
    {
        $subtotal = $this->cartSubtotal();

        return $this->itemsInCart()
            ->map(function (Item $item) use ($subtotal) {
                $product_minimum = $item->product->setting('order_minimum');
                if (is_null($product_minimum) || $subtotal >= formatCurrencyForDB($product_minimum)) {
                    return null;
                }

                return $item->product;
            })
            ->filter()
            ->sortByDesc(fn (Product $product) => $product->setting('order_minimum'));
    }

    public function hasValidSubscription(): bool
    {
        $subscription = $this->cartSubscription();

        return ! is_null($subscription?->frequency)
            && (
                ! is_null($subscription->product_incentive_id)
                || ! app(SubscriptionSettingsService::class)->hasProductIncentive()
            );
    }

    public function hasContactInfo(): bool
    {
        $contact = $this->getContactInfo();

        return strlen($contact['first_name']) > 0
            && strlen($contact['last_name']) > 0
            && strlen($contact['phone']) > 0;
    }

    public function getContactInfo(): array
    {
        $customer = $this->cartCustomer();

        return $this->extra_attributes->get('contact') ?? [
            'first_name' => $customer->first_name ?? '',
            'last_name' => $customer->last_name ?? '',
            'email' => $customer->email ?? '',
            'phone' => $customer->phone ?? '',
            'save_for_later' => true,
            'opt_in_to_sms' => ! is_null($customer?->subscribed_to_sms_marketing_at),
            'subscribed_to_sms' => ! is_null($customer?->subscribed_to_sms_marketing_at),
        ];
    }

    public function hasShippingInfo(): bool
    {
        $shipping = $this->getShippingInfo();

        return strlen($shipping['street']) > 0
            && strlen($shipping['city']) > 0
            && strlen($shipping['state']) > 0
            && strlen($shipping['zip']) > 0;
    }

    public function hasSelectedBillingMethod(): bool
    {
        $billing_method = $this->cartBillingMethod();

        if (is_null($billing_method) || $billing_method->isDisabled()) {
            return false;
        }

        $billing_methods = $this->cartLocation()?->payment_methods;

        if (empty($billing_methods)) {
            $billing_methods = Payment::enabled()->pluck('id')->toArray();
        }

        return in_array($billing_method->id, $billing_methods);
    }

    public function cartBillingMethod(): ?Payment
    {
        $key = $this->extra_attributes->get('billing.method');

        if (is_null($key)) {
            return null;
        }

        return Payment::where('key', $key)->first();
    }

    public function cartBillingSource(): ?Card
    {
        $id = $this->extra_attributes->get('billing.source_id');

        if (is_null($id)) {
            return null;
        }

        return $this->cartCustomer()
            ?->cards()
            ->find($id);
    }

    public function confirmWithCart(\App\Cart\Cart $cart): Order
    {
        return app(ConfirmDatabaseCartWithCart::class)->handle($this, $cart);
    }

    public function cartMinimum(): int
    {
        return 0;
    }

    public function setContactInfo(array $contact_info): Cartable
    {
        $current_attributes = $this->getContactInfo();

        $this->extra_attributes->set(['contact' => [
            'first_name' => $contact_info['first_name'] ?? $current_attributes['first_name'],
            'last_name' => $contact_info['last_name'] ?? $current_attributes['last_name'],
            'email' => $current_attributes['email'],
            'phone' => $contact_info['phone'] ?? $current_attributes['phone'],
            'save_for_later' => $contact_info['save_for_later'] ?? $current_attributes['save_for_later'],
            'opt_in_to_sms' => $contact_info['opt_in_to_sms'] ?? $current_attributes['opt_in_to_sms'],
            'subscribed_to_sms' => $contact_info['subscribed_to_sms'] ?? $current_attributes['subscribed_to_sms'],
        ]]);

        $this->save();

        return $this;
    }

    public function setBillingInfo(array $billing_info): Cartable
    {
        $current_attributes = $this->getBillingInfo();

        $this->extra_attributes->set(['billing' => [
            'method' => $billing_info['method'] ?? $current_attributes['method'],
            'source_id' => $billing_info['source_id'] ?? $current_attributes['source_id'],
            'save_for_later' => $billing_info['save_for_later'] ?? $current_attributes['save_for_later'],
        ]]);

        $this->save();

        return $this;
    }

    public function getBillingInfo(): array
    {
        $customer = $this->cartCustomer();

        return $this->extra_attributes->get('billing') ?? [
            'method' => ! is_null($customer?->setting('default_payment_method'))
                ? (int) $customer->setting('default_payment_method')
                : null,
            'source_id' => $customer?->checkout_card_id,
            'save_for_later' => true,
        ];
    }

    public function getCustomerNotes(): ?string
    {
        return $this->extra_attributes->get('notes');
    }

    public function belongsInDeliveryZone(?string $zip = null, ?string $state = null): bool
    {
        $delivery_method = $this->cartLocation();

        if (is_null($delivery_method) || ! $delivery_method->isDeliveryZone()) {
            return true;
        }

        if (DB::table('pickup_zips')
            ->where([
                'pickup_id' => $delivery_method->id,
                'zip' => formatPostalCode($zip ?? $this->extra_attributes->get('shipping.zip')),
            ])
            ->exists()
        ) {
            return true;
        }

        if (DB::table('pickup_states')
            ->where([
                'pickup_id' => $delivery_method->id,
                'state' => trim($state ?? $this->extra_attributes->get('shipping.state')),
            ])
            ->exists()
        ) {
            return true;
        }

        return false;
    }

    public function confirm(array $params): Order
    {
        return app(ConfirmDatabaseCartWithParams::class)->handle($this, $params);
    }

    public function removeOutOfStockItems(bool $shouldUpdateQuantity = false): self
    {
        foreach ($this->cartItemsWithoutAvailableInventory() as $item) {
            /** @var array $item */
            $remaining_inventory = $item['inventory']['available'];
            if ($remaining_inventory === 0 || ! $shouldUpdateQuantity) {
                $this->removeCartItem($item['item']->id);
            } else {
                $this->updateCartItemQuantity($item['item']->id, $remaining_inventory);
            }
        }

        return $this;
    }

    public function cartItemsWithoutAvailableInventory(): Collection
    {
        return $this->itemsInCart()
            ->map(fn (Item $item) => [
                'item' => $item,
                'inventory' => [
                    'is_unlimited' => $item->product->doesNotTrackInventory(),
                    'available' => ! $item->product->doesNotTrackInventory()
                        ? $item->product->remainingInventory()
                        : null,
                ],

            ])
            ->filter(function (array $item) {
                return ! $item['inventory']['is_unlimited']
                    && ! is_infinite($item['inventory']['available'])
                    && ($item['inventory']['available'] - $item['item']->quantity) < 0;
            });
    }

    protected function casts(): array
    {
        return [
            'extra_attributes' => SchemalessAttributes::class,
        ];
    }
}
