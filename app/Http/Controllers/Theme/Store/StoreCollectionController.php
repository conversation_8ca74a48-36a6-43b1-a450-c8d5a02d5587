<?php

namespace App\Http\Controllers\Theme\Store;

use App\Http\Controllers\Controller;
use App\Models\Collection;
use App\Repositories\StoreRepository;
use Illuminate\Http\Request;

class StoreCollectionController extends Controller
{
    public function __construct()
    {
        $this->middleware(['ForceSeoStoreUrls']);
    }

    public function index(Request $request, string $collectionId)
    {
        session(['store_url' => $request->fullUrl()]);

        $repository = new StoreRepository($request);

        /** @var Collection|null $collection */
        $collection = $repository->getCollection($collectionId);

        if (is_null($collection)) {
            return redirect()->to('/store', 301);
        }

        $tags = $repository->tags($collection->products->pluck('id')->toArray(), $collectionId);
        $tag = $tags->where('slug', $request->get('tag'))->first();

        $products = $repository->getProductsInCollection($collection, $request)
            ->simplePaginate(setting('store_products_per_page', 50));

        $pageTitle = $collection->getMetaTitle();

        return view('theme::store.index')
            ->with([
                'collection' => $collection,
                'pageTitle' => $tag ? $pageTitle . ': ' . $tag->title : $pageTitle,
                'heading' => $collection->getPageHeading(),
                'subheading' => $collection->description,
                'pageDescription' => $collection->getMetaDescription($stripTags = true) ?? setting('store_page_description'),
                'footerDescription' => $collection['footer_description'] ? $collection['footer_description'] : null,
                'headerPhoto' => $collection['cover_photo'],
                'tags' => $tags,
                'robots' => $collection->robots(),
                'pageCanonical' => $collection->getCanonicalUrl(),
                'headTags' => $collection->getHeadTags(),
                'bodyTags' => $collection->getBodyTags(),
                'products' => $products,
            ]);
    }

    public function show(Request $request, string $slug)
    {
        session(['store_url' => $request->fullUrl()]);

        $repository = new StoreRepository($request);

        /** @var Collection|null $collection */
        $collection = $repository->getCollection($slug);

        if (is_null($collection)) {
            return redirect()->to('/store', 301);
        }

        $tags = $repository->tags($collection->products->pluck('id')->toArray(), $slug);
        $tag = $tags->where('slug', $request->get('tag'))->first();

        $products = $repository->getProductsInCollection($collection, $request)
            ->simplePaginate(setting('store_products_per_page', 50));


        $view = config('grazecart.new_storefront_enabled')
            ? 'theme::store.collections.new-show'
            : 'theme::store.collections.show';

        return view($view)
            ->with(compact('collection', 'tags', 'tag', 'products'));
    }
}
