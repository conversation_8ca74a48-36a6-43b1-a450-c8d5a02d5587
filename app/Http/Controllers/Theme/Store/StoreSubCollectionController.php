<?php

namespace App\Http\Controllers\Theme\Store;

use App\Http\Controllers\Controller;
use App\Models\Subcollection;
use App\Models\Tag;
use App\Repositories\StoreRepository;
use Illuminate\Http\Request;

class StoreSubCollectionController extends Controller
{
    public function index(Request $request, string $collectionId, string $tagSlug)
    {
        session(['store_url' => $request->fullUrl()]);

        $repository = new StoreRepository($request);
        $collection = $repository->getCollection($collectionId);

        if (is_null($collection)) {
            return redirect()->route(route: 'store.index', status: 301);
        }

        $tag = Tag::whereSlug($tagSlug)->first();

        if (is_null($tag)) {
            return redirect()->route('store.collection.legacy', [$collection->slug], 301);
        }

        $tags = $repository->tags($collection->products->pluck('id')->toArray(), $collectionId);

        $pageTitle = $collection->getMetaTitle();
        $heading = $collection->getPageHeading();
        $pageTitle = $pageTitle . ': ' . $tag->title;
        $heading = $heading . ': ' . $tag->title;
        $subheading = $collection->description;
        $pageDescription = $collection->getMetaDescription($stripTags = true) ?? setting('store_page_description');
        $footerDescription = $collection->footer_description ?? null;
        $headerPhoto = $collection->cover_photo ?? null;
        $robots = null;
        $pageCanonical = null;

        $sub_collection = Subcollection::whereTagId($tag->id)->whereCollectionId($collection->id)->first();

        if ( ! is_null($sub_collection)) {
            $pageTitle = $sub_collection->getMetaTitle();
            $heading = $sub_collection->settings->display_name ?? $sub_collection->title;
            $subheading = $sub_collection->description;
            $pageDescription = $sub_collection->getMetaDescription() ?? setting('store_page_description');
            $footerDescription = $sub_collection->footer_description ?? null;
            $headerPhoto = $sub_collection->cover_photo ?? null;
            $robots = $sub_collection->robots();
            $pageCanonical = $sub_collection->getCanonicalUrl();
        }

        if(empty($collection->seo_visibility)) {
            $robots = 'noindex, nofollow';
        }

        // Set the tag slug on the request so that it's used when filtering products
        $request['tag'] = $tag->slug;

        $products = $repository->getProductsInCollection($collection, $request)
            ->simplePaginate(setting('store_products_per_page', 50));


        return view('theme::store.index')
            ->with(compact(
                'pageTitle', 'heading', 'subheading', 'pageDescription',
                'footerDescription', 'headerPhoto', 'robots', 'pageCanonical', 'tags', 'products',
            ));
    }
}
