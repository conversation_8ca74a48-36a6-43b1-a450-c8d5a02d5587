<?php

namespace App\Http\Controllers\Theme\Checkout;

use App\Billing\Gateway\PaymentMethod;
use App\Http\Controllers\Controller;
use App\Livewire\Theme\FetchesCart;
use App\Models\Integration;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\Schedule;
use App\Services\SettingsService;
use App\Services\StoreService;
use Inertia\Inertia;

class ProductCheckoutController extends Controller
{
    use FetchesCart;

    public function __invoke(Product $product)
    {
        $delivery_method = null;

        if ($product->isGiftCard() && $product->isFulfilledVirtually()) {
            $delivery_method = Pickup::forGiftCards();
        }

        if (is_null($delivery_method)) {
            $delivery_method = app(StoreService::class)->deliveryMethod((int) request()->cookie('shopping_delivery_method_id'));
        }

        if (is_null($delivery_method) || ! $product->canBePurchasedIndividually()) {
            error('This item is unavailable for pre-order.');
            return redirect(route('store.index'));
        }

        $delivery_method->load('fees');

        if ($product->isGiftCard()) {
            return $this->handleGiftCardCheckout($delivery_method, $product);
        }

        return $this->handlePreOrderCheckout($delivery_method, $product);
    }

    private function handleGiftCardCheckout(Pickup $delivery_method, Product $product)
    {
        $user = auth()->user();

        $customer = $this->customerWithCards();

        $is_subscriber = $user->is_subscriber;

        $shipping_address = $user->defaultShippingAttributes();

        $cart = $this->fetchShopperCart(should_stub: false);
        if (!is_null($cart)) {
            $shipping_address = $cart->getShippingInfo();
        }

        $cart = array_merge(config('cart.gift-card'), [
            'delivery_method_id' => $delivery_method->id,
            'items' =>  [[
                'id' => 'gift-card-id',
                'product' => $product->toArray(),
                'quantity' => 1,
                'price' => $product->getUnitPrice(quantity: 1),
                'weight' => $product->weight
            ]],
            'customer' => [
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'email' => $user->email,
                'phone' => $user->phone,
                'save_for_later' => ! $is_subscriber,
                'opt_in_to_sms' => ! is_null($user->subscribed_to_sms_marketing_at),
                'subscribed_to_sms' => ! is_null($user->subscribed_to_sms_marketing_at),
            ],
            'shipping' => [
                'address_id' => $shipping_address['address_id'] ?? null,
                'street' => $shipping_address['street'],
                'street_2' => $shipping_address['street_2'],
                'city' => $shipping_address['city'],
                'state' => $shipping_address['state'],
                'zip' => $shipping_address['postal_code'] ?? $shipping_address['zip'],
                'country' => 'USA',
                'save_for_later' => ! $is_subscriber && ! empty($shipping_address['street'])
            ],
            'billing' => [
                'method' => null,
                'source_id' => $user->defaultCard()->value('source_id'),
                'save_for_later' => ! $is_subscriber
            ],
            'discounts' => array_merge(config('cart.gift-card.discounts'), [
                'store_credit' => [
                    'amount' => 0
                ]
            ])
        ]);

        $notes_placeholder = getMessage('checkout_step4');
        $available_payment_options = Payment::where('key', 'card')->get();

        $checkout_settings = [
            'payment_gateway' => [
                'public_key' => app(SettingsService::class)->stripePublicKey(),
                'environment' => app()->isProduction() ? 'live' : 'test',
            ],
            'available_payment_options' => $available_payment_options,
            'sms_opt_in_settings' => $this->smsOptInSettings(),
            'delivery_method' => $delivery_method,
            'product' => $product,
            'notes_placeholder' => $notes_placeholder,
            'quantity_limit' => $product->hasLimitPerCustomer() ? (int) $product->limitPerCustomer() : null,
            'require_shipping_address' => false,
            'require_card_payment' => true,
            'google_places_js_api_key' => config('services.google.places_js_api_key'),
        ];

        return Inertia::render('Checkout/GiftCard', compact(
            'cart',
            'product',
            'customer',
            'checkout_settings'
        ));
    }

    private function customerWithCards(): array
    {
        $user = request()->user();

        $customer = $user->only([
            'street',
            'street_2',
            'city',
            'state',
            'zip',
            'country',
            'subscribed_to_sms_marketing_at',
            'checkout_card_id',
            'exempt_from_fees',
            'exempt_from_taxes',
        ]);
        
        $customer['cards'] = $user->stripePaymentMethods()
            ->map(fn(PaymentMethod $card) => [
                'id' => $card->id,
                'brand' => $card->brand,
                'last_four' => $card->last_four,
                'exp_month' => $card->exp_month,
                'exp_year' => $card->exp_year,
            ]);

        return $customer;
    }

    private function smsOptInSettings(): array
    {
        return (new Integration)->smsOptInSettings('during_checkout');
    }

    private function handlePreOrderCheckout(Pickup $delivery_method, Product $product)
    {
        $user = auth()->user();
        $shipping_address = $user->defaultShippingAttributes();

        $cart = $this->fetchShopperCart(should_stub: false);
        if (!is_null($cart)) {
            $shipping_address = $cart->getShippingInfo();
        }

        /** @var Schedule $schedule */
        $schedule = $product->schedule ?? $delivery_method->schedule;

        $price_group_id = 0;

        if ($user->pricing_group_id) {
            $price_group_id = $user->pricing_group_id;
        } elseif ($delivery_method->pricing_group_id) {
            $price_group_id = $delivery_method->pricing_group_id;
        }

        $product->load([
            'price' => fn($q) => $q->where('group_id', $price_group_id)
        ]);

        $customer = $this->customerWithCards();

        $customer['addresses'] = $user->addresses()->get();

        $available_dates = $schedule->openDeliveryDates()
            ->map(fn(array $available_date) => [
                'id' => $available_date['id'],
                'date' => $available_date['date']->format('Y-m-d H:i:s')
            ]);
        $available_payment_options = $user->pickup->paymentMethods();
        $notes_placeholder = getMessage('checkout_step4');
        $delivery_date_tba_message = getMessage('order_status_bar_tba_unconfirmed');
        $is_subscriber = $user->is_subscriber;

        $cart = array_merge(config('cart.preorder'), [
            'date_id' => $available_dates->first()['id'] ?? null,
            'delivery_method_id' => $delivery_method->id,
            'items' =>  [[
                'id' => 'preorder-id',
                'product' => $product->toArray(),
                'quantity' => 1,
                'price' => $product->getPrice(),
                'weight' => $product->weight
            ]],
            'customer' => [
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'email' => $user->email,
                'phone' => $user->phone,
                'save_for_later' => ! $is_subscriber,
                'opt_in_to_sms' => ! is_null($user->subscribed_to_sms_marketing_at),
                'subscribed_to_sms' => ! is_null($user->subscribed_to_sms_marketing_at),
            ],
            'shipping' => session('cart.shipping') ?? [
                'address_id' => $shipping_address['address_id'] ?? null,
                'street' => $shipping_address['street'],
                'street_2' => $shipping_address['street_2'],
                'city' => $shipping_address['city'],
                'state' => $shipping_address['state'],
                'zip' => $shipping_address['postal_code'] ?? $shipping_address['zip'],
                'country' => $shipping_address['country'] ?? app(SettingsService::class)->farmCountry(),
                'save_for_later' => ! $is_subscriber && ! empty($shipping_address['street'])
            ],
            'billing' => [
                'method' => null,
                'source_id' => $user->defaultCard()->value('source_id'),
                'save_for_later' => ! $is_subscriber
            ],
            'discounts' => array_merge(config('cart.preorder.discounts'), [
                'store_credit' => [
                    'amount' => $user->credit
                ]
            ])
        ]);

        $checkout_settings = [
            'payment_gateway' => [
                'public_key' => app(SettingsService::class)->stripePublicKey(),
                'environment' => app()->isProduction() ? 'live' : 'test',
            ],
            'available_dates' => $available_dates,
            'available_payment_options' => $available_payment_options,
            'sms_opt_in_settings' => $this->smsOptInSettings(),
            'delivery_method' => $delivery_method,
            'product' => $product,
            'notes_placeholder' => $notes_placeholder,
            'quantity_limit' => $product->hasLimitPerCustomer() ? (int) $product->limitPerCustomer() : null,
            'require_shipping_address' => app(SettingsService::class)->requiresAddressAtCheckout(),
            'delivery_date_tba_message' => $delivery_date_tba_message,
            'google_places_js_api_key' => config('services.google.places_js_api_key'),
        ];

        return Inertia::render('Checkout/Preorder', compact(
            'cart',
            'schedule',
            'customer',
            'checkout_settings'
        ));
    }
}
