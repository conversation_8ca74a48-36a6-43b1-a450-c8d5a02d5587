<?php

namespace App\Http\Controllers\Admin\Product;

use App\Http\Controllers\Controller;
use App\Models\Product;

class ProductDescriptionController extends Controller
{
    public function update(Product $product)
    {
        $validated = request()->validate([
            'title' => ['sometimes', 'required'],
            'summary' => ['nullable', 'string', 'max:2000'],
            'description' => ['nullable', 'string'],
            'unit_description' => ['nullable', 'string'],
            'ingredients' => ['nullable', 'string'],
            'fulfillment_instructions' => ['nullable', 'string', 'max:255'],
            'settings' => ['nullable', 'array'],
            'settings.tagline' => ['nullable', 'string'],
        ], [
            'summary.max' => 'The :attribute must be below the character limit.',
        ]);

        $product->update($validated);

        return back();
    }
}
