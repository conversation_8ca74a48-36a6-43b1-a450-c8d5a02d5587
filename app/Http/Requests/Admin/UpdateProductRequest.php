<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\Request;
use Illuminate\Validation\Rule;

class UpdateProductRequest extends Request
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $regex = '/^[0-9,.]*$/';
        $id = $this->route()->parameter('product');

        return [
            'sku' => ['nullable', 'unique:products,sku,' . $id],
            'slug' => ['unique:products,slug,' . $id],
            'item_cost' => ['regex:' . $regex],
            'wholesale_unit_price' => ['regex:' . $regex],
            'inventory' => ['integer'],
            'stock_out_inventory' => ['integer'],
            'oos_threshold_inventory' => ['sometimes', 'nullable', 'integer'],
            'processor_inventory' => ['integer'],
            'other_inventory' => ['integer'],
            'unit_of_issue' => ['sometimes', 'nullable', 'required'],
            'unit_price' => ['sometimes', 'nullable', 'required', 'min:0', 'regex:' . $regex],
            'sale_unit_price' => ['nullable', 'min:0', 'regex:' . $regex],
            'weight' => ['sometimes', 'nullable', 'required', 'regex:' . $regex],
            'settings' => ['array'],
            'settings.order_minimum' => ['nullable', 'min:0', 'regex:' . $regex],
            'settings.cart_action_label' => ['nullable', 'max:25'],
            'settings.schedule_type' => ['nullable', 'in:actual,estimate'],
            'settings.confirmation_email_template_id' => ['nullable', 'exists:templates,id'],
            'settings.tagline' => ['nullable', 'string'],
            'schedule_id' => ['nullable', 'integer', 'exists:schedules,id'],
            'track_inventory' => ['nullable', Rule::in(['yes', 'no', 'bundle'])],
            'is_bundle' => ['nullable', 'boolean'],
            'back_order' => ['nullable', 'boolean',],
            'store_template' => ['nullable', 'string', 'max:255'],
        ];
    }

    public function messages(): array
    {
        return [
            'unit_price.required' => 'The price is required',
            'unit_price.regex' => 'The price must be a number',
            'sale_unit_price.regex' => 'The sale price must be a number',
            'weight.regex' => 'The weight must be a number',
            'sku.unique' => 'The product SKU must be unique. The provided SKU has already been used.',
            'inventory.integer' => 'Inventory must be an integer.',
            'stock_out_inventory.integer' => 'Low inventory threshold must be an integer.',
            'oos_threshold_inventory.integer' => 'O.O.S threshold must be an integer.',
            'processor_inventory.integer' => 'Processor inventory must be an integer.',
            'settings.order_minimum.regex' => 'Order minimum must be a positive number',
        ];
    }
}
