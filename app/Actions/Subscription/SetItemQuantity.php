<?php

namespace App\Actions\Subscription;

use App\Exceptions\BackOrderException;
use App\Exceptions\ProductNotFoundException;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use Exception;

class SetItemQuantity
{
    /**
     * @throws BackOrderException
     * @throws ProductNotFoundException
     * @throws Exception
     */
    public function handle(RecurringOrder $subscription, RecurringOrderItem $item, int $quantity, bool $force = false): ?RecurringOrderItem
    {
        // Prevent updating the qty of a promotional item.
        if ($item->isPromo()) {
            return $item;
        }

        return match (true) {
            $quantity < 1 => $this->handleRemoval($subscription, $item, $force),
            $quantity > $item->qty => $this->handleIncrease($item, $quantity, $force),
            $quantity < $item->qty => $this->handleDecrease($item, $quantity, $force),
            default => $item
        };
    }

    /**
     * @throws Exception
     */
    protected function handleRemoval(RecurringOrder $subscription, RecurringOrderItem $item, bool $force): ?RecurringOrderItem
    {
        if ($force) {
            $item->delete();
            return null;
        }

        if ($item->type === 'recurring' && $subscription->items()->where(['type' => 'recurring'])->count() === 1) {
            throw new Exception("The last recurring item of a subscription cannot be removed. Add a different item and try again.");
        }

        if (
            ($subscription->fulfillment->min_customer_orders ?? 0) > 0
            && $subscription->fulfillment->min_customer_orders > ($subscription->subtotal() - $item->subtotal())
        ) {
            throw new Exception('Cannot remove the item because the subscription subtotal would not meet the $'.money($subscription->fulfillment->min_customer_orders).' order minimum.');
        }

        $items_after_removal = $subscription->items()
            ->whereNot('id', $item['id'])
            ->get();

        /** @var Product|null $product_with_largest_minimum */
        $product_with_largest_minimum = $items_after_removal
            ->filter(fn(RecurringOrderItem $item) => $item->product->hasOrderMinimum())
            ->sortByDesc(fn(RecurringOrderItem $item) => $item->product->orderMinimum())
            ->first()
            ?->product;

        if ( ! is_null($product_with_largest_minimum)) {

            $new_subtotal = $items_after_removal
                ->sum(fn(RecurringOrderItem $subscription_item) => $subscription_item->subtotal());

            if ($new_subtotal < $product_with_largest_minimum->orderMinimum()) {
                throw new Exception('Your subtotal must be at least $' . money($product_with_largest_minimum->orderMinimum()) . ' to keep ' . $product_with_largest_minimum->title. ' to in your subscription.');
            }
        }

        $item->delete();

        return null;
    }

    /**
     * @throws BackOrderException
     * @throws ProductNotFoundException
     */
    protected function handleIncrease(RecurringOrderItem $item, int $quantity, bool $force): RecurringOrderItem
    {
        if ($force) {
            $item->qty = $quantity;
            $item->save();

            return $item;
        }

        $product = $item->product;

        if (is_null($product) || $product->trashed()) {
            throw new ProductNotFoundException("{$product->title} is no longer available. You can not add any more to your subscription.");
        }

        if ($product->hasLimitPerCustomer() && $quantity > $product->limitPerCustomer()) {
            throw new BackOrderException("There is a limit of {$product->limitPerCustomer()} per customer for this product.");
        }

        if ( ! $product->hasAmountAvailable($quantity)) {
            throw new BackOrderException($item->product->getOutOfStockMessage());
        }

        $item->qty = $quantity;
        $item->save();

        return $item;
    }

    /**
     * @throws Exception
     */
    protected function handleDecrease(RecurringOrderItem $item, int $desired_quantity, bool $force): RecurringOrderItem
    {
        $item->load(['product.price' => fn($q) => $q->where('group_id', $item->order->pricingGroupId() ?? 0)]);

        if ($force) {
            $item->qty -= ($item->qty - $desired_quantity);
            $item->save();

            return $item;
        }


        $updated_item_subtotal = $item->subtotal() - ($item->price() * $desired_quantity);
        $updated_subscription_subtotal = $item->order->subtotal() - $updated_item_subtotal;

        if (
            ($item->order->fulfillment->min_customer_orders ?? 0) > 0
            && $item->order->fulfillment->min_customer_orders > $updated_subscription_subtotal
        ) {
            throw new Exception('Cannot remove the item because the order total would not meet the $'.money($item->order->fulfillment->min_customer_orders).' order minimum.');
        }

        $items = $item->order->items()->get();

        /** @var Product|null $product_with_largest_minimum */
        $product_with_largest_minimum = $items
            ->filter(fn(RecurringOrderItem $item) => $item->product->hasOrderMinimum())
            ->sortByDesc(fn(RecurringOrderItem $item) => $item->product->orderMinimum())
            ->first()
            ?->product;

        if ( ! is_null($product_with_largest_minimum)) {
            $updated_subtotal = $items
                ->filter(fn(RecurringOrderItem $subscription_item) => $subscription_item->id !== $item->id)
                ->sum(fn(RecurringOrderItem $subscription_item) => $subscription_item->subtotal())
                + $updated_item_subtotal;

            if ($updated_subtotal < $product_with_largest_minimum->orderMinimum()) {
                throw new Exception('Your subtotal must be at least $' . money($product_with_largest_minimum->orderMinimum()) . ' to keep ' . $product_with_largest_minimum->title. ' to in your subscription.');
            }
        }

        $item->qty -= ($item->qty - $desired_quantity);
        $item->save();

        return $item;
    }
}
