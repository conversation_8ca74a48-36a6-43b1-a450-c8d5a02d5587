<?php

namespace App\Actions\Order;

use App\Exceptions\BackOrderException;
use App\Exceptions\ProductNotFoundException;
use App\Exceptions\QuantityAdjustmentException;
use App\Models\Event;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use Exception;

class SetItemQuantity
{
    /**
     * @throws BackOrderException|ProductNotFoundException|QuantityAdjustmentException
     */
    public function handle(Order $order, OrderItem $item, int $quantity, bool $force = false): ?OrderItem
    {
        if ($order->is_paid) {
            throw new BackOrderException('This order has already been paid for and cannot be updated.');
        }

        if ($quantity === $item->qty) {
            return $item;
        }

        $item = match (true) {
            $quantity < 1 => $this->handleRemoval($order, $item, $force),
            $quantity > $item->qty => $this->handleIncrease($order, $item, $quantity, $force),
            $quantity < $item->qty => $this->handleDecrease($order, $item, $quantity, $force),
            default => $item
        };

        $order->updateTotals();

        return $item;
    }

    /**
     * @throws QuantityAdjustmentException
     */
    protected function handleRemoval(Order $order, OrderItem $item, bool $force): ?OrderItem
    {
        if ($force) {
            $order->removeItem($item);
            return null;
        }

        if ($order->isConfirmed()) {
            if ($item->type === 'standard' && $order->items()->where('type', 'standard')->count() === 1) {
                throw new QuantityAdjustmentException("The last item of a confirmed order cannot be removed.");
            }

            if (($order->pickup->min_customer_orders ?? 0) > 0
                && $order->pickup->min_customer_orders > ($order->subtotal - $item->subtotal)) {
                throw new QuantityAdjustmentException('Removing this item would cause the subtotal to be below the $'.money($order->pickup->min_customer_orders).' order minimum.');
            }

            $items_after_removal = $order->items()
                ->whereNot('order_items.id', $item['id'])
                ->get();

            /** @var Product|null $product_with_largest_minimum */
            $product_with_largest_minimum = $items_after_removal
                ->filter(fn(OrderItem $item) => $item->product->hasOrderMinimum())
                ->sortByDesc(fn(OrderItem $item) => $item->product->orderMinimum())
                ->first()
                ?->product;

            if ( ! is_null($product_with_largest_minimum)) {

                $new_subtotal = $items_after_removal->sum('subtotal');

                if ($new_subtotal < $product_with_largest_minimum->orderMinimum()) {
                    throw new QuantityAdjustmentException('The confirmed order subtotal must be at least $' . money($product_with_largest_minimum->orderMinimum()) . ' to purchase ' . $product_with_largest_minimum->title. '.');
                }
            }
        }

        $order->removeItem($item);

        return null;
    }

    /**
     * @throws BackOrderException
     * @throws ProductNotFoundException
     */
    protected function handleIncrease(Order $order, OrderItem $item, int $quantity, bool $force): OrderItem
    {
        if ($item->type === 'promo') {
            return $item;
        }

        if ($force) {
            return $this->increaseQty($order, $item, $item->product, $quantity - $item->qty);
        }

        $product = $item->product;

        if (empty($product) || $product->trashed()) {
            throw new ProductNotFoundException("{$item->title} is no longer available. You can not add any more to your order.");
        }

        if ($item->product->hasLimitPerCustomer() && $quantity > $item->product->limitPerCustomer()) {
            throw new BackOrderException("There is a limit of {$item->product->limitPerCustomer()} per customer for this product.");
        }

        $amount = $quantity - $item->qty;

        if ( ! $item->product->canAddAmountToCart($amount, $order)) {
            throw new BackOrderException($item->product->getOutOfStockMessage());
        }

        return $this->increaseQty($order, $item, $item->product, $amount);
    }

    public function increaseQty(Order $order, OrderItem $item, Product $product, int $quantity_to_add): OrderItem
    {
        $old_quantity = $item->qty;

        $item->unit_of_issue = $product->unit_of_issue;
        $item->qty += $quantity_to_add;
        /**
         * TODO: REMOVE THE isFromBlueprint check
         *
         * The isFromBlueprint check exists because one-time order item fulfilled_qty
         * used to not be set correctly on the order item at checkout. However, along with
         * this change, one-time orders now have fulfilled_qty correctly set. Once enough time
         * has passed, all one-time order items should have the fulfilled_qty set and this
         * check can be removed.
         */
        $order->isFromBlueprint()
            ? $item->fulfilled_qty += $quantity_to_add
            : $item->fulfilled_qty = $item->qty;

        $item->stock_status = $order->isFromBlueprint()
            ? ($item->fulfilled_qty < $item->qty ? 'short' : 'full')
            : 'full';
        $item->weight = $product->weight * $item->qty;

        $item->updateSubtotal()->save();

        if ($order->isConfirmed()) {
            $product->decrementInventory($quantity_to_add);
            $this->recordEvent($order, $item, $old_quantity, $item->qty);
        }

        return $item;
    }

    private function recordEvent(Order $order, OrderItem $item, int $old_quantity, int $new_quantity): void
    {
        Event::create([
            'model_type' => Order::class,
            'model_id' => $order->id,
            'description' => "{$item->title} quantity changed from {$old_quantity} to {$new_quantity}",
            'event_id' => 'order_item_qty_updated',
            'user_id' => auth()->check() ? auth()->user()->id : 0,
            'created_at' => now(),
            'metadata' => json_encode([
                'item_id' => $item->id,
                'old_qty' => $old_quantity,
                'qty' => $new_quantity
            ])
        ]);
    }

    /**
     * @throws Exception
     */
    protected function handleDecrease(Order $order, OrderItem $item, int $desired_quantity, bool $force): OrderItem
    {
        if ($item->type === 'promo') {
            return $item;
        }

        if ($force) {
            return $this->decreaseQty($order, $item, $item->product, ($item->qty - $desired_quantity));
        }

        if ($order->isConfirmed()) {
            if ($item->isPricedByWeight()) {
                $updated_item_subtotal = round($item->unit_price * ($item->product->weight * $desired_quantity));
            } else {
                $updated_item_subtotal = $item->unit_price * $desired_quantity;
            }

            $updated_order_subtotal = $order->subtotal - ($item->subtotal - $updated_item_subtotal);

            if (
                (($order->pickup->min_customer_orders ?? 0) > 0)
                && $order->pickup->min_customer_orders > $updated_order_subtotal
            ) {
                throw new QuantityAdjustmentException('Decreasing the quantity this item would cause the subtotal to be below the $'.money($order->pickup->min_customer_orders).' order minimum.');
            }

            $items = $order->items()->get();

            /** @var Product|null $product_with_largest_minimum */
            $product_with_largest_minimum = $items
                ->filter(fn(OrderItem $item) => $item->product->hasOrderMinimum())
                ->sortByDesc(fn(OrderItem $item) => $item->product->orderMinimum())
                ->first()
                ?->product;

            if ( ! is_null($product_with_largest_minimum)) {
                $updated_subtotal = $items
                    ->filter(fn(OrderItem $order_item) => $order_item->id !== $item->id)
                    ->sum('subtotal')
                    + $updated_item_subtotal;

                if ($updated_subtotal < $product_with_largest_minimum->orderMinimum()) {
                    throw new QuantityAdjustmentException('The confirmed order subtotal must be at least $' . money($product_with_largest_minimum->orderMinimum()) . ' to purchase ' . $product_with_largest_minimum->title. '.');
                }
            }
        }

        return $this->decreaseQty($order, $item, $item->product, ($item->qty - $desired_quantity));
    }

    public function decreaseQty(Order $order, OrderItem $item, Product $product, int $quantity_to_remove): OrderItem
    {
        $old_quantity = $item->qty;

        $item->unit_of_issue = $product->unit_of_issue;
        $item->qty -= $quantity_to_remove;
        /**
         * TODO: REMOVE THE isFromBlueprint check
         *
         * The isFromBlueprint check exists because one-time order item fulfilled_qty
         * used to not be set correctly on the order item at checkout. However, along with
         * this change, one-time orders now have fulfilled_qty correctly set. Once enough time
         * has passed, all one-time order items should have the fulfilled_qty set and this
         * check can be removed.
         */
        $order->isFromBlueprint()
            ? $item->fulfilled_qty -= $quantity_to_remove
            : $item->fulfilled_qty = $item->qty;
        $item->stock_status = $order->isFromBlueprint()
            ? ($item->fulfilled_qty < $item->qty ? 'short' : 'full')
            : 'full';
        $item->weight = $product->weight * $item->qty;

        $item->updateSubtotal()->save();

        if ($order->isConfirmed()) {
            $product->incrementInventory($quantity_to_remove);
            $this->recordEvent($order, $item, $old_quantity, $item->qty);
        }

        return $item;
    }
}
