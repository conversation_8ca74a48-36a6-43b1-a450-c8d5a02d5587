<?php

namespace App\Actions\Order;

use App\Actions\Subscription\SyncSubscriptionDatetimes;
use App\Events\Subscription\SubscriptionWasSkipped;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\RecurringOrder;
use Carbon\Carbon;
use Exception;

class Skip
{
    /**
     * @throws Exception
     */
    public function handle(Order $order, Carbon $new_delivery_date): RecurringOrder
    {
        if ( ! $order->isFromBlueprint() || ! $order->blueprint) {
            throw new Exception("The order cannot be skipped because it is not recurring.");
        }

        $updated_subscription = app(SyncSubscriptionDatetimes::class)
            ->handle($order->blueprint, $new_delivery_date);

        $this->processSkip($order, $updated_subscription);

        event(new SubscriptionWasSkipped(
            subscription: $updated_subscription,
            old_delivery_date: $order->pickup_date,
            new_delivery_date: $updated_subscription->ready_at
        ));

        $order->delete();

        return $updated_subscription;
    }

    private function processSkip(Order $order, RecurringOrder $recurringOrder): void
    {
        $order->skipped_at = now();
        $order->cancel();

        $recurringOrder->skip_count++;
        $recurringOrder->save();

        $recurringOrder->customer->order_skip_count++;
        $recurringOrder->customer->save();

        $this->restoreAddonProducts($order, $recurringOrder);
        $this->mapOrderItemsToSubscriptionItems($order, $recurringOrder);
    }

    private function restoreAddonProducts(Order $order, RecurringOrder $recurringOrder): void
    {
        $order->items
            ->filter(fn(OrderItem $item) => $item->isAddonItem())
            ->each(fn(OrderItem $item) => $recurringOrder->items()->create([
                'customer_id' => $recurringOrder->customer_id,
                'product_id' => $item->product_id,
                'qty' => $item->qty,
                'type' => 'addon',
            ]));
    }

    private function mapOrderItemsToSubscriptionItems(Order $order, RecurringOrder $recurringOrder): void
    {
        $order->items
            ->reject(fn(OrderItem $item) => $item->unit_price === $item->product->getUnitPrice($item->quantity))
            ->each(fn(OrderItem $item) => $recurringOrder->items()
                ->where('product_id', $item->product_id)
                ->update(['unit_price_override' => $item->unit_price]));
    }
}
