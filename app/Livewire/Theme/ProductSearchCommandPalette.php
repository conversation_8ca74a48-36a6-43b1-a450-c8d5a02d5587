<?php

namespace App\Livewire\Theme;

use App\Models\Product;
use Livewire\Component;

class ProductSearchCommandPalette extends Component
{
    public $term = '';

    public array $products = [];

    public function render()
    {
        if ( ! empty($this->term)) {
            $this->products = Product::advancedSearch($this->term)
                ->take(6)
                ->get()
                ->each(fn(Product $product) => $product->setVisible([
                    'id',
                    'title',
                    'slug',
                    'settings',
                ]))
                ->toArray();
        }

        return view('theme::livewire.product-search-command-palette');
    }
}
