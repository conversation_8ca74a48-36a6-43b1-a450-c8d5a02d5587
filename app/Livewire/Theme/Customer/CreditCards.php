<?php

namespace App\Livewire\Theme\Customer;

use App\Actions\Billing\RemoveCard;
use App\Actions\Billing\SetDefaultCard;
use App\Livewire\Theme\SendsStorefrontNotifications;
use Livewire\Component;

class CreditCards extends Component
{
    use SendsStorefrontNotifications;

    public ?string $default_source_id;

    public function mount()
    {
        $this->setDefaultSource();
    }

    private function setDefaultSource()
    {
        $this->default_source_id =  auth()->user()
            ->defaultCard()
            ->value('source_id');
    }

    public function render()
    {
        $cards = auth()->user()->stripePaymentMethods();
        return view('theme::livewire.customer.credit-cards', compact('cards'));
    }

    public function placeholder()
    {
        return <<<'HTML'
        <div class="tw-w-full tw-flex tw-justify-center tw-py-6">
           <svg class="tw-animate-spin tw--ml-1 tw-mr-3 tw-h-5 tw-w-5 tw-text-gray-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        </div>
        HTML;
    }

    public function setAsDefault(string $source_id)
    {
        if ( ! $this->hasValidOrderStatus()) {
            $this->sendStorefrontNotification([
                'level' => 'error',
                'title' => 'Error!',
                'message' => 'The default card could not be updated!',
            ]);
            return;
        }

        $card = auth()->user()
            ->cards()
            ->where('source_id', $source_id)
            ->first();

        if ( ! $card) {
            $this->sendStorefrontNotification([
                'level' => 'error',
                'title' => 'Error!',
                'message' => 'The default card could not be updated!',
            ]);
            return;
        }

        try {
            app(SetDefaultCard::class)->handle($card);
        } catch (\Exception $exception) {
            $this->sendStorefrontNotification([
                'level' => 'error',
                'title' => 'Error!',
                'message' => 'The default card could not be updated.',
            ]);
            return;
        }

        $this->default_source_id = $source_id;
        $this->sendStorefrontNotification([
            'level' => 'success',
            'title' => 'Success!',
            'message' => 'Your default card has been updated!',
        ]);
    }

    private function hasValidOrderStatus(): bool
    {
        return auth()->user()->orders()->where('confirmed', true)->exists();
    }

    public function removeCard(string $source_id)
    {
        if ( ! $this->hasValidOrderStatus()) {
            $this->sendStorefrontNotification([
                'level' => 'error',
                'title' => 'Error!',
                'message' => 'The default card could not be updated!',
            ]);
            return;
        }

        $card = auth()->user()
            ->cards()
            ->where('source_id', $source_id)
            ->first();

        if (
            ! is_null($card)
            && auth()->user()
                ->orders()
                ->where('canceled', false)
                ->where('pickup_date', '>=', today())
                ->where('payment_source_id', $card->id)
                ->exists()
        ) {
            $this->sendStorefrontNotification([
                'level' => 'error',
                'title' => 'Error!',
                'message' => 'The card could not be deleted because it is used on an upcoming order.',
            ]);
            return;
        }

        if (
            auth()->user()
                ->recurringOrder()
                ->exists()
            && auth()->user()
                ->cards()
                ->count() === 1
        ) {
            $this->sendStorefrontNotification([
                'level' => 'error',
                'title' => 'Error!',
                'message' => 'The last card cannot be deleted while actively subscribed.',
            ]);
            return;
        }

        try {
            app(RemoveCard::class)->handle(auth()->user()->customer_id, $source_id);
        } catch (\Exception $exception) {
            $this->sendStorefrontNotification([
                'level' => 'error',
                'title' => 'Error!',
                'message' => 'The card could not be deleted.',
            ]);
            return;
        }

        $this->setDefaultSource();

        $this->sendStorefrontNotification([
            'level' => 'success',
            'title' => 'Card deleted!',
            'message' => 'The card has been deleted!',
        ]);
    }
}
