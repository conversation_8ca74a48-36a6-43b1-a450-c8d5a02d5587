<?php

namespace App\Livewire\Theme;

use App\Actions\Order\SyncItemToSubscription;
use App\Actions\Subscription\SyncItemToCurrentOrder;
use App\Contracts\CartService;
use App\Events\Cart\CartCreated;
use App\Events\Cart\CartUpdated;
use App\Events\Order\OrderUpdated;
use App\Events\Subscription\SubscriptionUpdated;
use App\Models\Order;
use App\Models\Product;
use App\Services\SubscriptionSettingsService;
use Exception;

trait AddsProduct
{
    use FetchesCart, FetchesOrder, FetchesSubscription, SendsStorefrontNotifications;

    public Product $product;

    public bool $has_subscription = false;
    public bool $has_order = false;

    public function addToCart(int $product_id, array $metadata = []): void
    {
        $cart = $this->fetchShopperCart(should_stub: false);

        if (is_null($cart)) {
            $shopper = request()->shopper();
            $cart = app(CartService::class)->create(
                shopper_type: $shopper['type'],
                shopper_id: $shopper['id']
            );
        }

        if ( ! ($cart->cartCustomer()?->isActive() ?? true)) {
            $this->dispatch('openModal', title: 'Unable to add product', message: 'Your account is currently inactive. Please contact us to activate your account.');
            return;
        }

        $product = $this->fetchProduct($product_id, $cart->cartPricingGroupId());

        if (is_null($product)) {
            $this->dispatch('openModal', title: 'Unable to add product', message: 'The product is not longer available');
            return;
        }

        $cart_was_started = $cart->cartIsEmpty();

        try {
            $item = $cart->addProduct($product);
        } catch (Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to add product', message: $exception->getMessage());
            return;
        }

        if (! $cart->isRecurring() && $cart->hasSubscriptionEligibleItems()) {
            $cart->setCartAsSubscriptionPurchase(
                frequency: 28,
                product_incentive_id: app(SubscriptionSettingsService::class)->defaultProductIncentiveId()
            );
        }

        if ($cart_was_started) {
            CartCreated::dispatch($cart);
        }

        $this->dispatch('item-added-to-cart', item: [
            'quantity' => 1,
            'product' => [
                'id' => $item->product->id,
                'title' => $item->product->title,
                'price' => $item->price()
            ]
        ], metadata: $metadata);

        event(new CartUpdated($cart));

        $this->dispatch('cartUpdated');

        $added_item = $cart->itemsInCart()
            ->firstWhere(fn($item) => $item->product->id === $product->id);
    }

    private function fetchProduct(int $product_id, ?int $pricing_group_id = null): ?Product
    {
        return Product::query()
            ->with([
                'price' => fn($q) => $q->where('group_id', $pricing_group_id ?? 0),
                'variants.price' => fn($q) => $q->where('group_id', $pricing_group_id ?? 0)
            ])
            ->find($product_id);
    }

    public function addToOrder(int $product_id): void
    {
        /** @var Order|null $order */
        $order = $this->fetchCustomerOrder();

        if (is_null($order)) {
            $this->dispatch('openModal', title: 'Unable to add product', message: 'The order modification deadline has passed.');
            return;
        }

        if (! $order->canBeModified()) {
            $this->dispatch('openModal', title: 'Unable to add product', message: 'The order is processing and is no longer editable.');
            return;
        }

        $product = $this->fetchProduct($product_id, $order->getPricingGroup());

        if (is_null($product)) {
            $this->dispatch('openModal', title: 'Unable to add product', message: 'The product is not longer available');
            return;
        }

        // Validate that product meets order minimum.
        $order_minimum = $product->setting('order_minimum', 0);

        if ($order->subtotal < formatCurrencyForDB($order_minimum)) {
            $this->dispatch('openModal', title: 'Unable to add product', message: 'Your order total must be at least $' . money(formatCurrencyForDB($order_minimum)) . ' to purchase ' . $product->title);
            return;
        }

        try {
            $item = $order->addItem($product, 1);
        } catch (Exception $e) {
            $this->dispatch('openModal', title: 'Unable to add product', message: $e->getMessage());
            return;
        }

        if ( ! is_null($order->blueprint) && $item->type !== 'addon') {
            app(SyncItemToSubscription::class)->handle($item);
        }

        $order->updateTotals();

        event(new OrderUpdated($order));

        $this->dispatch('orderUpdated');

        $this->sendStorefrontNotification([
            'level' => 'success',
            'title' => 'Order updated!',
            'message' => "<strong>{$item->product->title} ({$item->quantity})</strong> has been added to your order.",
        ]);
    }

    public function addToSubscription(int $product_id): void
    {
        $subscription = $this->fetchCustomerSubscription();

        $current_order = $subscription?->currentOrder;

        if ( ! is_null($current_order) && ! $current_order->deadlineHasPassed() && ! $current_order->isNew()) {
            $this->dispatch('openModal', title: 'Unable to add product', message: 'The order is processing and is no longer editable.');
            return;
        }

        $product = $this->fetchProduct($product_id, $subscription->pricingGroupId());

        if (is_null($product)) {
            $this->dispatch('openModal', title: 'Unable to add product', message: 'The product is not longer available');
            return;
        }

        // Validate that product meets order minimum.
        $order_minimum = $product->setting('order_minimum', 0);

        if ($subscription->subtotal() < formatCurrencyForDB($order_minimum)) {
            $this->dispatch('openModal', title: 'Unable to add product', message: 'Your order total must be at least $' . money(formatCurrencyForDB($order_minimum)) . ' to purchase ' . $product->title);
            return;
        }

        try {
            $subscription_item = $subscription->addItem($product);
        } catch (Exception $e) {
            $this->dispatch('openModal', title: 'Unable to add product', message: $e->getMessage());
            return;
        }

        event(new SubscriptionUpdated($subscription));

        if ( ! is_null($current_order) && ! $current_order->deadlineHasPassed()) {
            app(SyncItemToCurrentOrder::class)->handle($subscription_item);
        } else {
            $subscription_item->unit_price_override = $product->getUnitPrice($subscription_item->qty);
            $subscription_item->save();
        }

        $this->dispatch('subscriptionUpdated');

        $this->sendStorefrontNotification([
            'level' => 'success',
            'title' => 'Subscription updated!',
            'message' => "<strong>{$subscription_item->product->title} ({$subscription_item->qty})</strong> has been added to your subscription.",
        ]);
    }

    private function refreshProduct(): void
    {
        $pricing_group_id = match (true) {
            $this->has_subscription => $this->fetchCustomerSubscription()?->pricingGroupId(),
            $this->has_order => $this->fetchCustomerOrder()?->getPricingGroup(),
            default => $this->fetchShopperCart(false)?->cartPricingGroupId(),
        };

        $this->product->load([
            'defaultPrice',
            'prices',
            'price' => fn($q) => $q->where('group_id', $pricing_group_id ?? 0),
            'variants.price' => fn($q) => $q->where('group_id', $pricing_group_id ?? 0),
            'variants.defaultPrice'
        ]);
    }
}
