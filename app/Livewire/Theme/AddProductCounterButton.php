<?php

namespace App\Livewire\Theme;

use App\Cart\Item;
use App\Contracts\Cartable;
use App\Models\Order;
use Livewire\Component;

class AddProductCounterButton extends Component
{
    use AddsProduct, DecrementsProduct;

    public string $style;

    public ?string $cta_label = null;

    public ?string $cta_classes = null;

    public array $metadata = [];

    protected $listeners = ['productAddedToCart' => 'refresh'];

    public function mount()
    {
        $this->style = 'btn-brand';
    }

    public function render()
    {
        return view('theme::livewire.add-product-counter-button', [
            'count' => $this->count(),
        ]);
    }

    public function count(): int
    {
        return match(true) {
            $this->has_subscription => $this->subscriptionCount($this->product->id),
            $this->has_order => $this->orderCount($this->product->id),
            default => $this->cartCount($this->product->id),
        };
    }

    public function subscriptionCount(int $product_id): int
    {
        $subscription = $this->fetchCustomerSubscription();

        if (is_null($subscription)) {
            return 0;
        }

        $current_order = $subscription->currentOrder;

        if ( ! is_null($current_order)) {
            return $current_order->items()
                ->select(['order_items.qty'])
                ->firstWhere('order_items.product_id', $product_id)
                ->qty
                ?? 0;
        }

        return $subscription->items()
            ->select(['recurring_order_items.qty'])
            ->firstWhere('recurring_order_items.product_id', $product_id)
            ->qty
            ?? 0;
    }

    public function orderCount(int $product_id): int
    {
        /** @var Order|null $order */
        $order = $this->fetchCustomerOrder();

        if (is_null($order)) {
            return 0;
        }

        return $order->items()
            ->select(['order_items.qty'])
            ->firstWhere('order_items.product_id', $product_id)
            ->qty
            ?? 0;
    }

    public function cartCount(int $product_id): int
    {
        /** @var Cartable|null $cart */
        $cart = $this->fetchShopperCart(should_stub: false);

        if (is_null($cart)) {
            return 0;
        }

        return $cart->itemsInCart()
            ->firstWhere(fn(Item $item) => $item->product->id === $product_id)
            ->quantity
            ?? 0;
    }

    public function add()
    {
        if (auth()->guest()) {
            $this->dispatch('open-modal-register-and-add-product', product_id: $this->product->id, metadata: $this->metadata);
            return;
        }

        if (is_null(
            app(\App\Services\StoreService::class)->deliveryMethod((int) request()->cookie('shopping_delivery_method_id'))
        )) {
            $this->dispatch('open-modal-confirm-delivery-method', product_id: $this->product->id, metadata: $this->metadata);
            return;
        }

        if ($this->product->isPreOrder() || ($this->product->isGiftCard() && $this->product->isFulfilledVirtually())) {
            $this->redirect($this->product->checkoutPath());
            return;
        }

        match(true) {
            $this->has_subscription => $this->addToSubscription($this->product->id),
            $this->has_order => $this->addToOrder($this->product->id),
            default => $this->addToCartAndNotify()
        };
    }

    public function addToCartAndNotify()
    {
        $this->addToCart($this->product->id, $this->metadata);

        $this->fetchShopperCart(should_stub: false)?->itemCount() === 1
            ? $this->dispatch('openPanel', title: 'Shopping cart', component: 'theme.cart-side-panel')
            : $this->sendStorefrontNotification([
                'level' => 'success',
                'title' => 'Cart updated!',
                'message' => "<strong>{$this->product->title}</strong> has been added to your cart.",
                'duration' => 1000,
            ]);
    }

    public function decrement()
    {
        match(true) {
            $this->has_subscription => $this->decrementFromSubscription($this->product->id),
            $this->has_order => $this->decrementFromOrder($this->product->id),
            default => $this->decrementFromCartAndNotify(),
        };
    }

    public function decrementFromCartAndNotify()
    {
        $this->decrementFromCart($this->product->id, $this->metadata);

        $this->sendStorefrontNotification([
            'level' => 'success',
            'title' => 'Cart updated!',
            'message' => "<strong>{$this->product->title}</strong> quantity was updated.",
            'duration' => 1000,
        ]);
    }
}
