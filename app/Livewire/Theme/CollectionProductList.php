<?php

namespace App\Livewire\Theme;

use App\Models\Collection;
use App\Repositories\StoreRepository;
use Livewire\Attributes\Locked;
use Livewire\Component;

class CollectionProductList extends Component
{
    #[Locked]
    public Collection $collection;

    public $products = [];

    public int $page = 1;

    public bool $has_more = true;

    public int $total = 0;

    public string $type = 'standard';

    public ?string $url = null;

    public bool $show_about = false;

    public function mount()
    {
        $products = (new StoreRepository(request()))
            ->getProductsInCollection($this->collection)
            ->paginate(perPage: 8, page: $this->page);

        $this->products = collect($products->items());
        $this->page = $products->currentPage();
        $this->has_more = $products->hasMorePages();
        $this->total = $products->total();
    }

    public function render()
    {
        return view('theme::livewire.collection-product-list');
    }

    public function loadMoreProducts()
    {
        if (! $this->has_more) return;

        $this->page = $this->page + 1;

        $products = (new StoreRepository(request()))
            ->getProductsInCollection($this->collection)
            ->paginate(perPage: 8, page: $this->page);

        $this->has_more = $products->hasMorePages();
        $this->products = $this->products->concat($products->items());
    }

    public function placeholder(array $params = [])
    {
        return view('theme::livewire.placeholders.collection-product-list', $params);
    }
}
