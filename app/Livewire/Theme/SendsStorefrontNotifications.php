<?php

namespace App\Livewire\Theme;

trait SendsStorefrontNotifications
{
    public function sendStorefrontNotification(array $notification): void
    {
        $this->dispatch('storefront-notification-sent', notification: [
            'level' => $notification['level'] ?? 'success',
            'title' => $notification['title'] ?? 'Success!',
            'message' => $notification['message'] ?? '',
            'duration' => $notification['duration'] ?? 3000, // Default duration is 3 seconds
        ]);
    }
}
