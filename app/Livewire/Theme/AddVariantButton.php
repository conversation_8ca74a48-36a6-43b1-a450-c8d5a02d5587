<?php

namespace App\Livewire\Theme;

use App\Models\Product;
use Illuminate\Contracts\View\View as ViewContract;
use Livewire\Component;

class AddVariantButton extends Component
{
    use AddsProduct;

    public bool $open = false;
    public ?int $selected_variant_id = null;

    public string $style;
    public string $variant_layout = 'popper';

    public ?string $cta_label = null;
    
    public ?string $cta_classes = null;

    public array $metadata = [];

    public $listeners = ['cartDeliveryMethodUpdated'];

    public function mount()
    {
        $this->style = theme('store_button_style', 'btn-brand');

        $this->selected_variant_id = $this->product->id;

        if ($this->product->isOutOfStock()) {
            $this->selected_variant_id = $this->product
                ->variants
                ->first(fn(Product $variant) => ! $variant->isOutOfStock())
                ?->id;
        }
    }

    public function open(): void
    {
        $this->open = true;
    }

    public function render(): ViewContract
    {
        return view('theme::livewire.add-variant-button');
    }

    public function add()
    {
        if (is_null($this->selected_variant_id)) return;

        match(true) {
            $this->has_subscription => $this->addToSubscription($this->selected_variant_id),
            $this->has_order => $this->addToOrder($this->selected_variant_id),
            default => $this->addAndOpenCartPanel()
        };

        $this->open = false;
        $this->refreshProduct();
        $this->dispatch('close-modal-product-quickview');
    }

    private function addAndOpenCartPanel()
    {
        $this->addToCart($this->selected_variant_id, $this->metadata);

        $this->fetchShopperCart(should_stub: false)?->itemCount() === 1
            ? $this->dispatch('openPanel', title: 'Shopping cart', component: 'theme.cart-side-panel')
            : $this->sendStorefrontNotification([
                'level' => 'success',
                'title' => 'Cart updated!',
                'message' => "<strong>{$this->product->title}</strong> has been added to your cart.",
                'duration' => 1000,
            ]);
    }
}
