<?php

namespace App\Providers;

use App\Livewire\Theme\FetchesCart;
use App\Livewire\Theme\FetchesSubscription;
use App\Services\StoreService;
use App\Services\SubscriptionSettingsService;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class ViewServiceProvider extends ServiceProvider
{
    use FetchesCart, FetchesSubscription;

    public function boot(): void
    {
        view()->addNamespace('theme', resource_path("theme/resources/views"));

        view()->share('themeService', resolve('ThemeBuilder'));

        $subscription_service = app(SubscriptionSettingsService::class);
        $store_service = app(StoreService::class);

        View::composer([
            'theme::*', // front end theme(s)
            'settings.subscribe-and-save.*',
        ], function (\Illuminate\View\View $view) use ($subscription_service) {
            $view->with('subscription_settings_service', $subscription_service);
        });


        View::composer('theme::*', function (\Illuminate\View\View $view) use ($store_service) {
            $most_recent_subscription = $store_service->mostRecentSubscription();
            $view->with('has_subscription', !is_null($most_recent_subscription) && ! $most_recent_subscription->trashed());
            $view->with('has_inactive_subscription',  !is_null($most_recent_subscription) && $most_recent_subscription->trashed());
            $view->with('openOrder', $store_service->order());
            $view->with('cart', $store_service->cartOrCartStub());
        });

    }
}
