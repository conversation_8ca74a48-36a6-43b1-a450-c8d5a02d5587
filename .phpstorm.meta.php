<?php
/* @noinspection ALL */
// @formatter:off
// phpcs:ignoreFile

namespace PHPSTORM_META {

   /**
    * PhpStorm Meta file, to provide autocomplete information for PhpStorm
    *
    * <AUTHOR> vd. <PERSON>l <<EMAIL>>
    * @see https://github.com/barryvdh/laravel-ide-helper
    */
    override(new \Illuminate\Contracts\Container\Container, map([
        '' => '@',
            'App\Contracts\Billing' => \App\Billing\Stripe\StripeStandardAccount::class,
            'App\Contracts\CartService' => \App\Services\DatabaseCartService::class,
            'App\Contracts\Geocoder' => \App\Services\Geocoding\GoogleGeocoder::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Maatwebsite\Excel\Transactions\TransactionHandler' => \Maatwebsite\Excel\Transactions\DbTransactionHandler::class,
            'ThemeBuilder' => \App\ThemeBuilder\ClassicThemeBuilder::class,
            'ThemeService' => \App\Services\ThemeService::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Illuminate\Auth\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'bugsnag' => \Bugsnag\Client::class,
            'bugsnag.tracker' => \Bugsnag\BugsnagLaravel\Queue\Tracker::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\MySqlConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'disposable_email.domains' => \Propaganistas\LaravelDisposableEmail\DisposableDomains::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'excel' => \Maatwebsite\Excel\Excel::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\AwsS3V3Adapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'image' => \Intervention\Image\ImageManager::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'theme' => \App\Models\Theme::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\Illuminate\Container\Container::makeWith(0), map([
        '' => '@',
            'App\Contracts\Billing' => \App\Billing\Stripe\StripeStandardAccount::class,
            'App\Contracts\CartService' => \App\Services\DatabaseCartService::class,
            'App\Contracts\Geocoder' => \App\Services\Geocoding\GoogleGeocoder::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Maatwebsite\Excel\Transactions\TransactionHandler' => \Maatwebsite\Excel\Transactions\DbTransactionHandler::class,
            'ThemeBuilder' => \App\ThemeBuilder\ClassicThemeBuilder::class,
            'ThemeService' => \App\Services\ThemeService::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Illuminate\Auth\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'bugsnag' => \Bugsnag\Client::class,
            'bugsnag.tracker' => \Bugsnag\BugsnagLaravel\Queue\Tracker::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\MySqlConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'disposable_email.domains' => \Propaganistas\LaravelDisposableEmail\DisposableDomains::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'excel' => \Maatwebsite\Excel\Excel::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\AwsS3V3Adapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'image' => \Intervention\Image\ImageManager::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'theme' => \App\Models\Theme::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\Illuminate\Contracts\Container\Container::get(0), map([
        '' => '@',
            'App\Contracts\Billing' => \App\Billing\Stripe\StripeStandardAccount::class,
            'App\Contracts\CartService' => \App\Services\DatabaseCartService::class,
            'App\Contracts\Geocoder' => \App\Services\Geocoding\GoogleGeocoder::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Maatwebsite\Excel\Transactions\TransactionHandler' => \Maatwebsite\Excel\Transactions\DbTransactionHandler::class,
            'ThemeBuilder' => \App\ThemeBuilder\ClassicThemeBuilder::class,
            'ThemeService' => \App\Services\ThemeService::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Illuminate\Auth\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'bugsnag' => \Bugsnag\Client::class,
            'bugsnag.tracker' => \Bugsnag\BugsnagLaravel\Queue\Tracker::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\MySqlConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'disposable_email.domains' => \Propaganistas\LaravelDisposableEmail\DisposableDomains::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'excel' => \Maatwebsite\Excel\Excel::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\AwsS3V3Adapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'image' => \Intervention\Image\ImageManager::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'theme' => \App\Models\Theme::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\Illuminate\Contracts\Container\Container::make(0), map([
        '' => '@',
            'App\Contracts\Billing' => \App\Billing\Stripe\StripeStandardAccount::class,
            'App\Contracts\CartService' => \App\Services\DatabaseCartService::class,
            'App\Contracts\Geocoder' => \App\Services\Geocoding\GoogleGeocoder::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Maatwebsite\Excel\Transactions\TransactionHandler' => \Maatwebsite\Excel\Transactions\DbTransactionHandler::class,
            'ThemeBuilder' => \App\ThemeBuilder\ClassicThemeBuilder::class,
            'ThemeService' => \App\Services\ThemeService::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Illuminate\Auth\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'bugsnag' => \Bugsnag\Client::class,
            'bugsnag.tracker' => \Bugsnag\BugsnagLaravel\Queue\Tracker::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\MySqlConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'disposable_email.domains' => \Propaganistas\LaravelDisposableEmail\DisposableDomains::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'excel' => \Maatwebsite\Excel\Excel::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\AwsS3V3Adapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'image' => \Intervention\Image\ImageManager::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'theme' => \App\Models\Theme::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\Illuminate\Contracts\Container\Container::makeWith(0), map([
        '' => '@',
            'App\Contracts\Billing' => \App\Billing\Stripe\StripeStandardAccount::class,
            'App\Contracts\CartService' => \App\Services\DatabaseCartService::class,
            'App\Contracts\Geocoder' => \App\Services\Geocoding\GoogleGeocoder::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Maatwebsite\Excel\Transactions\TransactionHandler' => \Maatwebsite\Excel\Transactions\DbTransactionHandler::class,
            'ThemeBuilder' => \App\ThemeBuilder\ClassicThemeBuilder::class,
            'ThemeService' => \App\Services\ThemeService::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Illuminate\Auth\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'bugsnag' => \Bugsnag\Client::class,
            'bugsnag.tracker' => \Bugsnag\BugsnagLaravel\Queue\Tracker::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\MySqlConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'disposable_email.domains' => \Propaganistas\LaravelDisposableEmail\DisposableDomains::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'excel' => \Maatwebsite\Excel\Excel::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\AwsS3V3Adapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'image' => \Intervention\Image\ImageManager::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'theme' => \App\Models\Theme::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\App::get(0), map([
        '' => '@',
            'App\Contracts\Billing' => \App\Billing\Stripe\StripeStandardAccount::class,
            'App\Contracts\CartService' => \App\Services\DatabaseCartService::class,
            'App\Contracts\Geocoder' => \App\Services\Geocoding\GoogleGeocoder::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Maatwebsite\Excel\Transactions\TransactionHandler' => \Maatwebsite\Excel\Transactions\DbTransactionHandler::class,
            'ThemeBuilder' => \App\ThemeBuilder\ClassicThemeBuilder::class,
            'ThemeService' => \App\Services\ThemeService::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Illuminate\Auth\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'bugsnag' => \Bugsnag\Client::class,
            'bugsnag.tracker' => \Bugsnag\BugsnagLaravel\Queue\Tracker::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\MySqlConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'disposable_email.domains' => \Propaganistas\LaravelDisposableEmail\DisposableDomains::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'excel' => \Maatwebsite\Excel\Excel::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\AwsS3V3Adapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'image' => \Intervention\Image\ImageManager::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'theme' => \App\Models\Theme::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\App::make(0), map([
        '' => '@',
            'App\Contracts\Billing' => \App\Billing\Stripe\StripeStandardAccount::class,
            'App\Contracts\CartService' => \App\Services\DatabaseCartService::class,
            'App\Contracts\Geocoder' => \App\Services\Geocoding\GoogleGeocoder::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Maatwebsite\Excel\Transactions\TransactionHandler' => \Maatwebsite\Excel\Transactions\DbTransactionHandler::class,
            'ThemeBuilder' => \App\ThemeBuilder\ClassicThemeBuilder::class,
            'ThemeService' => \App\Services\ThemeService::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Illuminate\Auth\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'bugsnag' => \Bugsnag\Client::class,
            'bugsnag.tracker' => \Bugsnag\BugsnagLaravel\Queue\Tracker::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\MySqlConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'disposable_email.domains' => \Propaganistas\LaravelDisposableEmail\DisposableDomains::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'excel' => \Maatwebsite\Excel\Excel::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\AwsS3V3Adapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'image' => \Intervention\Image\ImageManager::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'theme' => \App\Models\Theme::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\App::makeWith(0), map([
        '' => '@',
            'App\Contracts\Billing' => \App\Billing\Stripe\StripeStandardAccount::class,
            'App\Contracts\CartService' => \App\Services\DatabaseCartService::class,
            'App\Contracts\Geocoder' => \App\Services\Geocoding\GoogleGeocoder::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Maatwebsite\Excel\Transactions\TransactionHandler' => \Maatwebsite\Excel\Transactions\DbTransactionHandler::class,
            'ThemeBuilder' => \App\ThemeBuilder\ClassicThemeBuilder::class,
            'ThemeService' => \App\Services\ThemeService::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Illuminate\Auth\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'bugsnag' => \Bugsnag\Client::class,
            'bugsnag.tracker' => \Bugsnag\BugsnagLaravel\Queue\Tracker::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\MySqlConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'disposable_email.domains' => \Propaganistas\LaravelDisposableEmail\DisposableDomains::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'excel' => \Maatwebsite\Excel\Excel::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\AwsS3V3Adapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'image' => \Intervention\Image\ImageManager::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'theme' => \App\Models\Theme::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\app(0), map([
        '' => '@',
            'App\Contracts\Billing' => \App\Billing\Stripe\StripeStandardAccount::class,
            'App\Contracts\CartService' => \App\Services\DatabaseCartService::class,
            'App\Contracts\Geocoder' => \App\Services\Geocoding\GoogleGeocoder::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Maatwebsite\Excel\Transactions\TransactionHandler' => \Maatwebsite\Excel\Transactions\DbTransactionHandler::class,
            'ThemeBuilder' => \App\ThemeBuilder\ClassicThemeBuilder::class,
            'ThemeService' => \App\Services\ThemeService::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Illuminate\Auth\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'bugsnag' => \Bugsnag\Client::class,
            'bugsnag.tracker' => \Bugsnag\BugsnagLaravel\Queue\Tracker::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\MySqlConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'disposable_email.domains' => \Propaganistas\LaravelDisposableEmail\DisposableDomains::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'excel' => \Maatwebsite\Excel\Excel::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\AwsS3V3Adapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'image' => \Intervention\Image\ImageManager::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'theme' => \App\Models\Theme::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\resolve(0), map([
        '' => '@',
            'App\Contracts\Billing' => \App\Billing\Stripe\StripeStandardAccount::class,
            'App\Contracts\CartService' => \App\Services\DatabaseCartService::class,
            'App\Contracts\Geocoder' => \App\Services\Geocoding\GoogleGeocoder::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Maatwebsite\Excel\Transactions\TransactionHandler' => \Maatwebsite\Excel\Transactions\DbTransactionHandler::class,
            'ThemeBuilder' => \App\ThemeBuilder\ClassicThemeBuilder::class,
            'ThemeService' => \App\Services\ThemeService::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Illuminate\Auth\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'bugsnag' => \Bugsnag\Client::class,
            'bugsnag.tracker' => \Bugsnag\BugsnagLaravel\Queue\Tracker::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\MySqlConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'disposable_email.domains' => \Propaganistas\LaravelDisposableEmail\DisposableDomains::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'excel' => \Maatwebsite\Excel\Excel::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\AwsS3V3Adapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'image' => \Intervention\Image\ImageManager::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'theme' => \App\Models\Theme::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\Psr\Container\ContainerInterface::get(0), map([
        '' => '@',
            'App\Contracts\Billing' => \App\Billing\Stripe\StripeStandardAccount::class,
            'App\Contracts\CartService' => \App\Services\DatabaseCartService::class,
            'App\Contracts\Geocoder' => \App\Services\Geocoding\GoogleGeocoder::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Maatwebsite\Excel\Transactions\TransactionHandler' => \Maatwebsite\Excel\Transactions\DbTransactionHandler::class,
            'ThemeBuilder' => \App\ThemeBuilder\ClassicThemeBuilder::class,
            'ThemeService' => \App\Services\ThemeService::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Illuminate\Auth\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'bugsnag' => \Bugsnag\Client::class,
            'bugsnag.tracker' => \Bugsnag\BugsnagLaravel\Queue\Tracker::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\MySqlConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'disposable_email.domains' => \Propaganistas\LaravelDisposableEmail\DisposableDomains::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'excel' => \Maatwebsite\Excel\Excel::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\AwsS3V3Adapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'image' => \Intervention\Image\ImageManager::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'theme' => \App\Models\Theme::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));

    override(\auth()->user(), map([
        '' => \App\Models\User::class,
    ]));
    override(\Illuminate\Contracts\Auth\Guard::user(), map([
        '' => \App\Models\User::class,
    ]));
    override(\Illuminate\Support\Facades\Auth::user(), map([
        '' => \App\Models\User::class,
    ]));
    override(\request()->user(), map([
        '' => \App\Models\User::class,
    ]));
    override(\Illuminate\Http\Request::user(), map([
        '' => \App\Models\User::class,
    ]));
    override(\Illuminate\Support\Facades\Request::user(), map([
        '' => \App\Models\User::class,
    ]));

    override(\config(), map([
            'app.name' => 'string',
            'app.env' => 'string',
            'app.debug' => 'boolean',
            'app.url' => 'string',
            'app.frontend_url' => 'string',
            'app.asset_url' => 'NULL',
            'app.timezone' => 'string',
            'app.locale' => 'string',
            'app.fallback_locale' => 'string',
            'app.faker_locale' => 'string',
            'app.cipher' => 'string',
            'app.key' => 'string',
            'app.previous_keys' => 'array',
            'app.maintenance.driver' => 'string',
            'app.maintenance.store' => 'string',
            'app.providers' => 'array',
            'app.aliases.App' => 'string',
            'app.aliases.Arr' => 'string',
            'app.aliases.Artisan' => 'string',
            'app.aliases.Auth' => 'string',
            'app.aliases.Blade' => 'string',
            'app.aliases.Broadcast' => 'string',
            'app.aliases.Bus' => 'string',
            'app.aliases.Cache' => 'string',
            'app.aliases.Concurrency' => 'string',
            'app.aliases.Config' => 'string',
            'app.aliases.Context' => 'string',
            'app.aliases.Cookie' => 'string',
            'app.aliases.Crypt' => 'string',
            'app.aliases.Date' => 'string',
            'app.aliases.DB' => 'string',
            'app.aliases.Eloquent' => 'string',
            'app.aliases.Event' => 'string',
            'app.aliases.File' => 'string',
            'app.aliases.Gate' => 'string',
            'app.aliases.Hash' => 'string',
            'app.aliases.Http' => 'string',
            'app.aliases.Js' => 'string',
            'app.aliases.Lang' => 'string',
            'app.aliases.Log' => 'string',
            'app.aliases.Mail' => 'string',
            'app.aliases.Notification' => 'string',
            'app.aliases.Number' => 'string',
            'app.aliases.Password' => 'string',
            'app.aliases.Process' => 'string',
            'app.aliases.Queue' => 'string',
            'app.aliases.RateLimiter' => 'string',
            'app.aliases.Redirect' => 'string',
            'app.aliases.Request' => 'string',
            'app.aliases.Response' => 'string',
            'app.aliases.Route' => 'string',
            'app.aliases.Schedule' => 'string',
            'app.aliases.Schema' => 'string',
            'app.aliases.Session' => 'string',
            'app.aliases.Storage' => 'string',
            'app.aliases.Str' => 'string',
            'app.aliases.URL' => 'string',
            'app.aliases.Uri' => 'string',
            'app.aliases.Validator' => 'string',
            'app.aliases.View' => 'string',
            'app.aliases.Vite' => 'string',
            'app.aliases.Bugsnag' => 'string',
            'app.aliases.Image' => 'string',
            'app.aliases.Inspiring' => 'string',
            'app.aliases.Redis' => 'string',
            'app.aliases.Socialite' => 'string',
            'app.domains.root' => 'string',
            'app.domains.admin' => 'NULL',
            'app.domains.api' => 'string',
            'app.deleted_user_email' => 'string',
            'app.inventory.spreadsheet_id' => 'string',
            'app.inventory.worksheet_name' => 'string',
            'app.homepage_featured_bundle_ids' => 'string',
            'auth.defaults.guard' => 'string',
            'auth.defaults.passwords' => 'string',
            'auth.guards.web.driver' => 'string',
            'auth.guards.web.provider' => 'string',
            'auth.guards.api.driver' => 'string',
            'auth.guards.api.provider' => 'string',
            'auth.guards.api.hash' => 'boolean',
            'auth.guards.sanctum.driver' => 'string',
            'auth.guards.sanctum.provider' => 'NULL',
            'auth.providers.users.driver' => 'string',
            'auth.providers.users.model' => 'string',
            'auth.passwords.users.provider' => 'string',
            'auth.passwords.users.table' => 'string',
            'auth.passwords.users.expire' => 'integer',
            'auth.passwords.users.throttle' => 'integer',
            'auth.password_timeout' => 'integer',
            'bugsnag.api_key' => 'string',
            'bugsnag.app_type' => 'NULL',
            'bugsnag.app_version' => 'NULL',
            'bugsnag.batch_sending' => 'NULL',
            'bugsnag.endpoint' => 'NULL',
            'bugsnag.filters' => 'NULL',
            'bugsnag.hostname' => 'NULL',
            'bugsnag.proxy' => 'array',
            'bugsnag.project_root' => 'NULL',
            'bugsnag.project_root_regex' => 'NULL',
            'bugsnag.strip_path' => 'NULL',
            'bugsnag.strip_path_regex' => 'NULL',
            'bugsnag.query' => 'boolean',
            'bugsnag.bindings' => 'boolean',
            'bugsnag.octane_breadcrumbs' => 'boolean',
            'bugsnag.release_stage' => 'NULL',
            'bugsnag.notify_release_stages' => 'array',
            'bugsnag.send_code' => 'boolean',
            'bugsnag.callbacks' => 'boolean',
            'bugsnag.user' => 'boolean',
            'bugsnag.logger_notify_level' => 'NULL',
            'bugsnag.auto_capture_sessions' => 'boolean',
            'bugsnag.session_endpoint' => 'NULL',
            'bugsnag.build_endpoint' => 'NULL',
            'bugsnag.discard_classes' => 'NULL',
            'bugsnag.redacted_keys' => 'NULL',
            'bugsnag.feature_flags' => 'array',
            'bugsnag.max_breadcrumbs' => 'NULL',
            'bugsnag.attach_hidden_context' => 'boolean',
            'cache.default' => 'string',
            'cache.stores.array.driver' => 'string',
            'cache.stores.array.serialize' => 'boolean',
            'cache.stores.database.driver' => 'string',
            'cache.stores.database.connection' => 'NULL',
            'cache.stores.database.table' => 'string',
            'cache.stores.database.lock_connection' => 'NULL',
            'cache.stores.database.lock_table' => 'NULL',
            'cache.stores.file.driver' => 'string',
            'cache.stores.file.path' => 'string',
            'cache.stores.file.lock_path' => 'string',
            'cache.stores.memcached.driver' => 'string',
            'cache.stores.memcached.persistent_id' => 'NULL',
            'cache.stores.memcached.sasl' => 'array',
            'cache.stores.memcached.options' => 'array',
            'cache.stores.memcached.servers.0.host' => 'string',
            'cache.stores.memcached.servers.0.port' => 'integer',
            'cache.stores.memcached.servers.0.weight' => 'integer',
            'cache.stores.redis.driver' => 'string',
            'cache.stores.redis.connection' => 'string',
            'cache.stores.redis.lock_connection' => 'string',
            'cache.stores.dynamodb.driver' => 'string',
            'cache.stores.dynamodb.key' => 'NULL',
            'cache.stores.dynamodb.secret' => 'NULL',
            'cache.stores.dynamodb.region' => 'string',
            'cache.stores.dynamodb.table' => 'string',
            'cache.stores.dynamodb.endpoint' => 'NULL',
            'cache.stores.octane.driver' => 'string',
            'cache.prefix' => 'string',
            'cache.limiter' => 'string',
            'canny.app_id' => 'string',
            'cart.default.type' => 'string',
            'cart.default.id' => 'NULL',
            'cart.default.purchase_type' => 'string',
            'cart.default.date_id' => 'NULL',
            'cart.default.delivery_method_id' => 'NULL',
            'cart.default.items' => 'object',
            'cart.default.subscription' => 'NULL',
            'cart.default.notes' => 'string',
            'cart.default.is_gift' => 'boolean',
            'cart.default.recipient_email' => 'string',
            'cart.default.recipient_notes' => 'string',
            'cart.default.discounts.coupons' => 'object',
            'cart.default.discounts.gift_card.name' => 'string',
            'cart.default.discounts.gift_card.code' => 'string',
            'cart.default.discounts.gift_card.amount' => 'integer',
            'cart.default.discounts.store_credit.amount' => 'integer',
            'cart.default.customer.id' => 'NULL',
            'cart.default.customer.first_name' => 'string',
            'cart.default.customer.last_name' => 'string',
            'cart.default.customer.email' => 'string',
            'cart.default.customer.phone' => 'string',
            'cart.default.customer.save_for_later' => 'boolean',
            'cart.default.customer.opt_in_to_sms' => 'boolean',
            'cart.default.customer.subscribed_to_sms' => 'boolean',
            'cart.default.shipping.address_id' => 'NULL',
            'cart.default.shipping.street' => 'string',
            'cart.default.shipping.street_2' => 'string',
            'cart.default.shipping.city' => 'string',
            'cart.default.shipping.state' => 'string',
            'cart.default.shipping.zip' => 'string',
            'cart.default.shipping.country' => 'string',
            'cart.default.shipping.save_for_later' => 'boolean',
            'cart.default.billing.method' => 'NULL',
            'cart.default.billing.source_id' => 'NULL',
            'cart.default.billing.save_for_later' => 'boolean',
            'cart.database.date_id' => 'NULL',
            'cart.database.delivery_method_id' => 'NULL',
            'cart.database.items' => 'array',
            'cart.database.subscription' => 'NULL',
            'cart.database.notes' => 'string',
            'cart.database.is_gift' => 'boolean',
            'cart.database.recipient_email' => 'string',
            'cart.database.recipient_notes' => 'string',
            'cart.database.discounts.coupons' => 'array',
            'cart.database.discounts.gift_card.name' => 'string',
            'cart.database.discounts.gift_card.code' => 'string',
            'cart.database.discounts.gift_card.amount' => 'integer',
            'cart.database.discounts.store_credit.amount' => 'integer',
            'cart.database.customer.id' => 'NULL',
            'cart.database.customer.first_name' => 'string',
            'cart.database.customer.last_name' => 'string',
            'cart.database.customer.email' => 'string',
            'cart.database.customer.phone' => 'string',
            'cart.database.customer.save_for_later' => 'boolean',
            'cart.database.customer.opt_in_to_sms' => 'boolean',
            'cart.database.customer.subscribed_to_sms' => 'boolean',
            'cart.database.shipping.address_id' => 'NULL',
            'cart.database.shipping.street' => 'string',
            'cart.database.shipping.street_2' => 'string',
            'cart.database.shipping.city' => 'string',
            'cart.database.shipping.state' => 'string',
            'cart.database.shipping.zip' => 'string',
            'cart.database.shipping.country' => 'string',
            'cart.database.shipping.save_for_later' => 'boolean',
            'cart.database.billing.method' => 'NULL',
            'cart.database.billing.source_id' => 'NULL',
            'cart.database.billing.save_for_later' => 'boolean',
            'cart.preorder.type' => 'string',
            'cart.preorder.date_id' => 'NULL',
            'cart.preorder.delivery_method_id' => 'NULL',
            'cart.preorder.items' => 'object',
            'cart.preorder.notes' => 'string',
            'cart.preorder.is_gift' => 'boolean',
            'cart.preorder.recipient_email' => 'string',
            'cart.preorder.recipient_notes' => 'string',
            'cart.preorder.discounts.coupons' => 'object',
            'cart.preorder.discounts.gift_card.name' => 'string',
            'cart.preorder.discounts.gift_card.code' => 'string',
            'cart.preorder.discounts.gift_card.amount' => 'integer',
            'cart.preorder.discounts.store_credit.amount' => 'integer',
            'cart.preorder.customer.id' => 'NULL',
            'cart.preorder.customer.first_name' => 'string',
            'cart.preorder.customer.last_name' => 'string',
            'cart.preorder.customer.email' => 'string',
            'cart.preorder.customer.phone' => 'string',
            'cart.preorder.customer.save_for_later' => 'boolean',
            'cart.preorder.customer.opt_in_to_sms' => 'boolean',
            'cart.preorder.customer.subscribed_to_sms' => 'boolean',
            'cart.preorder.shipping.street' => 'string',
            'cart.preorder.shipping.street_2' => 'string',
            'cart.preorder.shipping.city' => 'string',
            'cart.preorder.shipping.state' => 'string',
            'cart.preorder.shipping.zip' => 'string',
            'cart.preorder.shipping.country' => 'string',
            'cart.preorder.shipping.save_for_later' => 'boolean',
            'cart.preorder.billing.method' => 'NULL',
            'cart.preorder.billing.source_id' => 'NULL',
            'cart.preorder.billing.save_for_later' => 'boolean',
            'cart.gift-card.type' => 'string',
            'cart.gift-card.items' => 'object',
            'cart.gift-card.notes' => 'string',
            'cart.gift-card.discounts.coupons' => 'object',
            'cart.gift-card.discounts.gift_card.name' => 'string',
            'cart.gift-card.discounts.gift_card.code' => 'string',
            'cart.gift-card.discounts.gift_card.amount' => 'integer',
            'cart.gift-card.discounts.store_credit.amount' => 'integer',
            'cart.gift-card.customer.id' => 'NULL',
            'cart.gift-card.customer.first_name' => 'string',
            'cart.gift-card.customer.last_name' => 'string',
            'cart.gift-card.customer.email' => 'string',
            'cart.gift-card.customer.phone' => 'string',
            'cart.gift-card.customer.save_for_later' => 'boolean',
            'cart.gift-card.customer.opt_in_to_sms' => 'boolean',
            'cart.gift-card.customer.subscribed_to_sms' => 'boolean',
            'cart.gift-card.shipping.street' => 'string',
            'cart.gift-card.shipping.street_2' => 'string',
            'cart.gift-card.shipping.city' => 'string',
            'cart.gift-card.shipping.state' => 'string',
            'cart.gift-card.shipping.zip' => 'string',
            'cart.gift-card.shipping.country' => 'string',
            'cart.gift-card.shipping.save_for_later' => 'boolean',
            'cart.gift-card.billing.method' => 'string',
            'cart.gift-card.billing.source_id' => 'NULL',
            'cart.gift-card.billing.save_for_later' => 'boolean',
            'clockwork.enable' => 'NULL',
            'clockwork.collect_data_always' => 'boolean',
            'clockwork.storage' => 'string',
            'clockwork.storage_files_path' => 'string',
            'clockwork.storage_sql_database' => 'string',
            'clockwork.storage_sql_table' => 'string',
            'clockwork.filter' => 'array',
            'clockwork.filter_uris' => 'array',
            'clockwork.additional_data_sources' => 'array',
            'clockwork.headers' => 'array',
            'compile.files' => 'array',
            'compile.providers' => 'array',
            'cors.paths' => 'array',
            'cors.allowed_methods' => 'array',
            'cors.allowed_origins' => 'array',
            'cors.allowed_origins_patterns' => 'array',
            'cors.allowed_headers' => 'array',
            'cors.exposed_headers' => 'array',
            'cors.max_age' => 'integer',
            'cors.supports_credentials' => 'boolean',
            'database.default' => 'string',
            'database.connections.sqlite.driver' => 'string',
            'database.connections.sqlite.url' => 'NULL',
            'database.connections.sqlite.database' => 'string',
            'database.connections.sqlite.prefix' => 'string',
            'database.connections.sqlite.foreign_key_constraints' => 'boolean',
            'database.connections.sqlite.busy_timeout' => 'NULL',
            'database.connections.sqlite.journal_mode' => 'NULL',
            'database.connections.sqlite.synchronous' => 'NULL',
            'database.connections.mysql.driver' => 'string',
            'database.connections.mysql.url' => 'NULL',
            'database.connections.mysql.host' => 'string',
            'database.connections.mysql.port' => 'string',
            'database.connections.mysql.database' => 'string',
            'database.connections.mysql.username' => 'string',
            'database.connections.mysql.password' => 'string',
            'database.connections.mysql.unix_socket' => 'string',
            'database.connections.mysql.charset' => 'string',
            'database.connections.mysql.collation' => 'string',
            'database.connections.mysql.prefix' => 'string',
            'database.connections.mysql.prefix_indexes' => 'boolean',
            'database.connections.mysql.strict' => 'boolean',
            'database.connections.mysql.engine' => 'NULL',
            'database.connections.mysql.options' => 'array',
            'database.connections.mariadb.driver' => 'string',
            'database.connections.mariadb.url' => 'NULL',
            'database.connections.mariadb.host' => 'string',
            'database.connections.mariadb.port' => 'string',
            'database.connections.mariadb.database' => 'string',
            'database.connections.mariadb.username' => 'string',
            'database.connections.mariadb.password' => 'string',
            'database.connections.mariadb.unix_socket' => 'string',
            'database.connections.mariadb.charset' => 'string',
            'database.connections.mariadb.collation' => 'string',
            'database.connections.mariadb.prefix' => 'string',
            'database.connections.mariadb.prefix_indexes' => 'boolean',
            'database.connections.mariadb.strict' => 'boolean',
            'database.connections.mariadb.engine' => 'NULL',
            'database.connections.mariadb.options' => 'array',
            'database.connections.pgsql.driver' => 'string',
            'database.connections.pgsql.url' => 'NULL',
            'database.connections.pgsql.host' => 'string',
            'database.connections.pgsql.port' => 'string',
            'database.connections.pgsql.database' => 'string',
            'database.connections.pgsql.username' => 'string',
            'database.connections.pgsql.password' => 'string',
            'database.connections.pgsql.charset' => 'string',
            'database.connections.pgsql.prefix' => 'string',
            'database.connections.pgsql.prefix_indexes' => 'boolean',
            'database.connections.pgsql.search_path' => 'string',
            'database.connections.pgsql.sslmode' => 'string',
            'database.connections.sqlsrv.driver' => 'string',
            'database.connections.sqlsrv.url' => 'NULL',
            'database.connections.sqlsrv.host' => 'string',
            'database.connections.sqlsrv.port' => 'string',
            'database.connections.sqlsrv.database' => 'string',
            'database.connections.sqlsrv.username' => 'string',
            'database.connections.sqlsrv.password' => 'string',
            'database.connections.sqlsrv.charset' => 'string',
            'database.connections.sqlsrv.prefix' => 'string',
            'database.connections.sqlsrv.prefix_indexes' => 'boolean',
            'database.connections.failed_jobs.driver' => 'string',
            'database.connections.failed_jobs.host' => 'string',
            'database.connections.failed_jobs.port' => 'string',
            'database.connections.failed_jobs.database' => 'string',
            'database.connections.failed_jobs.username' => 'string',
            'database.connections.failed_jobs.password' => 'string',
            'database.connections.failed_jobs.charset' => 'string',
            'database.connections.failed_jobs.collation' => 'string',
            'database.connections.failed_jobs.prefix' => 'string',
            'database.connections.failed_jobs.strict' => 'boolean',
            'database.migrations.table' => 'string',
            'database.migrations.update_date_on_publish' => 'boolean',
            'database.redis.client' => 'string',
            'database.redis.options.cluster' => 'string',
            'database.redis.options.prefix' => 'string',
            'database.redis.default.url' => 'NULL',
            'database.redis.default.host' => 'string',
            'database.redis.default.username' => 'NULL',
            'database.redis.default.password' => 'NULL',
            'database.redis.default.port' => 'string',
            'database.redis.default.database' => 'string',
            'database.redis.default.read_write_timeout' => 'integer',
            'database.redis.cache.url' => 'NULL',
            'database.redis.cache.host' => 'string',
            'database.redis.cache.username' => 'NULL',
            'database.redis.cache.password' => 'NULL',
            'database.redis.cache.port' => 'string',
            'database.redis.cache.database' => 'string',
            'database.redis.cache.read_write_timeout' => 'integer',
            'database.redis.horizon.url' => 'NULL',
            'database.redis.horizon.host' => 'string',
            'database.redis.horizon.username' => 'NULL',
            'database.redis.horizon.password' => 'NULL',
            'database.redis.horizon.port' => 'string',
            'database.redis.horizon.database' => 'string',
            'database.redis.horizon.read_write_timeout' => 'integer',
            'database.redis.horizon.options.prefix' => 'string',
            'disposable-email.sources' => 'array',
            'disposable-email.fetcher' => 'string',
            'disposable-email.storage' => 'string',
            'disposable-email.whitelist' => 'array',
            'disposable-email.include_subdomains' => 'boolean',
            'disposable-email.cache.enabled' => 'boolean',
            'disposable-email.cache.store' => 'string',
            'disposable-email.cache.key' => 'string',
            'eloquentfilter.namespace' => 'string',
            'eloquentfilter.paginate_limit' => 'integer',
            'excel.exports.chunk_size' => 'integer',
            'excel.exports.pre_calculate_formulas' => 'boolean',
            'excel.exports.strict_null_comparison' => 'boolean',
            'excel.exports.csv.delimiter' => 'string',
            'excel.exports.csv.enclosure' => 'string',
            'excel.exports.csv.line_ending' => 'string',
            'excel.exports.csv.use_bom' => 'boolean',
            'excel.exports.csv.include_separator_line' => 'boolean',
            'excel.exports.csv.excel_compatibility' => 'boolean',
            'excel.exports.csv.output_encoding' => 'string',
            'excel.exports.properties.creator' => 'string',
            'excel.exports.properties.lastModifiedBy' => 'string',
            'excel.exports.properties.title' => 'string',
            'excel.exports.properties.description' => 'string',
            'excel.exports.properties.subject' => 'string',
            'excel.exports.properties.keywords' => 'string',
            'excel.exports.properties.category' => 'string',
            'excel.exports.properties.manager' => 'string',
            'excel.exports.properties.company' => 'string',
            'excel.imports.read_only' => 'boolean',
            'excel.imports.ignore_empty' => 'boolean',
            'excel.imports.heading_row.formatter' => 'string',
            'excel.imports.csv.delimiter' => 'NULL',
            'excel.imports.csv.enclosure' => 'string',
            'excel.imports.csv.escape_character' => 'string',
            'excel.imports.csv.contiguous' => 'boolean',
            'excel.imports.csv.input_encoding' => 'string',
            'excel.imports.properties.creator' => 'string',
            'excel.imports.properties.lastModifiedBy' => 'string',
            'excel.imports.properties.title' => 'string',
            'excel.imports.properties.description' => 'string',
            'excel.imports.properties.subject' => 'string',
            'excel.imports.properties.keywords' => 'string',
            'excel.imports.properties.category' => 'string',
            'excel.imports.properties.manager' => 'string',
            'excel.imports.properties.company' => 'string',
            'excel.extension_detector.xlsx' => 'string',
            'excel.extension_detector.xlsm' => 'string',
            'excel.extension_detector.xltx' => 'string',
            'excel.extension_detector.xltm' => 'string',
            'excel.extension_detector.xls' => 'string',
            'excel.extension_detector.xlt' => 'string',
            'excel.extension_detector.ods' => 'string',
            'excel.extension_detector.ots' => 'string',
            'excel.extension_detector.slk' => 'string',
            'excel.extension_detector.xml' => 'string',
            'excel.extension_detector.gnumeric' => 'string',
            'excel.extension_detector.htm' => 'string',
            'excel.extension_detector.html' => 'string',
            'excel.extension_detector.csv' => 'string',
            'excel.extension_detector.tsv' => 'string',
            'excel.extension_detector.pdf' => 'string',
            'excel.value_binder.default' => 'string',
            'excel.cache.driver' => 'string',
            'excel.cache.batch.memory_limit' => 'integer',
            'excel.cache.illuminate.store' => 'NULL',
            'excel.transactions.handler' => 'string',
            'excel.transactions.db.connection' => 'NULL',
            'excel.temporary_files.local_path' => 'string',
            'excel.temporary_files.remote_disk' => 'NULL',
            'excel.temporary_files.remote_prefix' => 'NULL',
            'excel.temporary_files.force_resync_remote' => 'NULL',
            'filesystems.default' => 'string',
            'filesystems.disks.local.driver' => 'string',
            'filesystems.disks.local.root' => 'string',
            'filesystems.disks.local.serve' => 'boolean',
            'filesystems.disks.local.throw' => 'boolean',
            'filesystems.disks.local.report' => 'boolean',
            'filesystems.disks.public.driver' => 'string',
            'filesystems.disks.public.root' => 'string',
            'filesystems.disks.public.url' => 'string',
            'filesystems.disks.public.visibility' => 'string',
            'filesystems.disks.public.throw' => 'boolean',
            'filesystems.disks.public.report' => 'boolean',
            'filesystems.disks.s3.driver' => 'string',
            'filesystems.disks.s3.key' => 'string',
            'filesystems.disks.s3.secret' => 'string',
            'filesystems.disks.s3.region' => 'string',
            'filesystems.disks.s3.bucket' => 'string',
            'filesystems.disks.s3.options.CacheControl' => 'string',
            'filesystems.disks.s3.url' => 'NULL',
            'filesystems.disks.s3.endpoint' => 'NULL',
            'filesystems.disks.s3.use_path_style_endpoint' => 'boolean',
            'filesystems.disks.s3.throw' => 'boolean',
            'filesystems.disks.tenants.driver' => 'string',
            'filesystems.disks.tenants.root' => 'string',
            'filesystems.disks.client.driver' => 'string',
            'filesystems.disks.client.root' => 'string',
            'filesystems.disks.pages.driver' => 'string',
            'filesystems.disks.pages.root' => 'NULL',
            'filesystems.disks.seeds.driver' => 'string',
            'filesystems.disks.seeds.root' => 'string',
            'filesystems.disks.stubs.driver' => 'string',
            'filesystems.disks.stubs.root' => 'string',
            'filesystems.links./Users/<USER>/Documents/GitHub/sevensonsfarms/public/storage' => 'string',
            'filesystems.file_upload_prefix' => 'string',
            'filesystems.cloud' => 'string',
            'grazecart.sale_keywords' => 'array',
            'grazecart.default_map_center' => 'array',
            'grazecart.countries.USA' => 'string',
            'grazecart.countries.Canada' => 'string',
            'grazecart.states.AL' => 'string',
            'grazecart.states.AK' => 'string',
            'grazecart.states.AZ' => 'string',
            'grazecart.states.AR' => 'string',
            'grazecart.states.CA' => 'string',
            'grazecart.states.CO' => 'string',
            'grazecart.states.CT' => 'string',
            'grazecart.states.DE' => 'string',
            'grazecart.states.DC' => 'string',
            'grazecart.states.FL' => 'string',
            'grazecart.states.GA' => 'string',
            'grazecart.states.HI' => 'string',
            'grazecart.states.ID' => 'string',
            'grazecart.states.IL' => 'string',
            'grazecart.states.IN' => 'string',
            'grazecart.states.IA' => 'string',
            'grazecart.states.KS' => 'string',
            'grazecart.states.KY' => 'string',
            'grazecart.states.LA' => 'string',
            'grazecart.states.ME' => 'string',
            'grazecart.states.MD' => 'string',
            'grazecart.states.MA' => 'string',
            'grazecart.states.MI' => 'string',
            'grazecart.states.MN' => 'string',
            'grazecart.states.MS' => 'string',
            'grazecart.states.MO' => 'string',
            'grazecart.states.MT' => 'string',
            'grazecart.states.NE' => 'string',
            'grazecart.states.NV' => 'string',
            'grazecart.states.NH' => 'string',
            'grazecart.states.NJ' => 'string',
            'grazecart.states.NM' => 'string',
            'grazecart.states.NY' => 'string',
            'grazecart.states.NC' => 'string',
            'grazecart.states.ND' => 'string',
            'grazecart.states.OH' => 'string',
            'grazecart.states.OK' => 'string',
            'grazecart.states.OR' => 'string',
            'grazecart.states.PA' => 'string',
            'grazecart.states.RI' => 'string',
            'grazecart.states.SC' => 'string',
            'grazecart.states.SD' => 'string',
            'grazecart.states.TN' => 'string',
            'grazecart.states.TX' => 'string',
            'grazecart.states.UT' => 'string',
            'grazecart.states.VT' => 'string',
            'grazecart.states.VA' => 'string',
            'grazecart.states.WA' => 'string',
            'grazecart.states.WV' => 'string',
            'grazecart.states.WI' => 'string',
            'grazecart.states.WY' => 'string',
            'grazecart.states.PR' => 'string',
            'grazecart.provinces.NL' => 'string',
            'grazecart.provinces.PE' => 'string',
            'grazecart.provinces.NS' => 'string',
            'grazecart.provinces.NB' => 'string',
            'grazecart.provinces.QB' => 'string',
            'grazecart.provinces.ON' => 'string',
            'grazecart.provinces.MB' => 'string',
            'grazecart.provinces.SK' => 'string',
            'grazecart.provinces.AB' => 'string',
            'grazecart.provinces.BC' => 'string',
            'grazecart.provinces.YT' => 'string',
            'grazecart.provinces.NT' => 'string',
            'grazecart.provinces.NU' => 'string',
            'grazecart.referrals.none-selected' => 'string',
            'grazecart.referrals.Stockman' => 'string',
            'grazecart.referrals.Dirt to Soil' => 'string',
            'grazecart.referrals.3 Cow Marketing' => 'string',
            'grazecart.referrals.Graze Magazine' => 'string',
            'grazecart.referrals.IndependentProcessor' => 'string',
            'grazecart.referrals.Friend' => 'string',
            'grazecart.referrals.Online Search' => 'string',
            'grazecart.referrals.Other' => 'string',
            'grazecart.yearly_revenue_questions.none-selected' => 'string',
            'grazecart.yearly_revenue_questions.not selling online' => 'string',
            'grazecart.yearly_revenue_questions.0 to 50k' => 'string',
            'grazecart.yearly_revenue_questions.50k to 250k' => 'string',
            'grazecart.yearly_revenue_questions.250k to 1m' => 'string',
            'grazecart.yearly_revenue_questions.1m+' => 'string',
            'grazecart.new_storefront_enabled' => 'boolean',
            'grazecart.welcome_coupon_code' => 'string',
            'horizon.domain' => 'NULL',
            'horizon.path' => 'string',
            'horizon.use' => 'string',
            'horizon.prefix' => 'string',
            'horizon.middleware' => 'array',
            'horizon.waits.redis:default' => 'integer',
            'horizon.waits.redis:low' => 'integer',
            'horizon.trim.recent' => 'integer',
            'horizon.trim.pending' => 'integer',
            'horizon.trim.completed' => 'integer',
            'horizon.trim.recent_failed' => 'integer',
            'horizon.trim.failed' => 'integer',
            'horizon.trim.monitored' => 'integer',
            'horizon.silenced' => 'array',
            'horizon.metrics.trim_snapshots.job' => 'integer',
            'horizon.metrics.trim_snapshots.queue' => 'integer',
            'horizon.fast_termination' => 'boolean',
            'horizon.memory_limit' => 'integer',
            'horizon.defaults.supervisor-1.connection' => 'string',
            'horizon.defaults.supervisor-1.queue' => 'array',
            'horizon.defaults.supervisor-1.balance' => 'string',
            'horizon.defaults.supervisor-1.autoScalingStrategy' => 'string',
            'horizon.defaults.supervisor-1.minProcesses' => 'integer',
            'horizon.defaults.supervisor-1.maxProcesses' => 'integer',
            'horizon.defaults.supervisor-1.maxTime' => 'integer',
            'horizon.defaults.supervisor-1.maxJobs' => 'integer',
            'horizon.defaults.supervisor-1.memory' => 'integer',
            'horizon.defaults.supervisor-1.tries' => 'integer',
            'horizon.defaults.supervisor-1.timeout' => 'integer',
            'horizon.defaults.supervisor-1.nice' => 'integer',
            'horizon.environments.production.supervisor-1.maxProcesses' => 'integer',
            'horizon.environments.production.supervisor-1.balanceMaxShift' => 'integer',
            'horizon.environments.production.supervisor-1.balanceCooldown' => 'integer',
            'horizon.environments.production.supervisor-1.tries' => 'integer',
            'horizon.environments.staging.supervisor-1.maxProcesses' => 'integer',
            'horizon.environments.staging.supervisor-1.balanceMaxShift' => 'integer',
            'horizon.environments.staging.supervisor-1.balanceCooldown' => 'integer',
            'horizon.environments.local.supervisor-1.maxProcesses' => 'integer',
            'horizon.environments.local.supervisor-1.balanceMaxShift' => 'integer',
            'horizon.environments.local.supervisor-1.balanceCooldown' => 'integer',
            'horizon.environments.test.supervisor-1.maxProcesses' => 'integer',
            'horizon.environments.test.supervisor-1.balanceMaxShift' => 'integer',
            'horizon.environments.test.supervisor-1.balanceCooldown' => 'integer',
            'ide-helper.filename' => 'string',
            'ide-helper.models_filename' => 'string',
            'ide-helper.meta_filename' => 'string',
            'ide-helper.include_fluent' => 'boolean',
            'ide-helper.include_factory_builders' => 'boolean',
            'ide-helper.write_model_magic_where' => 'boolean',
            'ide-helper.write_model_external_builder_methods' => 'boolean',
            'ide-helper.write_model_relation_count_properties' => 'boolean',
            'ide-helper.write_eloquent_model_mixins' => 'boolean',
            'ide-helper.include_helpers' => 'boolean',
            'ide-helper.helper_files' => 'array',
            'ide-helper.model_locations' => 'array',
            'ide-helper.ignored_models' => 'array',
            'ide-helper.model_hooks' => 'array',
            'ide-helper.extra.Eloquent' => 'array',
            'ide-helper.extra.Session' => 'array',
            'ide-helper.magic' => 'array',
            'ide-helper.interfaces' => 'array',
            'ide-helper.model_camel_case_properties' => 'boolean',
            'ide-helper.type_overrides.integer' => 'string',
            'ide-helper.type_overrides.boolean' => 'string',
            'ide-helper.include_class_docblocks' => 'boolean',
            'ide-helper.force_fqn' => 'boolean',
            'ide-helper.use_generics_annotations' => 'boolean',
            'ide-helper.additional_relation_types' => 'array',
            'ide-helper.additional_relation_return_types' => 'array',
            'ide-helper.enforce_nullable_relationships' => 'boolean',
            'ide-helper.post_migrate' => 'array',
            'ide-helper.macroable_traits' => 'array',
            'ide-helper.custom_db_types' => 'array',
            'image.driver' => 'string',
            'inertia.ssr.enabled' => 'boolean',
            'inertia.ssr.url' => 'string',
            'inertia.testing.ensure_pages_exist' => 'boolean',
            'inertia.testing.page_paths' => 'array',
            'inertia.testing.page_extensions' => 'array',
            'inertia.history.encrypt' => 'boolean',
            'integrations.drip.id' => 'string',
            'integrations.drip.enabled' => 'boolean',
            'integrations.drip.api_key' => 'string',
            'integrations.drip.account_id' => 'string',
            'integrations.drip.default_subscriber_tags' => 'array',
            'integrations.drip.title' => 'string',
            'integrations.drip.logo' => 'string',
            'integrations.drip.svg' => 'NULL',
            'integrations.drip.setup_class' => 'string',
            'integrations.drip.show_slug' => 'string',
            'integrations.drip.edit_slug' => 'string',
            'integrations.drip.app_slug' => 'NULL',
            'integrations.drip.vendor' => 'string',
            'integrations.drip.short_description' => 'string',
            'integrations.drip.long_description' => 'string',
            'integrations.drip.categories' => 'array',
            'integrations.drip.highlights' => 'array',
            'integrations.drip.image' => 'string',
            'integrations.drip.screenshots' => 'array',
            'integrations.drip.support_url' => 'string',
            'integrations.drip.price_description' => 'string',
            'integrations.drip.product_id' => 'NULL',
            'integrations.pickup-manager.id' => 'string',
            'integrations.pickup-manager.enabled' => 'boolean',
            'integrations.pickup-manager.title' => 'string',
            'integrations.pickup-manager.logo' => 'NULL',
            'integrations.pickup-manager.svg' => 'string',
            'integrations.pickup-manager.setup_class' => 'NULL',
            'integrations.pickup-manager.show_slug' => 'string',
            'integrations.pickup-manager.edit_slug' => 'string',
            'integrations.pickup-manager.app_slug' => 'string',
            'integrations.pickup-manager.vendor' => 'string',
            'integrations.pickup-manager.short_description' => 'string',
            'integrations.pickup-manager.long_description' => 'string',
            'integrations.pickup-manager.categories' => 'array',
            'integrations.pickup-manager.highlights' => 'array',
            'integrations.pickup-manager.image' => 'string',
            'integrations.pickup-manager.screenshots' => 'array',
            'integrations.pickup-manager.support_url' => 'string',
            'integrations.pickup-manager.price_description' => 'string',
            'livewire.class_namespace' => 'string',
            'livewire.view_path' => 'string',
            'livewire.layout' => 'string',
            'livewire.lazy_placeholder' => 'NULL',
            'livewire.temporary_file_upload.disk' => 'NULL',
            'livewire.temporary_file_upload.rules' => 'NULL',
            'livewire.temporary_file_upload.directory' => 'NULL',
            'livewire.temporary_file_upload.middleware' => 'NULL',
            'livewire.temporary_file_upload.preview_mimes' => 'array',
            'livewire.temporary_file_upload.max_upload_time' => 'integer',
            'livewire.temporary_file_upload.cleanup' => 'boolean',
            'livewire.render_on_redirect' => 'boolean',
            'livewire.legacy_model_binding' => 'boolean',
            'livewire.inject_assets' => 'boolean',
            'livewire.navigate.show_progress_bar' => 'boolean',
            'livewire.navigate.progress_bar_color' => 'string',
            'livewire.inject_morph_markers' => 'boolean',
            'livewire.pagination_theme' => 'string',
            'logging.default' => 'string',
            'logging.deprecations.channel' => 'NULL',
            'logging.deprecations.trace' => 'boolean',
            'logging.channels.stack.driver' => 'string',
            'logging.channels.stack.channels' => 'array',
            'logging.channels.stack.ignore_exceptions' => 'boolean',
            'logging.channels.single.driver' => 'string',
            'logging.channels.single.path' => 'string',
            'logging.channels.single.level' => 'string',
            'logging.channels.single.replace_placeholders' => 'boolean',
            'logging.channels.daily.driver' => 'string',
            'logging.channels.daily.path' => 'string',
            'logging.channels.daily.level' => 'string',
            'logging.channels.daily.days' => 'string',
            'logging.channels.daily.replace_placeholders' => 'boolean',
            'logging.channels.slack.driver' => 'string',
            'logging.channels.slack.url' => 'string',
            'logging.channels.slack.username' => 'string',
            'logging.channels.slack.emoji' => 'string',
            'logging.channels.slack.level' => 'string',
            'logging.channels.slack.replace_placeholders' => 'boolean',
            'logging.channels.papertrail.driver' => 'string',
            'logging.channels.papertrail.level' => 'string',
            'logging.channels.papertrail.handler' => 'string',
            'logging.channels.papertrail.handler_with.host' => 'NULL',
            'logging.channels.papertrail.handler_with.port' => 'NULL',
            'logging.channels.papertrail.handler_with.connectionString' => 'string',
            'logging.channels.papertrail.processors' => 'array',
            'logging.channels.stderr.driver' => 'string',
            'logging.channels.stderr.level' => 'string',
            'logging.channels.stderr.handler' => 'string',
            'logging.channels.stderr.handler_with.stream' => 'string',
            'logging.channels.stderr.formatter' => 'NULL',
            'logging.channels.stderr.processors' => 'array',
            'logging.channels.syslog.driver' => 'string',
            'logging.channels.syslog.level' => 'string',
            'logging.channels.syslog.facility' => 'integer',
            'logging.channels.syslog.replace_placeholders' => 'boolean',
            'logging.channels.errorlog.driver' => 'string',
            'logging.channels.errorlog.level' => 'string',
            'logging.channels.errorlog.replace_placeholders' => 'boolean',
            'logging.channels.null.driver' => 'string',
            'logging.channels.null.handler' => 'string',
            'logging.channels.emergency.path' => 'string',
            'logging.channels.bugsnag.driver' => 'string',
            'logging.channels.deprecations.driver' => 'string',
            'logging.channels.deprecations.handler' => 'string',
            'mail.default' => 'string',
            'mail.mailers.smtp.transport' => 'string',
            'mail.mailers.smtp.url' => 'NULL',
            'mail.mailers.smtp.host' => 'string',
            'mail.mailers.smtp.port' => 'string',
            'mail.mailers.smtp.encryption' => 'string',
            'mail.mailers.smtp.username' => 'string',
            'mail.mailers.smtp.password' => 'string',
            'mail.mailers.smtp.timeout' => 'NULL',
            'mail.mailers.smtp.local_domain' => 'string',
            'mail.mailers.ses.transport' => 'string',
            'mail.mailers.postmark.transport' => 'string',
            'mail.mailers.resend.transport' => 'string',
            'mail.mailers.sendmail.transport' => 'string',
            'mail.mailers.sendmail.path' => 'string',
            'mail.mailers.log.transport' => 'string',
            'mail.mailers.log.channel' => 'NULL',
            'mail.mailers.array.transport' => 'string',
            'mail.mailers.failover.transport' => 'string',
            'mail.mailers.failover.mailers' => 'array',
            'mail.mailers.roundrobin.transport' => 'string',
            'mail.mailers.roundrobin.mailers' => 'array',
            'mail.mailers.mailgun.transport' => 'string',
            'mail.from.address' => 'string',
            'mail.from.name' => 'string',
            'mail.markdown.theme' => 'string',
            'mail.markdown.paths' => 'array',
            'mail.to' => 'array',
            'mail.no_reply.address' => 'string',
            'mail.marketing_from.address' => 'string',
            'mail.marketing_from.name' => 'string',
            'mail.notifications.new_registration' => 'array',
            'mail.notifications.subscription_demand_report' => 'array',
            'mail.notifications.order_credit_report' => 'array',
            'queue.default' => 'string',
            'queue.connections.sync.driver' => 'string',
            'queue.connections.database.driver' => 'string',
            'queue.connections.database.connection' => 'NULL',
            'queue.connections.database.table' => 'string',
            'queue.connections.database.queue' => 'string',
            'queue.connections.database.retry_after' => 'integer',
            'queue.connections.database.after_commit' => 'boolean',
            'queue.connections.beanstalkd.driver' => 'string',
            'queue.connections.beanstalkd.host' => 'string',
            'queue.connections.beanstalkd.queue' => 'string',
            'queue.connections.beanstalkd.retry_after' => 'integer',
            'queue.connections.beanstalkd.block_for' => 'integer',
            'queue.connections.beanstalkd.after_commit' => 'boolean',
            'queue.connections.sqs.driver' => 'string',
            'queue.connections.sqs.key' => 'NULL',
            'queue.connections.sqs.secret' => 'NULL',
            'queue.connections.sqs.prefix' => 'string',
            'queue.connections.sqs.queue' => 'string',
            'queue.connections.sqs.suffix' => 'NULL',
            'queue.connections.sqs.region' => 'string',
            'queue.connections.sqs.after_commit' => 'boolean',
            'queue.connections.redis.driver' => 'string',
            'queue.connections.redis.connection' => 'string',
            'queue.connections.redis.queue' => 'string',
            'queue.connections.redis.retry_after' => 'integer',
            'queue.connections.redis.block_for' => 'NULL',
            'queue.connections.redis.after_commit' => 'boolean',
            'queue.connections.central.driver' => 'string',
            'queue.connections.central.table' => 'string',
            'queue.connections.central.queue' => 'string',
            'queue.connections.central.retry_after' => 'integer',
            'queue.connections.central.after_commit' => 'boolean',
            'queue.connections.central.central' => 'boolean',
            'queue.batching.database' => 'string',
            'queue.batching.table' => 'string',
            'queue.failed.driver' => 'string',
            'queue.failed.database' => 'string',
            'queue.failed.table' => 'string',
            'sanctum.stateful' => 'array',
            'sanctum.guard' => 'array',
            'sanctum.expiration' => 'NULL',
            'sanctum.token_prefix' => 'string',
            'sanctum.middleware.authenticate_session' => 'string',
            'sanctum.middleware.encrypt_cookies' => 'string',
            'sanctum.middleware.validate_csrf_token' => 'string',
            'scout.driver' => 'string',
            'scout.prefix' => 'string',
            'scout.queue' => 'boolean',
            'scout.after_commit' => 'boolean',
            'scout.chunk.searchable' => 'integer',
            'scout.chunk.unsearchable' => 'integer',
            'scout.soft_delete' => 'boolean',
            'scout.identify' => 'boolean',
            'scout.algolia.id' => 'string',
            'scout.algolia.secret' => 'string',
            'scout.meilisearch.host' => 'string',
            'scout.meilisearch.key' => 'string',
            'scout.meilisearch.index-settings.App\Models\Product.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Product.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.enabled' => 'boolean',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.minWordSizeForTypos.oneTypo' => 'integer',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.minWordSizeForTypos.twoTypos' => 'integer',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.disableOnWords' => 'array',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.disableOnAttributes' => 'array',
            'scout.typesense.client-settings.api_key' => 'string',
            'scout.typesense.client-settings.nodes.0.host' => 'string',
            'scout.typesense.client-settings.nodes.0.port' => 'string',
            'scout.typesense.client-settings.nodes.0.path' => 'string',
            'scout.typesense.client-settings.nodes.0.protocol' => 'string',
            'scout.typesense.client-settings.nearest_node.host' => 'string',
            'scout.typesense.client-settings.nearest_node.port' => 'string',
            'scout.typesense.client-settings.nearest_node.path' => 'string',
            'scout.typesense.client-settings.nearest_node.protocol' => 'string',
            'scout.typesense.client-settings.connection_timeout_seconds' => 'integer',
            'scout.typesense.client-settings.healthcheck_interval_seconds' => 'integer',
            'scout.typesense.client-settings.num_retries' => 'integer',
            'scout.typesense.client-settings.retry_interval_seconds' => 'integer',
            'scout.typesense.model-settings' => 'array',
            'services.postmark.token' => 'NULL',
            'services.ses.key' => 'NULL',
            'services.ses.secret' => 'NULL',
            'services.ses.region' => 'string',
            'services.resend.key' => 'NULL',
            'services.slack.hook' => 'string',
            'services.mailgun.central_domain' => 'string',
            'services.mailgun.domain' => 'string',
            'services.mailgun.marketing_domain' => 'string',
            'services.mailgun.secret' => 'string',
            'services.mailgun.endpoint' => 'string',
            'services.mailgun.scheme' => 'string',
            'services.stripe.key' => 'string',
            'services.stripe.secret' => 'string',
            'services.stripe.account' => 'string',
            'services.stripe.managed' => 'boolean',
            'services.stripe.webhook.secret' => 'string',
            'services.stripe.webhook.tolerance' => 'integer',
            'services.stripe.debug' => 'boolean',
            'services.geocodio.key' => 'string',
            'services.geocoderCa.key' => 'string',
            'services.drip.key' => 'string',
            'services.drip.account_id' => 'string',
            'services.facebook.pixel_id' => 'NULL',
            'services.facebook.access_token' => 'string',
            'services.facebook.test_event_code' => 'string',
            'services.google.geocoder_api_key' => 'string',
            'services.google.places_js_api_key' => 'string',
            'services.google.sheets_api_key' => 'string',
            'services.google.spreadsheet_id' => 'string',
            'services.google.worksheet_name' => 'string',
            'services.google.place_id' => 'string',
            'services.google.analytics_id' => 'NULL',
            'services.google.ads_id' => 'NULL',
            'services.twilio.default' => 'string',
            'services.twilio.connections.twilio.sid' => 'string',
            'services.twilio.connections.twilio.token' => 'string',
            'services.twilio.connections.twilio.from' => 'string',
            'services.horizon.secret' => 'string',
            'services.segment.write_key' => 'string',
            'services.stax.partner_api_key' => 'string',
            'services.pay_fac.api_key' => 'string',
            'services.pay_fac.company_id' => 'string',
            'services.pay_fac.js_client_key' => 'string',
            'services.pay_fac.stripe_key' => 'NULL',
            'services.launch_darkly.sdk_key' => 'NULL',
            'services.tolstoy.app_key' => 'string',
            'services.cloudfront.url' => 'string',
            'sluggable.source' => 'NULL',
            'sluggable.maxLength' => 'NULL',
            'sluggable.maxLengthKeepWords' => 'boolean',
            'sluggable.method' => 'NULL',
            'sluggable.separator' => 'string',
            'sluggable.unique' => 'boolean',
            'sluggable.uniqueSuffix' => 'NULL',
            'sluggable.firstUniqueSuffix' => 'integer',
            'sluggable.includeTrashed' => 'boolean',
            'sluggable.reserved' => 'NULL',
            'sluggable.onUpdate' => 'boolean',
            'sluggable.slugEngineOptions' => 'array',
            'telescope.enabled' => 'boolean',
            'telescope.domain' => 'NULL',
            'telescope.path' => 'string',
            'telescope.driver' => 'string',
            'telescope.storage.database.connection' => 'string',
            'telescope.storage.database.chunk' => 'integer',
            'telescope.queue.connection' => 'NULL',
            'telescope.queue.queue' => 'NULL',
            'telescope.queue.delay' => 'integer',
            'telescope.middleware' => 'array',
            'telescope.only_paths' => 'array',
            'telescope.ignore_paths' => 'array',
            'telescope.ignore_commands' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\CacheWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CommandWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CommandWatcher.ignore' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\DumpWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\EventWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ExceptionWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\JobWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\LogWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\MailWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.events' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\NotificationWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.ignore_packages' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.slow' => 'integer',
            'telescope.watchers.Laravel\Telescope\Watchers\RedisWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.size_limit' => 'integer',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_abilities' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_packages' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ScheduleWatcher' => 'boolean',
            'theme.id' => 'string',
            'theme.title' => 'string',
            'theme.description' => 'string',
            'theme.view_path' => 'string',
            'theme.settings' => 'NULL',
            'theme.css' => 'NULL',
            'theme.css_preview' => 'NULL',
            'theme.custom_css' => 'NULL',
            'theme.theme_builder' => 'string',
            'theme.google_web_fonts' => 'array',
            'tinker.commands' => 'array',
            'tinker.alias' => 'array',
            'tinker.dont_alias' => 'array',
            'twilio-notification-channel.enabled' => 'boolean',
            'twilio-notification-channel.username' => 'NULL',
            'twilio-notification-channel.password' => 'NULL',
            'twilio-notification-channel.auth_token' => 'string',
            'twilio-notification-channel.account_sid' => 'string',
            'twilio-notification-channel.from' => 'string',
            'twilio-notification-channel.alphanumeric_sender' => 'NULL',
            'twilio-notification-channel.shorten_urls' => 'boolean',
            'twilio-notification-channel.sms_service_sid' => 'NULL',
            'twilio-notification-channel.debug_to' => 'NULL',
            'twilio-notification-channel.ignored_error_codes' => 'array',
            'twilio-notification-channel.message_size_limit' => 'integer',
            'hashing.driver' => 'string',
            'hashing.bcrypt.rounds' => 'string',
            'hashing.bcrypt.verify' => 'boolean',
            'hashing.bcrypt.limit' => 'NULL',
            'hashing.argon.memory' => 'integer',
            'hashing.argon.threads' => 'integer',
            'hashing.argon.time' => 'integer',
            'hashing.argon.verify' => 'boolean',
            'hashing.rehash_on_login' => 'boolean',
            'session.driver' => 'string',
            'session.lifetime' => 'integer',
            'session.expire_on_close' => 'boolean',
            'session.encrypt' => 'boolean',
            'session.files' => 'string',
            'session.connection' => 'string',
            'session.table' => 'string',
            'session.store' => 'NULL',
            'session.lottery' => 'array',
            'session.cookie' => 'string',
            'session.path' => 'string',
            'session.domain' => 'NULL',
            'session.secure' => 'NULL',
            'session.http_only' => 'boolean',
            'session.same_site' => 'string',
            'session.partitioned' => 'boolean',
            'concurrency.default' => 'string',
            'broadcasting.default' => 'string',
            'broadcasting.connections.reverb.driver' => 'string',
            'broadcasting.connections.reverb.key' => 'NULL',
            'broadcasting.connections.reverb.secret' => 'NULL',
            'broadcasting.connections.reverb.app_id' => 'NULL',
            'broadcasting.connections.reverb.options.host' => 'NULL',
            'broadcasting.connections.reverb.options.port' => 'integer',
            'broadcasting.connections.reverb.options.scheme' => 'string',
            'broadcasting.connections.reverb.options.useTLS' => 'boolean',
            'broadcasting.connections.reverb.client_options' => 'array',
            'broadcasting.connections.pusher.driver' => 'string',
            'broadcasting.connections.pusher.key' => 'NULL',
            'broadcasting.connections.pusher.secret' => 'NULL',
            'broadcasting.connections.pusher.app_id' => 'NULL',
            'broadcasting.connections.pusher.options.cluster' => 'NULL',
            'broadcasting.connections.pusher.options.host' => 'string',
            'broadcasting.connections.pusher.options.port' => 'integer',
            'broadcasting.connections.pusher.options.scheme' => 'string',
            'broadcasting.connections.pusher.options.encrypted' => 'boolean',
            'broadcasting.connections.pusher.options.useTLS' => 'boolean',
            'broadcasting.connections.pusher.client_options' => 'array',
            'broadcasting.connections.ably.driver' => 'string',
            'broadcasting.connections.ably.key' => 'NULL',
            'broadcasting.connections.log.driver' => 'string',
            'broadcasting.connections.null.driver' => 'string',
            'view.paths' => 'array',
            'view.compiled' => 'string',
            'debugbar.enabled' => 'boolean',
            'debugbar.hide_empty_tabs' => 'boolean',
            'debugbar.except' => 'array',
            'debugbar.storage.enabled' => 'boolean',
            'debugbar.storage.open' => 'NULL',
            'debugbar.storage.driver' => 'string',
            'debugbar.storage.path' => 'string',
            'debugbar.storage.connection' => 'NULL',
            'debugbar.storage.provider' => 'string',
            'debugbar.storage.hostname' => 'string',
            'debugbar.storage.port' => 'integer',
            'debugbar.editor' => 'string',
            'debugbar.remote_sites_path' => 'NULL',
            'debugbar.local_sites_path' => 'NULL',
            'debugbar.include_vendors' => 'boolean',
            'debugbar.capture_ajax' => 'boolean',
            'debugbar.add_ajax_timing' => 'boolean',
            'debugbar.ajax_handler_auto_show' => 'boolean',
            'debugbar.ajax_handler_enable_tab' => 'boolean',
            'debugbar.defer_datasets' => 'boolean',
            'debugbar.error_handler' => 'boolean',
            'debugbar.clockwork' => 'boolean',
            'debugbar.collectors.phpinfo' => 'boolean',
            'debugbar.collectors.messages' => 'boolean',
            'debugbar.collectors.time' => 'boolean',
            'debugbar.collectors.memory' => 'boolean',
            'debugbar.collectors.exceptions' => 'boolean',
            'debugbar.collectors.log' => 'boolean',
            'debugbar.collectors.db' => 'boolean',
            'debugbar.collectors.views' => 'boolean',
            'debugbar.collectors.route' => 'boolean',
            'debugbar.collectors.auth' => 'boolean',
            'debugbar.collectors.gate' => 'boolean',
            'debugbar.collectors.session' => 'boolean',
            'debugbar.collectors.symfony_request' => 'boolean',
            'debugbar.collectors.mail' => 'boolean',
            'debugbar.collectors.laravel' => 'boolean',
            'debugbar.collectors.events' => 'boolean',
            'debugbar.collectors.default_request' => 'boolean',
            'debugbar.collectors.logs' => 'boolean',
            'debugbar.collectors.files' => 'boolean',
            'debugbar.collectors.config' => 'boolean',
            'debugbar.collectors.cache' => 'boolean',
            'debugbar.collectors.models' => 'boolean',
            'debugbar.collectors.livewire' => 'boolean',
            'debugbar.collectors.jobs' => 'boolean',
            'debugbar.collectors.pennant' => 'boolean',
            'debugbar.options.time.memory_usage' => 'boolean',
            'debugbar.options.messages.trace' => 'boolean',
            'debugbar.options.messages.capture_dumps' => 'boolean',
            'debugbar.options.memory.reset_peak' => 'boolean',
            'debugbar.options.memory.with_baseline' => 'boolean',
            'debugbar.options.memory.precision' => 'integer',
            'debugbar.options.auth.show_name' => 'boolean',
            'debugbar.options.auth.show_guards' => 'boolean',
            'debugbar.options.db.with_params' => 'boolean',
            'debugbar.options.db.exclude_paths' => 'array',
            'debugbar.options.db.backtrace' => 'boolean',
            'debugbar.options.db.backtrace_exclude_paths' => 'array',
            'debugbar.options.db.timeline' => 'boolean',
            'debugbar.options.db.duration_background' => 'boolean',
            'debugbar.options.db.explain.enabled' => 'boolean',
            'debugbar.options.db.hints' => 'boolean',
            'debugbar.options.db.show_copy' => 'boolean',
            'debugbar.options.db.slow_threshold' => 'boolean',
            'debugbar.options.db.memory_usage' => 'boolean',
            'debugbar.options.db.soft_limit' => 'integer',
            'debugbar.options.db.hard_limit' => 'integer',
            'debugbar.options.mail.timeline' => 'boolean',
            'debugbar.options.mail.show_body' => 'boolean',
            'debugbar.options.views.timeline' => 'boolean',
            'debugbar.options.views.data' => 'boolean',
            'debugbar.options.views.group' => 'integer',
            'debugbar.options.views.exclude_paths' => 'array',
            'debugbar.options.route.label' => 'boolean',
            'debugbar.options.session.hiddens' => 'array',
            'debugbar.options.symfony_request.label' => 'boolean',
            'debugbar.options.symfony_request.hiddens' => 'array',
            'debugbar.options.events.data' => 'boolean',
            'debugbar.options.logs.file' => 'NULL',
            'debugbar.options.cache.values' => 'boolean',
            'debugbar.inject' => 'boolean',
            'debugbar.route_prefix' => 'string',
            'debugbar.route_middleware' => 'array',
            'debugbar.route_domain' => 'NULL',
            'debugbar.theme' => 'string',
            'debugbar.debug_backtrace_limit' => 'integer',
            'cookie-consent.enabled' => 'boolean',
            'cookie-consent.cookie_name' => 'string',
            'cookie-consent.cookie_lifetime' => 'integer',
        ]));
    override(\Illuminate\Config\Repository::get(), map([
            'app.name' => 'string',
            'app.env' => 'string',
            'app.debug' => 'boolean',
            'app.url' => 'string',
            'app.frontend_url' => 'string',
            'app.asset_url' => 'NULL',
            'app.timezone' => 'string',
            'app.locale' => 'string',
            'app.fallback_locale' => 'string',
            'app.faker_locale' => 'string',
            'app.cipher' => 'string',
            'app.key' => 'string',
            'app.previous_keys' => 'array',
            'app.maintenance.driver' => 'string',
            'app.maintenance.store' => 'string',
            'app.providers' => 'array',
            'app.aliases.App' => 'string',
            'app.aliases.Arr' => 'string',
            'app.aliases.Artisan' => 'string',
            'app.aliases.Auth' => 'string',
            'app.aliases.Blade' => 'string',
            'app.aliases.Broadcast' => 'string',
            'app.aliases.Bus' => 'string',
            'app.aliases.Cache' => 'string',
            'app.aliases.Concurrency' => 'string',
            'app.aliases.Config' => 'string',
            'app.aliases.Context' => 'string',
            'app.aliases.Cookie' => 'string',
            'app.aliases.Crypt' => 'string',
            'app.aliases.Date' => 'string',
            'app.aliases.DB' => 'string',
            'app.aliases.Eloquent' => 'string',
            'app.aliases.Event' => 'string',
            'app.aliases.File' => 'string',
            'app.aliases.Gate' => 'string',
            'app.aliases.Hash' => 'string',
            'app.aliases.Http' => 'string',
            'app.aliases.Js' => 'string',
            'app.aliases.Lang' => 'string',
            'app.aliases.Log' => 'string',
            'app.aliases.Mail' => 'string',
            'app.aliases.Notification' => 'string',
            'app.aliases.Number' => 'string',
            'app.aliases.Password' => 'string',
            'app.aliases.Process' => 'string',
            'app.aliases.Queue' => 'string',
            'app.aliases.RateLimiter' => 'string',
            'app.aliases.Redirect' => 'string',
            'app.aliases.Request' => 'string',
            'app.aliases.Response' => 'string',
            'app.aliases.Route' => 'string',
            'app.aliases.Schedule' => 'string',
            'app.aliases.Schema' => 'string',
            'app.aliases.Session' => 'string',
            'app.aliases.Storage' => 'string',
            'app.aliases.Str' => 'string',
            'app.aliases.URL' => 'string',
            'app.aliases.Uri' => 'string',
            'app.aliases.Validator' => 'string',
            'app.aliases.View' => 'string',
            'app.aliases.Vite' => 'string',
            'app.aliases.Bugsnag' => 'string',
            'app.aliases.Image' => 'string',
            'app.aliases.Inspiring' => 'string',
            'app.aliases.Redis' => 'string',
            'app.aliases.Socialite' => 'string',
            'app.domains.root' => 'string',
            'app.domains.admin' => 'NULL',
            'app.domains.api' => 'string',
            'app.deleted_user_email' => 'string',
            'app.inventory.spreadsheet_id' => 'string',
            'app.inventory.worksheet_name' => 'string',
            'app.homepage_featured_bundle_ids' => 'string',
            'auth.defaults.guard' => 'string',
            'auth.defaults.passwords' => 'string',
            'auth.guards.web.driver' => 'string',
            'auth.guards.web.provider' => 'string',
            'auth.guards.api.driver' => 'string',
            'auth.guards.api.provider' => 'string',
            'auth.guards.api.hash' => 'boolean',
            'auth.guards.sanctum.driver' => 'string',
            'auth.guards.sanctum.provider' => 'NULL',
            'auth.providers.users.driver' => 'string',
            'auth.providers.users.model' => 'string',
            'auth.passwords.users.provider' => 'string',
            'auth.passwords.users.table' => 'string',
            'auth.passwords.users.expire' => 'integer',
            'auth.passwords.users.throttle' => 'integer',
            'auth.password_timeout' => 'integer',
            'bugsnag.api_key' => 'string',
            'bugsnag.app_type' => 'NULL',
            'bugsnag.app_version' => 'NULL',
            'bugsnag.batch_sending' => 'NULL',
            'bugsnag.endpoint' => 'NULL',
            'bugsnag.filters' => 'NULL',
            'bugsnag.hostname' => 'NULL',
            'bugsnag.proxy' => 'array',
            'bugsnag.project_root' => 'NULL',
            'bugsnag.project_root_regex' => 'NULL',
            'bugsnag.strip_path' => 'NULL',
            'bugsnag.strip_path_regex' => 'NULL',
            'bugsnag.query' => 'boolean',
            'bugsnag.bindings' => 'boolean',
            'bugsnag.octane_breadcrumbs' => 'boolean',
            'bugsnag.release_stage' => 'NULL',
            'bugsnag.notify_release_stages' => 'array',
            'bugsnag.send_code' => 'boolean',
            'bugsnag.callbacks' => 'boolean',
            'bugsnag.user' => 'boolean',
            'bugsnag.logger_notify_level' => 'NULL',
            'bugsnag.auto_capture_sessions' => 'boolean',
            'bugsnag.session_endpoint' => 'NULL',
            'bugsnag.build_endpoint' => 'NULL',
            'bugsnag.discard_classes' => 'NULL',
            'bugsnag.redacted_keys' => 'NULL',
            'bugsnag.feature_flags' => 'array',
            'bugsnag.max_breadcrumbs' => 'NULL',
            'bugsnag.attach_hidden_context' => 'boolean',
            'cache.default' => 'string',
            'cache.stores.array.driver' => 'string',
            'cache.stores.array.serialize' => 'boolean',
            'cache.stores.database.driver' => 'string',
            'cache.stores.database.connection' => 'NULL',
            'cache.stores.database.table' => 'string',
            'cache.stores.database.lock_connection' => 'NULL',
            'cache.stores.database.lock_table' => 'NULL',
            'cache.stores.file.driver' => 'string',
            'cache.stores.file.path' => 'string',
            'cache.stores.file.lock_path' => 'string',
            'cache.stores.memcached.driver' => 'string',
            'cache.stores.memcached.persistent_id' => 'NULL',
            'cache.stores.memcached.sasl' => 'array',
            'cache.stores.memcached.options' => 'array',
            'cache.stores.memcached.servers.0.host' => 'string',
            'cache.stores.memcached.servers.0.port' => 'integer',
            'cache.stores.memcached.servers.0.weight' => 'integer',
            'cache.stores.redis.driver' => 'string',
            'cache.stores.redis.connection' => 'string',
            'cache.stores.redis.lock_connection' => 'string',
            'cache.stores.dynamodb.driver' => 'string',
            'cache.stores.dynamodb.key' => 'NULL',
            'cache.stores.dynamodb.secret' => 'NULL',
            'cache.stores.dynamodb.region' => 'string',
            'cache.stores.dynamodb.table' => 'string',
            'cache.stores.dynamodb.endpoint' => 'NULL',
            'cache.stores.octane.driver' => 'string',
            'cache.prefix' => 'string',
            'cache.limiter' => 'string',
            'canny.app_id' => 'string',
            'cart.default.type' => 'string',
            'cart.default.id' => 'NULL',
            'cart.default.purchase_type' => 'string',
            'cart.default.date_id' => 'NULL',
            'cart.default.delivery_method_id' => 'NULL',
            'cart.default.items' => 'object',
            'cart.default.subscription' => 'NULL',
            'cart.default.notes' => 'string',
            'cart.default.is_gift' => 'boolean',
            'cart.default.recipient_email' => 'string',
            'cart.default.recipient_notes' => 'string',
            'cart.default.discounts.coupons' => 'object',
            'cart.default.discounts.gift_card.name' => 'string',
            'cart.default.discounts.gift_card.code' => 'string',
            'cart.default.discounts.gift_card.amount' => 'integer',
            'cart.default.discounts.store_credit.amount' => 'integer',
            'cart.default.customer.id' => 'NULL',
            'cart.default.customer.first_name' => 'string',
            'cart.default.customer.last_name' => 'string',
            'cart.default.customer.email' => 'string',
            'cart.default.customer.phone' => 'string',
            'cart.default.customer.save_for_later' => 'boolean',
            'cart.default.customer.opt_in_to_sms' => 'boolean',
            'cart.default.customer.subscribed_to_sms' => 'boolean',
            'cart.default.shipping.address_id' => 'NULL',
            'cart.default.shipping.street' => 'string',
            'cart.default.shipping.street_2' => 'string',
            'cart.default.shipping.city' => 'string',
            'cart.default.shipping.state' => 'string',
            'cart.default.shipping.zip' => 'string',
            'cart.default.shipping.country' => 'string',
            'cart.default.shipping.save_for_later' => 'boolean',
            'cart.default.billing.method' => 'NULL',
            'cart.default.billing.source_id' => 'NULL',
            'cart.default.billing.save_for_later' => 'boolean',
            'cart.database.date_id' => 'NULL',
            'cart.database.delivery_method_id' => 'NULL',
            'cart.database.items' => 'array',
            'cart.database.subscription' => 'NULL',
            'cart.database.notes' => 'string',
            'cart.database.is_gift' => 'boolean',
            'cart.database.recipient_email' => 'string',
            'cart.database.recipient_notes' => 'string',
            'cart.database.discounts.coupons' => 'array',
            'cart.database.discounts.gift_card.name' => 'string',
            'cart.database.discounts.gift_card.code' => 'string',
            'cart.database.discounts.gift_card.amount' => 'integer',
            'cart.database.discounts.store_credit.amount' => 'integer',
            'cart.database.customer.id' => 'NULL',
            'cart.database.customer.first_name' => 'string',
            'cart.database.customer.last_name' => 'string',
            'cart.database.customer.email' => 'string',
            'cart.database.customer.phone' => 'string',
            'cart.database.customer.save_for_later' => 'boolean',
            'cart.database.customer.opt_in_to_sms' => 'boolean',
            'cart.database.customer.subscribed_to_sms' => 'boolean',
            'cart.database.shipping.address_id' => 'NULL',
            'cart.database.shipping.street' => 'string',
            'cart.database.shipping.street_2' => 'string',
            'cart.database.shipping.city' => 'string',
            'cart.database.shipping.state' => 'string',
            'cart.database.shipping.zip' => 'string',
            'cart.database.shipping.country' => 'string',
            'cart.database.shipping.save_for_later' => 'boolean',
            'cart.database.billing.method' => 'NULL',
            'cart.database.billing.source_id' => 'NULL',
            'cart.database.billing.save_for_later' => 'boolean',
            'cart.preorder.type' => 'string',
            'cart.preorder.date_id' => 'NULL',
            'cart.preorder.delivery_method_id' => 'NULL',
            'cart.preorder.items' => 'object',
            'cart.preorder.notes' => 'string',
            'cart.preorder.is_gift' => 'boolean',
            'cart.preorder.recipient_email' => 'string',
            'cart.preorder.recipient_notes' => 'string',
            'cart.preorder.discounts.coupons' => 'object',
            'cart.preorder.discounts.gift_card.name' => 'string',
            'cart.preorder.discounts.gift_card.code' => 'string',
            'cart.preorder.discounts.gift_card.amount' => 'integer',
            'cart.preorder.discounts.store_credit.amount' => 'integer',
            'cart.preorder.customer.id' => 'NULL',
            'cart.preorder.customer.first_name' => 'string',
            'cart.preorder.customer.last_name' => 'string',
            'cart.preorder.customer.email' => 'string',
            'cart.preorder.customer.phone' => 'string',
            'cart.preorder.customer.save_for_later' => 'boolean',
            'cart.preorder.customer.opt_in_to_sms' => 'boolean',
            'cart.preorder.customer.subscribed_to_sms' => 'boolean',
            'cart.preorder.shipping.street' => 'string',
            'cart.preorder.shipping.street_2' => 'string',
            'cart.preorder.shipping.city' => 'string',
            'cart.preorder.shipping.state' => 'string',
            'cart.preorder.shipping.zip' => 'string',
            'cart.preorder.shipping.country' => 'string',
            'cart.preorder.shipping.save_for_later' => 'boolean',
            'cart.preorder.billing.method' => 'NULL',
            'cart.preorder.billing.source_id' => 'NULL',
            'cart.preorder.billing.save_for_later' => 'boolean',
            'cart.gift-card.type' => 'string',
            'cart.gift-card.items' => 'object',
            'cart.gift-card.notes' => 'string',
            'cart.gift-card.discounts.coupons' => 'object',
            'cart.gift-card.discounts.gift_card.name' => 'string',
            'cart.gift-card.discounts.gift_card.code' => 'string',
            'cart.gift-card.discounts.gift_card.amount' => 'integer',
            'cart.gift-card.discounts.store_credit.amount' => 'integer',
            'cart.gift-card.customer.id' => 'NULL',
            'cart.gift-card.customer.first_name' => 'string',
            'cart.gift-card.customer.last_name' => 'string',
            'cart.gift-card.customer.email' => 'string',
            'cart.gift-card.customer.phone' => 'string',
            'cart.gift-card.customer.save_for_later' => 'boolean',
            'cart.gift-card.customer.opt_in_to_sms' => 'boolean',
            'cart.gift-card.customer.subscribed_to_sms' => 'boolean',
            'cart.gift-card.shipping.street' => 'string',
            'cart.gift-card.shipping.street_2' => 'string',
            'cart.gift-card.shipping.city' => 'string',
            'cart.gift-card.shipping.state' => 'string',
            'cart.gift-card.shipping.zip' => 'string',
            'cart.gift-card.shipping.country' => 'string',
            'cart.gift-card.shipping.save_for_later' => 'boolean',
            'cart.gift-card.billing.method' => 'string',
            'cart.gift-card.billing.source_id' => 'NULL',
            'cart.gift-card.billing.save_for_later' => 'boolean',
            'clockwork.enable' => 'NULL',
            'clockwork.collect_data_always' => 'boolean',
            'clockwork.storage' => 'string',
            'clockwork.storage_files_path' => 'string',
            'clockwork.storage_sql_database' => 'string',
            'clockwork.storage_sql_table' => 'string',
            'clockwork.filter' => 'array',
            'clockwork.filter_uris' => 'array',
            'clockwork.additional_data_sources' => 'array',
            'clockwork.headers' => 'array',
            'compile.files' => 'array',
            'compile.providers' => 'array',
            'cors.paths' => 'array',
            'cors.allowed_methods' => 'array',
            'cors.allowed_origins' => 'array',
            'cors.allowed_origins_patterns' => 'array',
            'cors.allowed_headers' => 'array',
            'cors.exposed_headers' => 'array',
            'cors.max_age' => 'integer',
            'cors.supports_credentials' => 'boolean',
            'database.default' => 'string',
            'database.connections.sqlite.driver' => 'string',
            'database.connections.sqlite.url' => 'NULL',
            'database.connections.sqlite.database' => 'string',
            'database.connections.sqlite.prefix' => 'string',
            'database.connections.sqlite.foreign_key_constraints' => 'boolean',
            'database.connections.sqlite.busy_timeout' => 'NULL',
            'database.connections.sqlite.journal_mode' => 'NULL',
            'database.connections.sqlite.synchronous' => 'NULL',
            'database.connections.mysql.driver' => 'string',
            'database.connections.mysql.url' => 'NULL',
            'database.connections.mysql.host' => 'string',
            'database.connections.mysql.port' => 'string',
            'database.connections.mysql.database' => 'string',
            'database.connections.mysql.username' => 'string',
            'database.connections.mysql.password' => 'string',
            'database.connections.mysql.unix_socket' => 'string',
            'database.connections.mysql.charset' => 'string',
            'database.connections.mysql.collation' => 'string',
            'database.connections.mysql.prefix' => 'string',
            'database.connections.mysql.prefix_indexes' => 'boolean',
            'database.connections.mysql.strict' => 'boolean',
            'database.connections.mysql.engine' => 'NULL',
            'database.connections.mysql.options' => 'array',
            'database.connections.mariadb.driver' => 'string',
            'database.connections.mariadb.url' => 'NULL',
            'database.connections.mariadb.host' => 'string',
            'database.connections.mariadb.port' => 'string',
            'database.connections.mariadb.database' => 'string',
            'database.connections.mariadb.username' => 'string',
            'database.connections.mariadb.password' => 'string',
            'database.connections.mariadb.unix_socket' => 'string',
            'database.connections.mariadb.charset' => 'string',
            'database.connections.mariadb.collation' => 'string',
            'database.connections.mariadb.prefix' => 'string',
            'database.connections.mariadb.prefix_indexes' => 'boolean',
            'database.connections.mariadb.strict' => 'boolean',
            'database.connections.mariadb.engine' => 'NULL',
            'database.connections.mariadb.options' => 'array',
            'database.connections.pgsql.driver' => 'string',
            'database.connections.pgsql.url' => 'NULL',
            'database.connections.pgsql.host' => 'string',
            'database.connections.pgsql.port' => 'string',
            'database.connections.pgsql.database' => 'string',
            'database.connections.pgsql.username' => 'string',
            'database.connections.pgsql.password' => 'string',
            'database.connections.pgsql.charset' => 'string',
            'database.connections.pgsql.prefix' => 'string',
            'database.connections.pgsql.prefix_indexes' => 'boolean',
            'database.connections.pgsql.search_path' => 'string',
            'database.connections.pgsql.sslmode' => 'string',
            'database.connections.sqlsrv.driver' => 'string',
            'database.connections.sqlsrv.url' => 'NULL',
            'database.connections.sqlsrv.host' => 'string',
            'database.connections.sqlsrv.port' => 'string',
            'database.connections.sqlsrv.database' => 'string',
            'database.connections.sqlsrv.username' => 'string',
            'database.connections.sqlsrv.password' => 'string',
            'database.connections.sqlsrv.charset' => 'string',
            'database.connections.sqlsrv.prefix' => 'string',
            'database.connections.sqlsrv.prefix_indexes' => 'boolean',
            'database.connections.failed_jobs.driver' => 'string',
            'database.connections.failed_jobs.host' => 'string',
            'database.connections.failed_jobs.port' => 'string',
            'database.connections.failed_jobs.database' => 'string',
            'database.connections.failed_jobs.username' => 'string',
            'database.connections.failed_jobs.password' => 'string',
            'database.connections.failed_jobs.charset' => 'string',
            'database.connections.failed_jobs.collation' => 'string',
            'database.connections.failed_jobs.prefix' => 'string',
            'database.connections.failed_jobs.strict' => 'boolean',
            'database.migrations.table' => 'string',
            'database.migrations.update_date_on_publish' => 'boolean',
            'database.redis.client' => 'string',
            'database.redis.options.cluster' => 'string',
            'database.redis.options.prefix' => 'string',
            'database.redis.default.url' => 'NULL',
            'database.redis.default.host' => 'string',
            'database.redis.default.username' => 'NULL',
            'database.redis.default.password' => 'NULL',
            'database.redis.default.port' => 'string',
            'database.redis.default.database' => 'string',
            'database.redis.default.read_write_timeout' => 'integer',
            'database.redis.cache.url' => 'NULL',
            'database.redis.cache.host' => 'string',
            'database.redis.cache.username' => 'NULL',
            'database.redis.cache.password' => 'NULL',
            'database.redis.cache.port' => 'string',
            'database.redis.cache.database' => 'string',
            'database.redis.cache.read_write_timeout' => 'integer',
            'database.redis.horizon.url' => 'NULL',
            'database.redis.horizon.host' => 'string',
            'database.redis.horizon.username' => 'NULL',
            'database.redis.horizon.password' => 'NULL',
            'database.redis.horizon.port' => 'string',
            'database.redis.horizon.database' => 'string',
            'database.redis.horizon.read_write_timeout' => 'integer',
            'database.redis.horizon.options.prefix' => 'string',
            'disposable-email.sources' => 'array',
            'disposable-email.fetcher' => 'string',
            'disposable-email.storage' => 'string',
            'disposable-email.whitelist' => 'array',
            'disposable-email.include_subdomains' => 'boolean',
            'disposable-email.cache.enabled' => 'boolean',
            'disposable-email.cache.store' => 'string',
            'disposable-email.cache.key' => 'string',
            'eloquentfilter.namespace' => 'string',
            'eloquentfilter.paginate_limit' => 'integer',
            'excel.exports.chunk_size' => 'integer',
            'excel.exports.pre_calculate_formulas' => 'boolean',
            'excel.exports.strict_null_comparison' => 'boolean',
            'excel.exports.csv.delimiter' => 'string',
            'excel.exports.csv.enclosure' => 'string',
            'excel.exports.csv.line_ending' => 'string',
            'excel.exports.csv.use_bom' => 'boolean',
            'excel.exports.csv.include_separator_line' => 'boolean',
            'excel.exports.csv.excel_compatibility' => 'boolean',
            'excel.exports.csv.output_encoding' => 'string',
            'excel.exports.properties.creator' => 'string',
            'excel.exports.properties.lastModifiedBy' => 'string',
            'excel.exports.properties.title' => 'string',
            'excel.exports.properties.description' => 'string',
            'excel.exports.properties.subject' => 'string',
            'excel.exports.properties.keywords' => 'string',
            'excel.exports.properties.category' => 'string',
            'excel.exports.properties.manager' => 'string',
            'excel.exports.properties.company' => 'string',
            'excel.imports.read_only' => 'boolean',
            'excel.imports.ignore_empty' => 'boolean',
            'excel.imports.heading_row.formatter' => 'string',
            'excel.imports.csv.delimiter' => 'NULL',
            'excel.imports.csv.enclosure' => 'string',
            'excel.imports.csv.escape_character' => 'string',
            'excel.imports.csv.contiguous' => 'boolean',
            'excel.imports.csv.input_encoding' => 'string',
            'excel.imports.properties.creator' => 'string',
            'excel.imports.properties.lastModifiedBy' => 'string',
            'excel.imports.properties.title' => 'string',
            'excel.imports.properties.description' => 'string',
            'excel.imports.properties.subject' => 'string',
            'excel.imports.properties.keywords' => 'string',
            'excel.imports.properties.category' => 'string',
            'excel.imports.properties.manager' => 'string',
            'excel.imports.properties.company' => 'string',
            'excel.extension_detector.xlsx' => 'string',
            'excel.extension_detector.xlsm' => 'string',
            'excel.extension_detector.xltx' => 'string',
            'excel.extension_detector.xltm' => 'string',
            'excel.extension_detector.xls' => 'string',
            'excel.extension_detector.xlt' => 'string',
            'excel.extension_detector.ods' => 'string',
            'excel.extension_detector.ots' => 'string',
            'excel.extension_detector.slk' => 'string',
            'excel.extension_detector.xml' => 'string',
            'excel.extension_detector.gnumeric' => 'string',
            'excel.extension_detector.htm' => 'string',
            'excel.extension_detector.html' => 'string',
            'excel.extension_detector.csv' => 'string',
            'excel.extension_detector.tsv' => 'string',
            'excel.extension_detector.pdf' => 'string',
            'excel.value_binder.default' => 'string',
            'excel.cache.driver' => 'string',
            'excel.cache.batch.memory_limit' => 'integer',
            'excel.cache.illuminate.store' => 'NULL',
            'excel.transactions.handler' => 'string',
            'excel.transactions.db.connection' => 'NULL',
            'excel.temporary_files.local_path' => 'string',
            'excel.temporary_files.remote_disk' => 'NULL',
            'excel.temporary_files.remote_prefix' => 'NULL',
            'excel.temporary_files.force_resync_remote' => 'NULL',
            'filesystems.default' => 'string',
            'filesystems.disks.local.driver' => 'string',
            'filesystems.disks.local.root' => 'string',
            'filesystems.disks.local.serve' => 'boolean',
            'filesystems.disks.local.throw' => 'boolean',
            'filesystems.disks.local.report' => 'boolean',
            'filesystems.disks.public.driver' => 'string',
            'filesystems.disks.public.root' => 'string',
            'filesystems.disks.public.url' => 'string',
            'filesystems.disks.public.visibility' => 'string',
            'filesystems.disks.public.throw' => 'boolean',
            'filesystems.disks.public.report' => 'boolean',
            'filesystems.disks.s3.driver' => 'string',
            'filesystems.disks.s3.key' => 'string',
            'filesystems.disks.s3.secret' => 'string',
            'filesystems.disks.s3.region' => 'string',
            'filesystems.disks.s3.bucket' => 'string',
            'filesystems.disks.s3.options.CacheControl' => 'string',
            'filesystems.disks.s3.url' => 'NULL',
            'filesystems.disks.s3.endpoint' => 'NULL',
            'filesystems.disks.s3.use_path_style_endpoint' => 'boolean',
            'filesystems.disks.s3.throw' => 'boolean',
            'filesystems.disks.tenants.driver' => 'string',
            'filesystems.disks.tenants.root' => 'string',
            'filesystems.disks.client.driver' => 'string',
            'filesystems.disks.client.root' => 'string',
            'filesystems.disks.pages.driver' => 'string',
            'filesystems.disks.pages.root' => 'NULL',
            'filesystems.disks.seeds.driver' => 'string',
            'filesystems.disks.seeds.root' => 'string',
            'filesystems.disks.stubs.driver' => 'string',
            'filesystems.disks.stubs.root' => 'string',
            'filesystems.links./Users/<USER>/Documents/GitHub/sevensonsfarms/public/storage' => 'string',
            'filesystems.file_upload_prefix' => 'string',
            'filesystems.cloud' => 'string',
            'grazecart.sale_keywords' => 'array',
            'grazecart.default_map_center' => 'array',
            'grazecart.countries.USA' => 'string',
            'grazecart.countries.Canada' => 'string',
            'grazecart.states.AL' => 'string',
            'grazecart.states.AK' => 'string',
            'grazecart.states.AZ' => 'string',
            'grazecart.states.AR' => 'string',
            'grazecart.states.CA' => 'string',
            'grazecart.states.CO' => 'string',
            'grazecart.states.CT' => 'string',
            'grazecart.states.DE' => 'string',
            'grazecart.states.DC' => 'string',
            'grazecart.states.FL' => 'string',
            'grazecart.states.GA' => 'string',
            'grazecart.states.HI' => 'string',
            'grazecart.states.ID' => 'string',
            'grazecart.states.IL' => 'string',
            'grazecart.states.IN' => 'string',
            'grazecart.states.IA' => 'string',
            'grazecart.states.KS' => 'string',
            'grazecart.states.KY' => 'string',
            'grazecart.states.LA' => 'string',
            'grazecart.states.ME' => 'string',
            'grazecart.states.MD' => 'string',
            'grazecart.states.MA' => 'string',
            'grazecart.states.MI' => 'string',
            'grazecart.states.MN' => 'string',
            'grazecart.states.MS' => 'string',
            'grazecart.states.MO' => 'string',
            'grazecart.states.MT' => 'string',
            'grazecart.states.NE' => 'string',
            'grazecart.states.NV' => 'string',
            'grazecart.states.NH' => 'string',
            'grazecart.states.NJ' => 'string',
            'grazecart.states.NM' => 'string',
            'grazecart.states.NY' => 'string',
            'grazecart.states.NC' => 'string',
            'grazecart.states.ND' => 'string',
            'grazecart.states.OH' => 'string',
            'grazecart.states.OK' => 'string',
            'grazecart.states.OR' => 'string',
            'grazecart.states.PA' => 'string',
            'grazecart.states.RI' => 'string',
            'grazecart.states.SC' => 'string',
            'grazecart.states.SD' => 'string',
            'grazecart.states.TN' => 'string',
            'grazecart.states.TX' => 'string',
            'grazecart.states.UT' => 'string',
            'grazecart.states.VT' => 'string',
            'grazecart.states.VA' => 'string',
            'grazecart.states.WA' => 'string',
            'grazecart.states.WV' => 'string',
            'grazecart.states.WI' => 'string',
            'grazecart.states.WY' => 'string',
            'grazecart.states.PR' => 'string',
            'grazecart.provinces.NL' => 'string',
            'grazecart.provinces.PE' => 'string',
            'grazecart.provinces.NS' => 'string',
            'grazecart.provinces.NB' => 'string',
            'grazecart.provinces.QB' => 'string',
            'grazecart.provinces.ON' => 'string',
            'grazecart.provinces.MB' => 'string',
            'grazecart.provinces.SK' => 'string',
            'grazecart.provinces.AB' => 'string',
            'grazecart.provinces.BC' => 'string',
            'grazecart.provinces.YT' => 'string',
            'grazecart.provinces.NT' => 'string',
            'grazecart.provinces.NU' => 'string',
            'grazecart.referrals.none-selected' => 'string',
            'grazecart.referrals.Stockman' => 'string',
            'grazecart.referrals.Dirt to Soil' => 'string',
            'grazecart.referrals.3 Cow Marketing' => 'string',
            'grazecart.referrals.Graze Magazine' => 'string',
            'grazecart.referrals.IndependentProcessor' => 'string',
            'grazecart.referrals.Friend' => 'string',
            'grazecart.referrals.Online Search' => 'string',
            'grazecart.referrals.Other' => 'string',
            'grazecart.yearly_revenue_questions.none-selected' => 'string',
            'grazecart.yearly_revenue_questions.not selling online' => 'string',
            'grazecart.yearly_revenue_questions.0 to 50k' => 'string',
            'grazecart.yearly_revenue_questions.50k to 250k' => 'string',
            'grazecart.yearly_revenue_questions.250k to 1m' => 'string',
            'grazecart.yearly_revenue_questions.1m+' => 'string',
            'grazecart.new_storefront_enabled' => 'boolean',
            'grazecart.welcome_coupon_code' => 'string',
            'horizon.domain' => 'NULL',
            'horizon.path' => 'string',
            'horizon.use' => 'string',
            'horizon.prefix' => 'string',
            'horizon.middleware' => 'array',
            'horizon.waits.redis:default' => 'integer',
            'horizon.waits.redis:low' => 'integer',
            'horizon.trim.recent' => 'integer',
            'horizon.trim.pending' => 'integer',
            'horizon.trim.completed' => 'integer',
            'horizon.trim.recent_failed' => 'integer',
            'horizon.trim.failed' => 'integer',
            'horizon.trim.monitored' => 'integer',
            'horizon.silenced' => 'array',
            'horizon.metrics.trim_snapshots.job' => 'integer',
            'horizon.metrics.trim_snapshots.queue' => 'integer',
            'horizon.fast_termination' => 'boolean',
            'horizon.memory_limit' => 'integer',
            'horizon.defaults.supervisor-1.connection' => 'string',
            'horizon.defaults.supervisor-1.queue' => 'array',
            'horizon.defaults.supervisor-1.balance' => 'string',
            'horizon.defaults.supervisor-1.autoScalingStrategy' => 'string',
            'horizon.defaults.supervisor-1.minProcesses' => 'integer',
            'horizon.defaults.supervisor-1.maxProcesses' => 'integer',
            'horizon.defaults.supervisor-1.maxTime' => 'integer',
            'horizon.defaults.supervisor-1.maxJobs' => 'integer',
            'horizon.defaults.supervisor-1.memory' => 'integer',
            'horizon.defaults.supervisor-1.tries' => 'integer',
            'horizon.defaults.supervisor-1.timeout' => 'integer',
            'horizon.defaults.supervisor-1.nice' => 'integer',
            'horizon.environments.production.supervisor-1.maxProcesses' => 'integer',
            'horizon.environments.production.supervisor-1.balanceMaxShift' => 'integer',
            'horizon.environments.production.supervisor-1.balanceCooldown' => 'integer',
            'horizon.environments.production.supervisor-1.tries' => 'integer',
            'horizon.environments.staging.supervisor-1.maxProcesses' => 'integer',
            'horizon.environments.staging.supervisor-1.balanceMaxShift' => 'integer',
            'horizon.environments.staging.supervisor-1.balanceCooldown' => 'integer',
            'horizon.environments.local.supervisor-1.maxProcesses' => 'integer',
            'horizon.environments.local.supervisor-1.balanceMaxShift' => 'integer',
            'horizon.environments.local.supervisor-1.balanceCooldown' => 'integer',
            'horizon.environments.test.supervisor-1.maxProcesses' => 'integer',
            'horizon.environments.test.supervisor-1.balanceMaxShift' => 'integer',
            'horizon.environments.test.supervisor-1.balanceCooldown' => 'integer',
            'ide-helper.filename' => 'string',
            'ide-helper.models_filename' => 'string',
            'ide-helper.meta_filename' => 'string',
            'ide-helper.include_fluent' => 'boolean',
            'ide-helper.include_factory_builders' => 'boolean',
            'ide-helper.write_model_magic_where' => 'boolean',
            'ide-helper.write_model_external_builder_methods' => 'boolean',
            'ide-helper.write_model_relation_count_properties' => 'boolean',
            'ide-helper.write_eloquent_model_mixins' => 'boolean',
            'ide-helper.include_helpers' => 'boolean',
            'ide-helper.helper_files' => 'array',
            'ide-helper.model_locations' => 'array',
            'ide-helper.ignored_models' => 'array',
            'ide-helper.model_hooks' => 'array',
            'ide-helper.extra.Eloquent' => 'array',
            'ide-helper.extra.Session' => 'array',
            'ide-helper.magic' => 'array',
            'ide-helper.interfaces' => 'array',
            'ide-helper.model_camel_case_properties' => 'boolean',
            'ide-helper.type_overrides.integer' => 'string',
            'ide-helper.type_overrides.boolean' => 'string',
            'ide-helper.include_class_docblocks' => 'boolean',
            'ide-helper.force_fqn' => 'boolean',
            'ide-helper.use_generics_annotations' => 'boolean',
            'ide-helper.additional_relation_types' => 'array',
            'ide-helper.additional_relation_return_types' => 'array',
            'ide-helper.enforce_nullable_relationships' => 'boolean',
            'ide-helper.post_migrate' => 'array',
            'ide-helper.macroable_traits' => 'array',
            'ide-helper.custom_db_types' => 'array',
            'image.driver' => 'string',
            'inertia.ssr.enabled' => 'boolean',
            'inertia.ssr.url' => 'string',
            'inertia.testing.ensure_pages_exist' => 'boolean',
            'inertia.testing.page_paths' => 'array',
            'inertia.testing.page_extensions' => 'array',
            'inertia.history.encrypt' => 'boolean',
            'integrations.drip.id' => 'string',
            'integrations.drip.enabled' => 'boolean',
            'integrations.drip.api_key' => 'string',
            'integrations.drip.account_id' => 'string',
            'integrations.drip.default_subscriber_tags' => 'array',
            'integrations.drip.title' => 'string',
            'integrations.drip.logo' => 'string',
            'integrations.drip.svg' => 'NULL',
            'integrations.drip.setup_class' => 'string',
            'integrations.drip.show_slug' => 'string',
            'integrations.drip.edit_slug' => 'string',
            'integrations.drip.app_slug' => 'NULL',
            'integrations.drip.vendor' => 'string',
            'integrations.drip.short_description' => 'string',
            'integrations.drip.long_description' => 'string',
            'integrations.drip.categories' => 'array',
            'integrations.drip.highlights' => 'array',
            'integrations.drip.image' => 'string',
            'integrations.drip.screenshots' => 'array',
            'integrations.drip.support_url' => 'string',
            'integrations.drip.price_description' => 'string',
            'integrations.drip.product_id' => 'NULL',
            'integrations.pickup-manager.id' => 'string',
            'integrations.pickup-manager.enabled' => 'boolean',
            'integrations.pickup-manager.title' => 'string',
            'integrations.pickup-manager.logo' => 'NULL',
            'integrations.pickup-manager.svg' => 'string',
            'integrations.pickup-manager.setup_class' => 'NULL',
            'integrations.pickup-manager.show_slug' => 'string',
            'integrations.pickup-manager.edit_slug' => 'string',
            'integrations.pickup-manager.app_slug' => 'string',
            'integrations.pickup-manager.vendor' => 'string',
            'integrations.pickup-manager.short_description' => 'string',
            'integrations.pickup-manager.long_description' => 'string',
            'integrations.pickup-manager.categories' => 'array',
            'integrations.pickup-manager.highlights' => 'array',
            'integrations.pickup-manager.image' => 'string',
            'integrations.pickup-manager.screenshots' => 'array',
            'integrations.pickup-manager.support_url' => 'string',
            'integrations.pickup-manager.price_description' => 'string',
            'livewire.class_namespace' => 'string',
            'livewire.view_path' => 'string',
            'livewire.layout' => 'string',
            'livewire.lazy_placeholder' => 'NULL',
            'livewire.temporary_file_upload.disk' => 'NULL',
            'livewire.temporary_file_upload.rules' => 'NULL',
            'livewire.temporary_file_upload.directory' => 'NULL',
            'livewire.temporary_file_upload.middleware' => 'NULL',
            'livewire.temporary_file_upload.preview_mimes' => 'array',
            'livewire.temporary_file_upload.max_upload_time' => 'integer',
            'livewire.temporary_file_upload.cleanup' => 'boolean',
            'livewire.render_on_redirect' => 'boolean',
            'livewire.legacy_model_binding' => 'boolean',
            'livewire.inject_assets' => 'boolean',
            'livewire.navigate.show_progress_bar' => 'boolean',
            'livewire.navigate.progress_bar_color' => 'string',
            'livewire.inject_morph_markers' => 'boolean',
            'livewire.pagination_theme' => 'string',
            'logging.default' => 'string',
            'logging.deprecations.channel' => 'NULL',
            'logging.deprecations.trace' => 'boolean',
            'logging.channels.stack.driver' => 'string',
            'logging.channels.stack.channels' => 'array',
            'logging.channels.stack.ignore_exceptions' => 'boolean',
            'logging.channels.single.driver' => 'string',
            'logging.channels.single.path' => 'string',
            'logging.channels.single.level' => 'string',
            'logging.channels.single.replace_placeholders' => 'boolean',
            'logging.channels.daily.driver' => 'string',
            'logging.channels.daily.path' => 'string',
            'logging.channels.daily.level' => 'string',
            'logging.channels.daily.days' => 'string',
            'logging.channels.daily.replace_placeholders' => 'boolean',
            'logging.channels.slack.driver' => 'string',
            'logging.channels.slack.url' => 'string',
            'logging.channels.slack.username' => 'string',
            'logging.channels.slack.emoji' => 'string',
            'logging.channels.slack.level' => 'string',
            'logging.channels.slack.replace_placeholders' => 'boolean',
            'logging.channels.papertrail.driver' => 'string',
            'logging.channels.papertrail.level' => 'string',
            'logging.channels.papertrail.handler' => 'string',
            'logging.channels.papertrail.handler_with.host' => 'NULL',
            'logging.channels.papertrail.handler_with.port' => 'NULL',
            'logging.channels.papertrail.handler_with.connectionString' => 'string',
            'logging.channels.papertrail.processors' => 'array',
            'logging.channels.stderr.driver' => 'string',
            'logging.channels.stderr.level' => 'string',
            'logging.channels.stderr.handler' => 'string',
            'logging.channels.stderr.handler_with.stream' => 'string',
            'logging.channels.stderr.formatter' => 'NULL',
            'logging.channels.stderr.processors' => 'array',
            'logging.channels.syslog.driver' => 'string',
            'logging.channels.syslog.level' => 'string',
            'logging.channels.syslog.facility' => 'integer',
            'logging.channels.syslog.replace_placeholders' => 'boolean',
            'logging.channels.errorlog.driver' => 'string',
            'logging.channels.errorlog.level' => 'string',
            'logging.channels.errorlog.replace_placeholders' => 'boolean',
            'logging.channels.null.driver' => 'string',
            'logging.channels.null.handler' => 'string',
            'logging.channels.emergency.path' => 'string',
            'logging.channels.bugsnag.driver' => 'string',
            'logging.channels.deprecations.driver' => 'string',
            'logging.channels.deprecations.handler' => 'string',
            'mail.default' => 'string',
            'mail.mailers.smtp.transport' => 'string',
            'mail.mailers.smtp.url' => 'NULL',
            'mail.mailers.smtp.host' => 'string',
            'mail.mailers.smtp.port' => 'string',
            'mail.mailers.smtp.encryption' => 'string',
            'mail.mailers.smtp.username' => 'string',
            'mail.mailers.smtp.password' => 'string',
            'mail.mailers.smtp.timeout' => 'NULL',
            'mail.mailers.smtp.local_domain' => 'string',
            'mail.mailers.ses.transport' => 'string',
            'mail.mailers.postmark.transport' => 'string',
            'mail.mailers.resend.transport' => 'string',
            'mail.mailers.sendmail.transport' => 'string',
            'mail.mailers.sendmail.path' => 'string',
            'mail.mailers.log.transport' => 'string',
            'mail.mailers.log.channel' => 'NULL',
            'mail.mailers.array.transport' => 'string',
            'mail.mailers.failover.transport' => 'string',
            'mail.mailers.failover.mailers' => 'array',
            'mail.mailers.roundrobin.transport' => 'string',
            'mail.mailers.roundrobin.mailers' => 'array',
            'mail.mailers.mailgun.transport' => 'string',
            'mail.from.address' => 'string',
            'mail.from.name' => 'string',
            'mail.markdown.theme' => 'string',
            'mail.markdown.paths' => 'array',
            'mail.to' => 'array',
            'mail.no_reply.address' => 'string',
            'mail.marketing_from.address' => 'string',
            'mail.marketing_from.name' => 'string',
            'mail.notifications.new_registration' => 'array',
            'mail.notifications.subscription_demand_report' => 'array',
            'mail.notifications.order_credit_report' => 'array',
            'queue.default' => 'string',
            'queue.connections.sync.driver' => 'string',
            'queue.connections.database.driver' => 'string',
            'queue.connections.database.connection' => 'NULL',
            'queue.connections.database.table' => 'string',
            'queue.connections.database.queue' => 'string',
            'queue.connections.database.retry_after' => 'integer',
            'queue.connections.database.after_commit' => 'boolean',
            'queue.connections.beanstalkd.driver' => 'string',
            'queue.connections.beanstalkd.host' => 'string',
            'queue.connections.beanstalkd.queue' => 'string',
            'queue.connections.beanstalkd.retry_after' => 'integer',
            'queue.connections.beanstalkd.block_for' => 'integer',
            'queue.connections.beanstalkd.after_commit' => 'boolean',
            'queue.connections.sqs.driver' => 'string',
            'queue.connections.sqs.key' => 'NULL',
            'queue.connections.sqs.secret' => 'NULL',
            'queue.connections.sqs.prefix' => 'string',
            'queue.connections.sqs.queue' => 'string',
            'queue.connections.sqs.suffix' => 'NULL',
            'queue.connections.sqs.region' => 'string',
            'queue.connections.sqs.after_commit' => 'boolean',
            'queue.connections.redis.driver' => 'string',
            'queue.connections.redis.connection' => 'string',
            'queue.connections.redis.queue' => 'string',
            'queue.connections.redis.retry_after' => 'integer',
            'queue.connections.redis.block_for' => 'NULL',
            'queue.connections.redis.after_commit' => 'boolean',
            'queue.connections.central.driver' => 'string',
            'queue.connections.central.table' => 'string',
            'queue.connections.central.queue' => 'string',
            'queue.connections.central.retry_after' => 'integer',
            'queue.connections.central.after_commit' => 'boolean',
            'queue.connections.central.central' => 'boolean',
            'queue.batching.database' => 'string',
            'queue.batching.table' => 'string',
            'queue.failed.driver' => 'string',
            'queue.failed.database' => 'string',
            'queue.failed.table' => 'string',
            'sanctum.stateful' => 'array',
            'sanctum.guard' => 'array',
            'sanctum.expiration' => 'NULL',
            'sanctum.token_prefix' => 'string',
            'sanctum.middleware.authenticate_session' => 'string',
            'sanctum.middleware.encrypt_cookies' => 'string',
            'sanctum.middleware.validate_csrf_token' => 'string',
            'scout.driver' => 'string',
            'scout.prefix' => 'string',
            'scout.queue' => 'boolean',
            'scout.after_commit' => 'boolean',
            'scout.chunk.searchable' => 'integer',
            'scout.chunk.unsearchable' => 'integer',
            'scout.soft_delete' => 'boolean',
            'scout.identify' => 'boolean',
            'scout.algolia.id' => 'string',
            'scout.algolia.secret' => 'string',
            'scout.meilisearch.host' => 'string',
            'scout.meilisearch.key' => 'string',
            'scout.meilisearch.index-settings.App\Models\Product.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Product.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.enabled' => 'boolean',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.minWordSizeForTypos.oneTypo' => 'integer',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.minWordSizeForTypos.twoTypos' => 'integer',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.disableOnWords' => 'array',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.disableOnAttributes' => 'array',
            'scout.typesense.client-settings.api_key' => 'string',
            'scout.typesense.client-settings.nodes.0.host' => 'string',
            'scout.typesense.client-settings.nodes.0.port' => 'string',
            'scout.typesense.client-settings.nodes.0.path' => 'string',
            'scout.typesense.client-settings.nodes.0.protocol' => 'string',
            'scout.typesense.client-settings.nearest_node.host' => 'string',
            'scout.typesense.client-settings.nearest_node.port' => 'string',
            'scout.typesense.client-settings.nearest_node.path' => 'string',
            'scout.typesense.client-settings.nearest_node.protocol' => 'string',
            'scout.typesense.client-settings.connection_timeout_seconds' => 'integer',
            'scout.typesense.client-settings.healthcheck_interval_seconds' => 'integer',
            'scout.typesense.client-settings.num_retries' => 'integer',
            'scout.typesense.client-settings.retry_interval_seconds' => 'integer',
            'scout.typesense.model-settings' => 'array',
            'services.postmark.token' => 'NULL',
            'services.ses.key' => 'NULL',
            'services.ses.secret' => 'NULL',
            'services.ses.region' => 'string',
            'services.resend.key' => 'NULL',
            'services.slack.hook' => 'string',
            'services.mailgun.central_domain' => 'string',
            'services.mailgun.domain' => 'string',
            'services.mailgun.marketing_domain' => 'string',
            'services.mailgun.secret' => 'string',
            'services.mailgun.endpoint' => 'string',
            'services.mailgun.scheme' => 'string',
            'services.stripe.key' => 'string',
            'services.stripe.secret' => 'string',
            'services.stripe.account' => 'string',
            'services.stripe.managed' => 'boolean',
            'services.stripe.webhook.secret' => 'string',
            'services.stripe.webhook.tolerance' => 'integer',
            'services.stripe.debug' => 'boolean',
            'services.geocodio.key' => 'string',
            'services.geocoderCa.key' => 'string',
            'services.drip.key' => 'string',
            'services.drip.account_id' => 'string',
            'services.facebook.pixel_id' => 'NULL',
            'services.facebook.access_token' => 'string',
            'services.facebook.test_event_code' => 'string',
            'services.google.geocoder_api_key' => 'string',
            'services.google.places_js_api_key' => 'string',
            'services.google.sheets_api_key' => 'string',
            'services.google.spreadsheet_id' => 'string',
            'services.google.worksheet_name' => 'string',
            'services.google.place_id' => 'string',
            'services.google.analytics_id' => 'NULL',
            'services.google.ads_id' => 'NULL',
            'services.twilio.default' => 'string',
            'services.twilio.connections.twilio.sid' => 'string',
            'services.twilio.connections.twilio.token' => 'string',
            'services.twilio.connections.twilio.from' => 'string',
            'services.horizon.secret' => 'string',
            'services.segment.write_key' => 'string',
            'services.stax.partner_api_key' => 'string',
            'services.pay_fac.api_key' => 'string',
            'services.pay_fac.company_id' => 'string',
            'services.pay_fac.js_client_key' => 'string',
            'services.pay_fac.stripe_key' => 'NULL',
            'services.launch_darkly.sdk_key' => 'NULL',
            'services.tolstoy.app_key' => 'string',
            'services.cloudfront.url' => 'string',
            'sluggable.source' => 'NULL',
            'sluggable.maxLength' => 'NULL',
            'sluggable.maxLengthKeepWords' => 'boolean',
            'sluggable.method' => 'NULL',
            'sluggable.separator' => 'string',
            'sluggable.unique' => 'boolean',
            'sluggable.uniqueSuffix' => 'NULL',
            'sluggable.firstUniqueSuffix' => 'integer',
            'sluggable.includeTrashed' => 'boolean',
            'sluggable.reserved' => 'NULL',
            'sluggable.onUpdate' => 'boolean',
            'sluggable.slugEngineOptions' => 'array',
            'telescope.enabled' => 'boolean',
            'telescope.domain' => 'NULL',
            'telescope.path' => 'string',
            'telescope.driver' => 'string',
            'telescope.storage.database.connection' => 'string',
            'telescope.storage.database.chunk' => 'integer',
            'telescope.queue.connection' => 'NULL',
            'telescope.queue.queue' => 'NULL',
            'telescope.queue.delay' => 'integer',
            'telescope.middleware' => 'array',
            'telescope.only_paths' => 'array',
            'telescope.ignore_paths' => 'array',
            'telescope.ignore_commands' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\CacheWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CommandWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CommandWatcher.ignore' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\DumpWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\EventWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ExceptionWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\JobWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\LogWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\MailWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.events' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\NotificationWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.ignore_packages' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.slow' => 'integer',
            'telescope.watchers.Laravel\Telescope\Watchers\RedisWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.size_limit' => 'integer',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_abilities' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_packages' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ScheduleWatcher' => 'boolean',
            'theme.id' => 'string',
            'theme.title' => 'string',
            'theme.description' => 'string',
            'theme.view_path' => 'string',
            'theme.settings' => 'NULL',
            'theme.css' => 'NULL',
            'theme.css_preview' => 'NULL',
            'theme.custom_css' => 'NULL',
            'theme.theme_builder' => 'string',
            'theme.google_web_fonts' => 'array',
            'tinker.commands' => 'array',
            'tinker.alias' => 'array',
            'tinker.dont_alias' => 'array',
            'twilio-notification-channel.enabled' => 'boolean',
            'twilio-notification-channel.username' => 'NULL',
            'twilio-notification-channel.password' => 'NULL',
            'twilio-notification-channel.auth_token' => 'string',
            'twilio-notification-channel.account_sid' => 'string',
            'twilio-notification-channel.from' => 'string',
            'twilio-notification-channel.alphanumeric_sender' => 'NULL',
            'twilio-notification-channel.shorten_urls' => 'boolean',
            'twilio-notification-channel.sms_service_sid' => 'NULL',
            'twilio-notification-channel.debug_to' => 'NULL',
            'twilio-notification-channel.ignored_error_codes' => 'array',
            'twilio-notification-channel.message_size_limit' => 'integer',
            'hashing.driver' => 'string',
            'hashing.bcrypt.rounds' => 'string',
            'hashing.bcrypt.verify' => 'boolean',
            'hashing.bcrypt.limit' => 'NULL',
            'hashing.argon.memory' => 'integer',
            'hashing.argon.threads' => 'integer',
            'hashing.argon.time' => 'integer',
            'hashing.argon.verify' => 'boolean',
            'hashing.rehash_on_login' => 'boolean',
            'session.driver' => 'string',
            'session.lifetime' => 'integer',
            'session.expire_on_close' => 'boolean',
            'session.encrypt' => 'boolean',
            'session.files' => 'string',
            'session.connection' => 'string',
            'session.table' => 'string',
            'session.store' => 'NULL',
            'session.lottery' => 'array',
            'session.cookie' => 'string',
            'session.path' => 'string',
            'session.domain' => 'NULL',
            'session.secure' => 'NULL',
            'session.http_only' => 'boolean',
            'session.same_site' => 'string',
            'session.partitioned' => 'boolean',
            'concurrency.default' => 'string',
            'broadcasting.default' => 'string',
            'broadcasting.connections.reverb.driver' => 'string',
            'broadcasting.connections.reverb.key' => 'NULL',
            'broadcasting.connections.reverb.secret' => 'NULL',
            'broadcasting.connections.reverb.app_id' => 'NULL',
            'broadcasting.connections.reverb.options.host' => 'NULL',
            'broadcasting.connections.reverb.options.port' => 'integer',
            'broadcasting.connections.reverb.options.scheme' => 'string',
            'broadcasting.connections.reverb.options.useTLS' => 'boolean',
            'broadcasting.connections.reverb.client_options' => 'array',
            'broadcasting.connections.pusher.driver' => 'string',
            'broadcasting.connections.pusher.key' => 'NULL',
            'broadcasting.connections.pusher.secret' => 'NULL',
            'broadcasting.connections.pusher.app_id' => 'NULL',
            'broadcasting.connections.pusher.options.cluster' => 'NULL',
            'broadcasting.connections.pusher.options.host' => 'string',
            'broadcasting.connections.pusher.options.port' => 'integer',
            'broadcasting.connections.pusher.options.scheme' => 'string',
            'broadcasting.connections.pusher.options.encrypted' => 'boolean',
            'broadcasting.connections.pusher.options.useTLS' => 'boolean',
            'broadcasting.connections.pusher.client_options' => 'array',
            'broadcasting.connections.ably.driver' => 'string',
            'broadcasting.connections.ably.key' => 'NULL',
            'broadcasting.connections.log.driver' => 'string',
            'broadcasting.connections.null.driver' => 'string',
            'view.paths' => 'array',
            'view.compiled' => 'string',
            'debugbar.enabled' => 'boolean',
            'debugbar.hide_empty_tabs' => 'boolean',
            'debugbar.except' => 'array',
            'debugbar.storage.enabled' => 'boolean',
            'debugbar.storage.open' => 'NULL',
            'debugbar.storage.driver' => 'string',
            'debugbar.storage.path' => 'string',
            'debugbar.storage.connection' => 'NULL',
            'debugbar.storage.provider' => 'string',
            'debugbar.storage.hostname' => 'string',
            'debugbar.storage.port' => 'integer',
            'debugbar.editor' => 'string',
            'debugbar.remote_sites_path' => 'NULL',
            'debugbar.local_sites_path' => 'NULL',
            'debugbar.include_vendors' => 'boolean',
            'debugbar.capture_ajax' => 'boolean',
            'debugbar.add_ajax_timing' => 'boolean',
            'debugbar.ajax_handler_auto_show' => 'boolean',
            'debugbar.ajax_handler_enable_tab' => 'boolean',
            'debugbar.defer_datasets' => 'boolean',
            'debugbar.error_handler' => 'boolean',
            'debugbar.clockwork' => 'boolean',
            'debugbar.collectors.phpinfo' => 'boolean',
            'debugbar.collectors.messages' => 'boolean',
            'debugbar.collectors.time' => 'boolean',
            'debugbar.collectors.memory' => 'boolean',
            'debugbar.collectors.exceptions' => 'boolean',
            'debugbar.collectors.log' => 'boolean',
            'debugbar.collectors.db' => 'boolean',
            'debugbar.collectors.views' => 'boolean',
            'debugbar.collectors.route' => 'boolean',
            'debugbar.collectors.auth' => 'boolean',
            'debugbar.collectors.gate' => 'boolean',
            'debugbar.collectors.session' => 'boolean',
            'debugbar.collectors.symfony_request' => 'boolean',
            'debugbar.collectors.mail' => 'boolean',
            'debugbar.collectors.laravel' => 'boolean',
            'debugbar.collectors.events' => 'boolean',
            'debugbar.collectors.default_request' => 'boolean',
            'debugbar.collectors.logs' => 'boolean',
            'debugbar.collectors.files' => 'boolean',
            'debugbar.collectors.config' => 'boolean',
            'debugbar.collectors.cache' => 'boolean',
            'debugbar.collectors.models' => 'boolean',
            'debugbar.collectors.livewire' => 'boolean',
            'debugbar.collectors.jobs' => 'boolean',
            'debugbar.collectors.pennant' => 'boolean',
            'debugbar.options.time.memory_usage' => 'boolean',
            'debugbar.options.messages.trace' => 'boolean',
            'debugbar.options.messages.capture_dumps' => 'boolean',
            'debugbar.options.memory.reset_peak' => 'boolean',
            'debugbar.options.memory.with_baseline' => 'boolean',
            'debugbar.options.memory.precision' => 'integer',
            'debugbar.options.auth.show_name' => 'boolean',
            'debugbar.options.auth.show_guards' => 'boolean',
            'debugbar.options.db.with_params' => 'boolean',
            'debugbar.options.db.exclude_paths' => 'array',
            'debugbar.options.db.backtrace' => 'boolean',
            'debugbar.options.db.backtrace_exclude_paths' => 'array',
            'debugbar.options.db.timeline' => 'boolean',
            'debugbar.options.db.duration_background' => 'boolean',
            'debugbar.options.db.explain.enabled' => 'boolean',
            'debugbar.options.db.hints' => 'boolean',
            'debugbar.options.db.show_copy' => 'boolean',
            'debugbar.options.db.slow_threshold' => 'boolean',
            'debugbar.options.db.memory_usage' => 'boolean',
            'debugbar.options.db.soft_limit' => 'integer',
            'debugbar.options.db.hard_limit' => 'integer',
            'debugbar.options.mail.timeline' => 'boolean',
            'debugbar.options.mail.show_body' => 'boolean',
            'debugbar.options.views.timeline' => 'boolean',
            'debugbar.options.views.data' => 'boolean',
            'debugbar.options.views.group' => 'integer',
            'debugbar.options.views.exclude_paths' => 'array',
            'debugbar.options.route.label' => 'boolean',
            'debugbar.options.session.hiddens' => 'array',
            'debugbar.options.symfony_request.label' => 'boolean',
            'debugbar.options.symfony_request.hiddens' => 'array',
            'debugbar.options.events.data' => 'boolean',
            'debugbar.options.logs.file' => 'NULL',
            'debugbar.options.cache.values' => 'boolean',
            'debugbar.inject' => 'boolean',
            'debugbar.route_prefix' => 'string',
            'debugbar.route_middleware' => 'array',
            'debugbar.route_domain' => 'NULL',
            'debugbar.theme' => 'string',
            'debugbar.debug_backtrace_limit' => 'integer',
            'cookie-consent.enabled' => 'boolean',
            'cookie-consent.cookie_name' => 'string',
            'cookie-consent.cookie_lifetime' => 'integer',
        ]));
    override(\Illuminate\Support\Facades\Config::get(), map([
            'app.name' => 'string',
            'app.env' => 'string',
            'app.debug' => 'boolean',
            'app.url' => 'string',
            'app.frontend_url' => 'string',
            'app.asset_url' => 'NULL',
            'app.timezone' => 'string',
            'app.locale' => 'string',
            'app.fallback_locale' => 'string',
            'app.faker_locale' => 'string',
            'app.cipher' => 'string',
            'app.key' => 'string',
            'app.previous_keys' => 'array',
            'app.maintenance.driver' => 'string',
            'app.maintenance.store' => 'string',
            'app.providers' => 'array',
            'app.aliases.App' => 'string',
            'app.aliases.Arr' => 'string',
            'app.aliases.Artisan' => 'string',
            'app.aliases.Auth' => 'string',
            'app.aliases.Blade' => 'string',
            'app.aliases.Broadcast' => 'string',
            'app.aliases.Bus' => 'string',
            'app.aliases.Cache' => 'string',
            'app.aliases.Concurrency' => 'string',
            'app.aliases.Config' => 'string',
            'app.aliases.Context' => 'string',
            'app.aliases.Cookie' => 'string',
            'app.aliases.Crypt' => 'string',
            'app.aliases.Date' => 'string',
            'app.aliases.DB' => 'string',
            'app.aliases.Eloquent' => 'string',
            'app.aliases.Event' => 'string',
            'app.aliases.File' => 'string',
            'app.aliases.Gate' => 'string',
            'app.aliases.Hash' => 'string',
            'app.aliases.Http' => 'string',
            'app.aliases.Js' => 'string',
            'app.aliases.Lang' => 'string',
            'app.aliases.Log' => 'string',
            'app.aliases.Mail' => 'string',
            'app.aliases.Notification' => 'string',
            'app.aliases.Number' => 'string',
            'app.aliases.Password' => 'string',
            'app.aliases.Process' => 'string',
            'app.aliases.Queue' => 'string',
            'app.aliases.RateLimiter' => 'string',
            'app.aliases.Redirect' => 'string',
            'app.aliases.Request' => 'string',
            'app.aliases.Response' => 'string',
            'app.aliases.Route' => 'string',
            'app.aliases.Schedule' => 'string',
            'app.aliases.Schema' => 'string',
            'app.aliases.Session' => 'string',
            'app.aliases.Storage' => 'string',
            'app.aliases.Str' => 'string',
            'app.aliases.URL' => 'string',
            'app.aliases.Uri' => 'string',
            'app.aliases.Validator' => 'string',
            'app.aliases.View' => 'string',
            'app.aliases.Vite' => 'string',
            'app.aliases.Bugsnag' => 'string',
            'app.aliases.Image' => 'string',
            'app.aliases.Inspiring' => 'string',
            'app.aliases.Redis' => 'string',
            'app.aliases.Socialite' => 'string',
            'app.domains.root' => 'string',
            'app.domains.admin' => 'NULL',
            'app.domains.api' => 'string',
            'app.deleted_user_email' => 'string',
            'app.inventory.spreadsheet_id' => 'string',
            'app.inventory.worksheet_name' => 'string',
            'app.homepage_featured_bundle_ids' => 'string',
            'auth.defaults.guard' => 'string',
            'auth.defaults.passwords' => 'string',
            'auth.guards.web.driver' => 'string',
            'auth.guards.web.provider' => 'string',
            'auth.guards.api.driver' => 'string',
            'auth.guards.api.provider' => 'string',
            'auth.guards.api.hash' => 'boolean',
            'auth.guards.sanctum.driver' => 'string',
            'auth.guards.sanctum.provider' => 'NULL',
            'auth.providers.users.driver' => 'string',
            'auth.providers.users.model' => 'string',
            'auth.passwords.users.provider' => 'string',
            'auth.passwords.users.table' => 'string',
            'auth.passwords.users.expire' => 'integer',
            'auth.passwords.users.throttle' => 'integer',
            'auth.password_timeout' => 'integer',
            'bugsnag.api_key' => 'string',
            'bugsnag.app_type' => 'NULL',
            'bugsnag.app_version' => 'NULL',
            'bugsnag.batch_sending' => 'NULL',
            'bugsnag.endpoint' => 'NULL',
            'bugsnag.filters' => 'NULL',
            'bugsnag.hostname' => 'NULL',
            'bugsnag.proxy' => 'array',
            'bugsnag.project_root' => 'NULL',
            'bugsnag.project_root_regex' => 'NULL',
            'bugsnag.strip_path' => 'NULL',
            'bugsnag.strip_path_regex' => 'NULL',
            'bugsnag.query' => 'boolean',
            'bugsnag.bindings' => 'boolean',
            'bugsnag.octane_breadcrumbs' => 'boolean',
            'bugsnag.release_stage' => 'NULL',
            'bugsnag.notify_release_stages' => 'array',
            'bugsnag.send_code' => 'boolean',
            'bugsnag.callbacks' => 'boolean',
            'bugsnag.user' => 'boolean',
            'bugsnag.logger_notify_level' => 'NULL',
            'bugsnag.auto_capture_sessions' => 'boolean',
            'bugsnag.session_endpoint' => 'NULL',
            'bugsnag.build_endpoint' => 'NULL',
            'bugsnag.discard_classes' => 'NULL',
            'bugsnag.redacted_keys' => 'NULL',
            'bugsnag.feature_flags' => 'array',
            'bugsnag.max_breadcrumbs' => 'NULL',
            'bugsnag.attach_hidden_context' => 'boolean',
            'cache.default' => 'string',
            'cache.stores.array.driver' => 'string',
            'cache.stores.array.serialize' => 'boolean',
            'cache.stores.database.driver' => 'string',
            'cache.stores.database.connection' => 'NULL',
            'cache.stores.database.table' => 'string',
            'cache.stores.database.lock_connection' => 'NULL',
            'cache.stores.database.lock_table' => 'NULL',
            'cache.stores.file.driver' => 'string',
            'cache.stores.file.path' => 'string',
            'cache.stores.file.lock_path' => 'string',
            'cache.stores.memcached.driver' => 'string',
            'cache.stores.memcached.persistent_id' => 'NULL',
            'cache.stores.memcached.sasl' => 'array',
            'cache.stores.memcached.options' => 'array',
            'cache.stores.memcached.servers.0.host' => 'string',
            'cache.stores.memcached.servers.0.port' => 'integer',
            'cache.stores.memcached.servers.0.weight' => 'integer',
            'cache.stores.redis.driver' => 'string',
            'cache.stores.redis.connection' => 'string',
            'cache.stores.redis.lock_connection' => 'string',
            'cache.stores.dynamodb.driver' => 'string',
            'cache.stores.dynamodb.key' => 'NULL',
            'cache.stores.dynamodb.secret' => 'NULL',
            'cache.stores.dynamodb.region' => 'string',
            'cache.stores.dynamodb.table' => 'string',
            'cache.stores.dynamodb.endpoint' => 'NULL',
            'cache.stores.octane.driver' => 'string',
            'cache.prefix' => 'string',
            'cache.limiter' => 'string',
            'canny.app_id' => 'string',
            'cart.default.type' => 'string',
            'cart.default.id' => 'NULL',
            'cart.default.purchase_type' => 'string',
            'cart.default.date_id' => 'NULL',
            'cart.default.delivery_method_id' => 'NULL',
            'cart.default.items' => 'object',
            'cart.default.subscription' => 'NULL',
            'cart.default.notes' => 'string',
            'cart.default.is_gift' => 'boolean',
            'cart.default.recipient_email' => 'string',
            'cart.default.recipient_notes' => 'string',
            'cart.default.discounts.coupons' => 'object',
            'cart.default.discounts.gift_card.name' => 'string',
            'cart.default.discounts.gift_card.code' => 'string',
            'cart.default.discounts.gift_card.amount' => 'integer',
            'cart.default.discounts.store_credit.amount' => 'integer',
            'cart.default.customer.id' => 'NULL',
            'cart.default.customer.first_name' => 'string',
            'cart.default.customer.last_name' => 'string',
            'cart.default.customer.email' => 'string',
            'cart.default.customer.phone' => 'string',
            'cart.default.customer.save_for_later' => 'boolean',
            'cart.default.customer.opt_in_to_sms' => 'boolean',
            'cart.default.customer.subscribed_to_sms' => 'boolean',
            'cart.default.shipping.address_id' => 'NULL',
            'cart.default.shipping.street' => 'string',
            'cart.default.shipping.street_2' => 'string',
            'cart.default.shipping.city' => 'string',
            'cart.default.shipping.state' => 'string',
            'cart.default.shipping.zip' => 'string',
            'cart.default.shipping.country' => 'string',
            'cart.default.shipping.save_for_later' => 'boolean',
            'cart.default.billing.method' => 'NULL',
            'cart.default.billing.source_id' => 'NULL',
            'cart.default.billing.save_for_later' => 'boolean',
            'cart.database.date_id' => 'NULL',
            'cart.database.delivery_method_id' => 'NULL',
            'cart.database.items' => 'array',
            'cart.database.subscription' => 'NULL',
            'cart.database.notes' => 'string',
            'cart.database.is_gift' => 'boolean',
            'cart.database.recipient_email' => 'string',
            'cart.database.recipient_notes' => 'string',
            'cart.database.discounts.coupons' => 'array',
            'cart.database.discounts.gift_card.name' => 'string',
            'cart.database.discounts.gift_card.code' => 'string',
            'cart.database.discounts.gift_card.amount' => 'integer',
            'cart.database.discounts.store_credit.amount' => 'integer',
            'cart.database.customer.id' => 'NULL',
            'cart.database.customer.first_name' => 'string',
            'cart.database.customer.last_name' => 'string',
            'cart.database.customer.email' => 'string',
            'cart.database.customer.phone' => 'string',
            'cart.database.customer.save_for_later' => 'boolean',
            'cart.database.customer.opt_in_to_sms' => 'boolean',
            'cart.database.customer.subscribed_to_sms' => 'boolean',
            'cart.database.shipping.address_id' => 'NULL',
            'cart.database.shipping.street' => 'string',
            'cart.database.shipping.street_2' => 'string',
            'cart.database.shipping.city' => 'string',
            'cart.database.shipping.state' => 'string',
            'cart.database.shipping.zip' => 'string',
            'cart.database.shipping.country' => 'string',
            'cart.database.shipping.save_for_later' => 'boolean',
            'cart.database.billing.method' => 'NULL',
            'cart.database.billing.source_id' => 'NULL',
            'cart.database.billing.save_for_later' => 'boolean',
            'cart.preorder.type' => 'string',
            'cart.preorder.date_id' => 'NULL',
            'cart.preorder.delivery_method_id' => 'NULL',
            'cart.preorder.items' => 'object',
            'cart.preorder.notes' => 'string',
            'cart.preorder.is_gift' => 'boolean',
            'cart.preorder.recipient_email' => 'string',
            'cart.preorder.recipient_notes' => 'string',
            'cart.preorder.discounts.coupons' => 'object',
            'cart.preorder.discounts.gift_card.name' => 'string',
            'cart.preorder.discounts.gift_card.code' => 'string',
            'cart.preorder.discounts.gift_card.amount' => 'integer',
            'cart.preorder.discounts.store_credit.amount' => 'integer',
            'cart.preorder.customer.id' => 'NULL',
            'cart.preorder.customer.first_name' => 'string',
            'cart.preorder.customer.last_name' => 'string',
            'cart.preorder.customer.email' => 'string',
            'cart.preorder.customer.phone' => 'string',
            'cart.preorder.customer.save_for_later' => 'boolean',
            'cart.preorder.customer.opt_in_to_sms' => 'boolean',
            'cart.preorder.customer.subscribed_to_sms' => 'boolean',
            'cart.preorder.shipping.street' => 'string',
            'cart.preorder.shipping.street_2' => 'string',
            'cart.preorder.shipping.city' => 'string',
            'cart.preorder.shipping.state' => 'string',
            'cart.preorder.shipping.zip' => 'string',
            'cart.preorder.shipping.country' => 'string',
            'cart.preorder.shipping.save_for_later' => 'boolean',
            'cart.preorder.billing.method' => 'NULL',
            'cart.preorder.billing.source_id' => 'NULL',
            'cart.preorder.billing.save_for_later' => 'boolean',
            'cart.gift-card.type' => 'string',
            'cart.gift-card.items' => 'object',
            'cart.gift-card.notes' => 'string',
            'cart.gift-card.discounts.coupons' => 'object',
            'cart.gift-card.discounts.gift_card.name' => 'string',
            'cart.gift-card.discounts.gift_card.code' => 'string',
            'cart.gift-card.discounts.gift_card.amount' => 'integer',
            'cart.gift-card.discounts.store_credit.amount' => 'integer',
            'cart.gift-card.customer.id' => 'NULL',
            'cart.gift-card.customer.first_name' => 'string',
            'cart.gift-card.customer.last_name' => 'string',
            'cart.gift-card.customer.email' => 'string',
            'cart.gift-card.customer.phone' => 'string',
            'cart.gift-card.customer.save_for_later' => 'boolean',
            'cart.gift-card.customer.opt_in_to_sms' => 'boolean',
            'cart.gift-card.customer.subscribed_to_sms' => 'boolean',
            'cart.gift-card.shipping.street' => 'string',
            'cart.gift-card.shipping.street_2' => 'string',
            'cart.gift-card.shipping.city' => 'string',
            'cart.gift-card.shipping.state' => 'string',
            'cart.gift-card.shipping.zip' => 'string',
            'cart.gift-card.shipping.country' => 'string',
            'cart.gift-card.shipping.save_for_later' => 'boolean',
            'cart.gift-card.billing.method' => 'string',
            'cart.gift-card.billing.source_id' => 'NULL',
            'cart.gift-card.billing.save_for_later' => 'boolean',
            'clockwork.enable' => 'NULL',
            'clockwork.collect_data_always' => 'boolean',
            'clockwork.storage' => 'string',
            'clockwork.storage_files_path' => 'string',
            'clockwork.storage_sql_database' => 'string',
            'clockwork.storage_sql_table' => 'string',
            'clockwork.filter' => 'array',
            'clockwork.filter_uris' => 'array',
            'clockwork.additional_data_sources' => 'array',
            'clockwork.headers' => 'array',
            'compile.files' => 'array',
            'compile.providers' => 'array',
            'cors.paths' => 'array',
            'cors.allowed_methods' => 'array',
            'cors.allowed_origins' => 'array',
            'cors.allowed_origins_patterns' => 'array',
            'cors.allowed_headers' => 'array',
            'cors.exposed_headers' => 'array',
            'cors.max_age' => 'integer',
            'cors.supports_credentials' => 'boolean',
            'database.default' => 'string',
            'database.connections.sqlite.driver' => 'string',
            'database.connections.sqlite.url' => 'NULL',
            'database.connections.sqlite.database' => 'string',
            'database.connections.sqlite.prefix' => 'string',
            'database.connections.sqlite.foreign_key_constraints' => 'boolean',
            'database.connections.sqlite.busy_timeout' => 'NULL',
            'database.connections.sqlite.journal_mode' => 'NULL',
            'database.connections.sqlite.synchronous' => 'NULL',
            'database.connections.mysql.driver' => 'string',
            'database.connections.mysql.url' => 'NULL',
            'database.connections.mysql.host' => 'string',
            'database.connections.mysql.port' => 'string',
            'database.connections.mysql.database' => 'string',
            'database.connections.mysql.username' => 'string',
            'database.connections.mysql.password' => 'string',
            'database.connections.mysql.unix_socket' => 'string',
            'database.connections.mysql.charset' => 'string',
            'database.connections.mysql.collation' => 'string',
            'database.connections.mysql.prefix' => 'string',
            'database.connections.mysql.prefix_indexes' => 'boolean',
            'database.connections.mysql.strict' => 'boolean',
            'database.connections.mysql.engine' => 'NULL',
            'database.connections.mysql.options' => 'array',
            'database.connections.mariadb.driver' => 'string',
            'database.connections.mariadb.url' => 'NULL',
            'database.connections.mariadb.host' => 'string',
            'database.connections.mariadb.port' => 'string',
            'database.connections.mariadb.database' => 'string',
            'database.connections.mariadb.username' => 'string',
            'database.connections.mariadb.password' => 'string',
            'database.connections.mariadb.unix_socket' => 'string',
            'database.connections.mariadb.charset' => 'string',
            'database.connections.mariadb.collation' => 'string',
            'database.connections.mariadb.prefix' => 'string',
            'database.connections.mariadb.prefix_indexes' => 'boolean',
            'database.connections.mariadb.strict' => 'boolean',
            'database.connections.mariadb.engine' => 'NULL',
            'database.connections.mariadb.options' => 'array',
            'database.connections.pgsql.driver' => 'string',
            'database.connections.pgsql.url' => 'NULL',
            'database.connections.pgsql.host' => 'string',
            'database.connections.pgsql.port' => 'string',
            'database.connections.pgsql.database' => 'string',
            'database.connections.pgsql.username' => 'string',
            'database.connections.pgsql.password' => 'string',
            'database.connections.pgsql.charset' => 'string',
            'database.connections.pgsql.prefix' => 'string',
            'database.connections.pgsql.prefix_indexes' => 'boolean',
            'database.connections.pgsql.search_path' => 'string',
            'database.connections.pgsql.sslmode' => 'string',
            'database.connections.sqlsrv.driver' => 'string',
            'database.connections.sqlsrv.url' => 'NULL',
            'database.connections.sqlsrv.host' => 'string',
            'database.connections.sqlsrv.port' => 'string',
            'database.connections.sqlsrv.database' => 'string',
            'database.connections.sqlsrv.username' => 'string',
            'database.connections.sqlsrv.password' => 'string',
            'database.connections.sqlsrv.charset' => 'string',
            'database.connections.sqlsrv.prefix' => 'string',
            'database.connections.sqlsrv.prefix_indexes' => 'boolean',
            'database.connections.failed_jobs.driver' => 'string',
            'database.connections.failed_jobs.host' => 'string',
            'database.connections.failed_jobs.port' => 'string',
            'database.connections.failed_jobs.database' => 'string',
            'database.connections.failed_jobs.username' => 'string',
            'database.connections.failed_jobs.password' => 'string',
            'database.connections.failed_jobs.charset' => 'string',
            'database.connections.failed_jobs.collation' => 'string',
            'database.connections.failed_jobs.prefix' => 'string',
            'database.connections.failed_jobs.strict' => 'boolean',
            'database.migrations.table' => 'string',
            'database.migrations.update_date_on_publish' => 'boolean',
            'database.redis.client' => 'string',
            'database.redis.options.cluster' => 'string',
            'database.redis.options.prefix' => 'string',
            'database.redis.default.url' => 'NULL',
            'database.redis.default.host' => 'string',
            'database.redis.default.username' => 'NULL',
            'database.redis.default.password' => 'NULL',
            'database.redis.default.port' => 'string',
            'database.redis.default.database' => 'string',
            'database.redis.default.read_write_timeout' => 'integer',
            'database.redis.cache.url' => 'NULL',
            'database.redis.cache.host' => 'string',
            'database.redis.cache.username' => 'NULL',
            'database.redis.cache.password' => 'NULL',
            'database.redis.cache.port' => 'string',
            'database.redis.cache.database' => 'string',
            'database.redis.cache.read_write_timeout' => 'integer',
            'database.redis.horizon.url' => 'NULL',
            'database.redis.horizon.host' => 'string',
            'database.redis.horizon.username' => 'NULL',
            'database.redis.horizon.password' => 'NULL',
            'database.redis.horizon.port' => 'string',
            'database.redis.horizon.database' => 'string',
            'database.redis.horizon.read_write_timeout' => 'integer',
            'database.redis.horizon.options.prefix' => 'string',
            'disposable-email.sources' => 'array',
            'disposable-email.fetcher' => 'string',
            'disposable-email.storage' => 'string',
            'disposable-email.whitelist' => 'array',
            'disposable-email.include_subdomains' => 'boolean',
            'disposable-email.cache.enabled' => 'boolean',
            'disposable-email.cache.store' => 'string',
            'disposable-email.cache.key' => 'string',
            'eloquentfilter.namespace' => 'string',
            'eloquentfilter.paginate_limit' => 'integer',
            'excel.exports.chunk_size' => 'integer',
            'excel.exports.pre_calculate_formulas' => 'boolean',
            'excel.exports.strict_null_comparison' => 'boolean',
            'excel.exports.csv.delimiter' => 'string',
            'excel.exports.csv.enclosure' => 'string',
            'excel.exports.csv.line_ending' => 'string',
            'excel.exports.csv.use_bom' => 'boolean',
            'excel.exports.csv.include_separator_line' => 'boolean',
            'excel.exports.csv.excel_compatibility' => 'boolean',
            'excel.exports.csv.output_encoding' => 'string',
            'excel.exports.properties.creator' => 'string',
            'excel.exports.properties.lastModifiedBy' => 'string',
            'excel.exports.properties.title' => 'string',
            'excel.exports.properties.description' => 'string',
            'excel.exports.properties.subject' => 'string',
            'excel.exports.properties.keywords' => 'string',
            'excel.exports.properties.category' => 'string',
            'excel.exports.properties.manager' => 'string',
            'excel.exports.properties.company' => 'string',
            'excel.imports.read_only' => 'boolean',
            'excel.imports.ignore_empty' => 'boolean',
            'excel.imports.heading_row.formatter' => 'string',
            'excel.imports.csv.delimiter' => 'NULL',
            'excel.imports.csv.enclosure' => 'string',
            'excel.imports.csv.escape_character' => 'string',
            'excel.imports.csv.contiguous' => 'boolean',
            'excel.imports.csv.input_encoding' => 'string',
            'excel.imports.properties.creator' => 'string',
            'excel.imports.properties.lastModifiedBy' => 'string',
            'excel.imports.properties.title' => 'string',
            'excel.imports.properties.description' => 'string',
            'excel.imports.properties.subject' => 'string',
            'excel.imports.properties.keywords' => 'string',
            'excel.imports.properties.category' => 'string',
            'excel.imports.properties.manager' => 'string',
            'excel.imports.properties.company' => 'string',
            'excel.extension_detector.xlsx' => 'string',
            'excel.extension_detector.xlsm' => 'string',
            'excel.extension_detector.xltx' => 'string',
            'excel.extension_detector.xltm' => 'string',
            'excel.extension_detector.xls' => 'string',
            'excel.extension_detector.xlt' => 'string',
            'excel.extension_detector.ods' => 'string',
            'excel.extension_detector.ots' => 'string',
            'excel.extension_detector.slk' => 'string',
            'excel.extension_detector.xml' => 'string',
            'excel.extension_detector.gnumeric' => 'string',
            'excel.extension_detector.htm' => 'string',
            'excel.extension_detector.html' => 'string',
            'excel.extension_detector.csv' => 'string',
            'excel.extension_detector.tsv' => 'string',
            'excel.extension_detector.pdf' => 'string',
            'excel.value_binder.default' => 'string',
            'excel.cache.driver' => 'string',
            'excel.cache.batch.memory_limit' => 'integer',
            'excel.cache.illuminate.store' => 'NULL',
            'excel.transactions.handler' => 'string',
            'excel.transactions.db.connection' => 'NULL',
            'excel.temporary_files.local_path' => 'string',
            'excel.temporary_files.remote_disk' => 'NULL',
            'excel.temporary_files.remote_prefix' => 'NULL',
            'excel.temporary_files.force_resync_remote' => 'NULL',
            'filesystems.default' => 'string',
            'filesystems.disks.local.driver' => 'string',
            'filesystems.disks.local.root' => 'string',
            'filesystems.disks.local.serve' => 'boolean',
            'filesystems.disks.local.throw' => 'boolean',
            'filesystems.disks.local.report' => 'boolean',
            'filesystems.disks.public.driver' => 'string',
            'filesystems.disks.public.root' => 'string',
            'filesystems.disks.public.url' => 'string',
            'filesystems.disks.public.visibility' => 'string',
            'filesystems.disks.public.throw' => 'boolean',
            'filesystems.disks.public.report' => 'boolean',
            'filesystems.disks.s3.driver' => 'string',
            'filesystems.disks.s3.key' => 'string',
            'filesystems.disks.s3.secret' => 'string',
            'filesystems.disks.s3.region' => 'string',
            'filesystems.disks.s3.bucket' => 'string',
            'filesystems.disks.s3.options.CacheControl' => 'string',
            'filesystems.disks.s3.url' => 'NULL',
            'filesystems.disks.s3.endpoint' => 'NULL',
            'filesystems.disks.s3.use_path_style_endpoint' => 'boolean',
            'filesystems.disks.s3.throw' => 'boolean',
            'filesystems.disks.tenants.driver' => 'string',
            'filesystems.disks.tenants.root' => 'string',
            'filesystems.disks.client.driver' => 'string',
            'filesystems.disks.client.root' => 'string',
            'filesystems.disks.pages.driver' => 'string',
            'filesystems.disks.pages.root' => 'NULL',
            'filesystems.disks.seeds.driver' => 'string',
            'filesystems.disks.seeds.root' => 'string',
            'filesystems.disks.stubs.driver' => 'string',
            'filesystems.disks.stubs.root' => 'string',
            'filesystems.links./Users/<USER>/Documents/GitHub/sevensonsfarms/public/storage' => 'string',
            'filesystems.file_upload_prefix' => 'string',
            'filesystems.cloud' => 'string',
            'grazecart.sale_keywords' => 'array',
            'grazecart.default_map_center' => 'array',
            'grazecart.countries.USA' => 'string',
            'grazecart.countries.Canada' => 'string',
            'grazecart.states.AL' => 'string',
            'grazecart.states.AK' => 'string',
            'grazecart.states.AZ' => 'string',
            'grazecart.states.AR' => 'string',
            'grazecart.states.CA' => 'string',
            'grazecart.states.CO' => 'string',
            'grazecart.states.CT' => 'string',
            'grazecart.states.DE' => 'string',
            'grazecart.states.DC' => 'string',
            'grazecart.states.FL' => 'string',
            'grazecart.states.GA' => 'string',
            'grazecart.states.HI' => 'string',
            'grazecart.states.ID' => 'string',
            'grazecart.states.IL' => 'string',
            'grazecart.states.IN' => 'string',
            'grazecart.states.IA' => 'string',
            'grazecart.states.KS' => 'string',
            'grazecart.states.KY' => 'string',
            'grazecart.states.LA' => 'string',
            'grazecart.states.ME' => 'string',
            'grazecart.states.MD' => 'string',
            'grazecart.states.MA' => 'string',
            'grazecart.states.MI' => 'string',
            'grazecart.states.MN' => 'string',
            'grazecart.states.MS' => 'string',
            'grazecart.states.MO' => 'string',
            'grazecart.states.MT' => 'string',
            'grazecart.states.NE' => 'string',
            'grazecart.states.NV' => 'string',
            'grazecart.states.NH' => 'string',
            'grazecart.states.NJ' => 'string',
            'grazecart.states.NM' => 'string',
            'grazecart.states.NY' => 'string',
            'grazecart.states.NC' => 'string',
            'grazecart.states.ND' => 'string',
            'grazecart.states.OH' => 'string',
            'grazecart.states.OK' => 'string',
            'grazecart.states.OR' => 'string',
            'grazecart.states.PA' => 'string',
            'grazecart.states.RI' => 'string',
            'grazecart.states.SC' => 'string',
            'grazecart.states.SD' => 'string',
            'grazecart.states.TN' => 'string',
            'grazecart.states.TX' => 'string',
            'grazecart.states.UT' => 'string',
            'grazecart.states.VT' => 'string',
            'grazecart.states.VA' => 'string',
            'grazecart.states.WA' => 'string',
            'grazecart.states.WV' => 'string',
            'grazecart.states.WI' => 'string',
            'grazecart.states.WY' => 'string',
            'grazecart.states.PR' => 'string',
            'grazecart.provinces.NL' => 'string',
            'grazecart.provinces.PE' => 'string',
            'grazecart.provinces.NS' => 'string',
            'grazecart.provinces.NB' => 'string',
            'grazecart.provinces.QB' => 'string',
            'grazecart.provinces.ON' => 'string',
            'grazecart.provinces.MB' => 'string',
            'grazecart.provinces.SK' => 'string',
            'grazecart.provinces.AB' => 'string',
            'grazecart.provinces.BC' => 'string',
            'grazecart.provinces.YT' => 'string',
            'grazecart.provinces.NT' => 'string',
            'grazecart.provinces.NU' => 'string',
            'grazecart.referrals.none-selected' => 'string',
            'grazecart.referrals.Stockman' => 'string',
            'grazecart.referrals.Dirt to Soil' => 'string',
            'grazecart.referrals.3 Cow Marketing' => 'string',
            'grazecart.referrals.Graze Magazine' => 'string',
            'grazecart.referrals.IndependentProcessor' => 'string',
            'grazecart.referrals.Friend' => 'string',
            'grazecart.referrals.Online Search' => 'string',
            'grazecart.referrals.Other' => 'string',
            'grazecart.yearly_revenue_questions.none-selected' => 'string',
            'grazecart.yearly_revenue_questions.not selling online' => 'string',
            'grazecart.yearly_revenue_questions.0 to 50k' => 'string',
            'grazecart.yearly_revenue_questions.50k to 250k' => 'string',
            'grazecart.yearly_revenue_questions.250k to 1m' => 'string',
            'grazecart.yearly_revenue_questions.1m+' => 'string',
            'grazecart.new_storefront_enabled' => 'boolean',
            'grazecart.welcome_coupon_code' => 'string',
            'horizon.domain' => 'NULL',
            'horizon.path' => 'string',
            'horizon.use' => 'string',
            'horizon.prefix' => 'string',
            'horizon.middleware' => 'array',
            'horizon.waits.redis:default' => 'integer',
            'horizon.waits.redis:low' => 'integer',
            'horizon.trim.recent' => 'integer',
            'horizon.trim.pending' => 'integer',
            'horizon.trim.completed' => 'integer',
            'horizon.trim.recent_failed' => 'integer',
            'horizon.trim.failed' => 'integer',
            'horizon.trim.monitored' => 'integer',
            'horizon.silenced' => 'array',
            'horizon.metrics.trim_snapshots.job' => 'integer',
            'horizon.metrics.trim_snapshots.queue' => 'integer',
            'horizon.fast_termination' => 'boolean',
            'horizon.memory_limit' => 'integer',
            'horizon.defaults.supervisor-1.connection' => 'string',
            'horizon.defaults.supervisor-1.queue' => 'array',
            'horizon.defaults.supervisor-1.balance' => 'string',
            'horizon.defaults.supervisor-1.autoScalingStrategy' => 'string',
            'horizon.defaults.supervisor-1.minProcesses' => 'integer',
            'horizon.defaults.supervisor-1.maxProcesses' => 'integer',
            'horizon.defaults.supervisor-1.maxTime' => 'integer',
            'horizon.defaults.supervisor-1.maxJobs' => 'integer',
            'horizon.defaults.supervisor-1.memory' => 'integer',
            'horizon.defaults.supervisor-1.tries' => 'integer',
            'horizon.defaults.supervisor-1.timeout' => 'integer',
            'horizon.defaults.supervisor-1.nice' => 'integer',
            'horizon.environments.production.supervisor-1.maxProcesses' => 'integer',
            'horizon.environments.production.supervisor-1.balanceMaxShift' => 'integer',
            'horizon.environments.production.supervisor-1.balanceCooldown' => 'integer',
            'horizon.environments.production.supervisor-1.tries' => 'integer',
            'horizon.environments.staging.supervisor-1.maxProcesses' => 'integer',
            'horizon.environments.staging.supervisor-1.balanceMaxShift' => 'integer',
            'horizon.environments.staging.supervisor-1.balanceCooldown' => 'integer',
            'horizon.environments.local.supervisor-1.maxProcesses' => 'integer',
            'horizon.environments.local.supervisor-1.balanceMaxShift' => 'integer',
            'horizon.environments.local.supervisor-1.balanceCooldown' => 'integer',
            'horizon.environments.test.supervisor-1.maxProcesses' => 'integer',
            'horizon.environments.test.supervisor-1.balanceMaxShift' => 'integer',
            'horizon.environments.test.supervisor-1.balanceCooldown' => 'integer',
            'ide-helper.filename' => 'string',
            'ide-helper.models_filename' => 'string',
            'ide-helper.meta_filename' => 'string',
            'ide-helper.include_fluent' => 'boolean',
            'ide-helper.include_factory_builders' => 'boolean',
            'ide-helper.write_model_magic_where' => 'boolean',
            'ide-helper.write_model_external_builder_methods' => 'boolean',
            'ide-helper.write_model_relation_count_properties' => 'boolean',
            'ide-helper.write_eloquent_model_mixins' => 'boolean',
            'ide-helper.include_helpers' => 'boolean',
            'ide-helper.helper_files' => 'array',
            'ide-helper.model_locations' => 'array',
            'ide-helper.ignored_models' => 'array',
            'ide-helper.model_hooks' => 'array',
            'ide-helper.extra.Eloquent' => 'array',
            'ide-helper.extra.Session' => 'array',
            'ide-helper.magic' => 'array',
            'ide-helper.interfaces' => 'array',
            'ide-helper.model_camel_case_properties' => 'boolean',
            'ide-helper.type_overrides.integer' => 'string',
            'ide-helper.type_overrides.boolean' => 'string',
            'ide-helper.include_class_docblocks' => 'boolean',
            'ide-helper.force_fqn' => 'boolean',
            'ide-helper.use_generics_annotations' => 'boolean',
            'ide-helper.additional_relation_types' => 'array',
            'ide-helper.additional_relation_return_types' => 'array',
            'ide-helper.enforce_nullable_relationships' => 'boolean',
            'ide-helper.post_migrate' => 'array',
            'ide-helper.macroable_traits' => 'array',
            'ide-helper.custom_db_types' => 'array',
            'image.driver' => 'string',
            'inertia.ssr.enabled' => 'boolean',
            'inertia.ssr.url' => 'string',
            'inertia.testing.ensure_pages_exist' => 'boolean',
            'inertia.testing.page_paths' => 'array',
            'inertia.testing.page_extensions' => 'array',
            'inertia.history.encrypt' => 'boolean',
            'integrations.drip.id' => 'string',
            'integrations.drip.enabled' => 'boolean',
            'integrations.drip.api_key' => 'string',
            'integrations.drip.account_id' => 'string',
            'integrations.drip.default_subscriber_tags' => 'array',
            'integrations.drip.title' => 'string',
            'integrations.drip.logo' => 'string',
            'integrations.drip.svg' => 'NULL',
            'integrations.drip.setup_class' => 'string',
            'integrations.drip.show_slug' => 'string',
            'integrations.drip.edit_slug' => 'string',
            'integrations.drip.app_slug' => 'NULL',
            'integrations.drip.vendor' => 'string',
            'integrations.drip.short_description' => 'string',
            'integrations.drip.long_description' => 'string',
            'integrations.drip.categories' => 'array',
            'integrations.drip.highlights' => 'array',
            'integrations.drip.image' => 'string',
            'integrations.drip.screenshots' => 'array',
            'integrations.drip.support_url' => 'string',
            'integrations.drip.price_description' => 'string',
            'integrations.drip.product_id' => 'NULL',
            'integrations.pickup-manager.id' => 'string',
            'integrations.pickup-manager.enabled' => 'boolean',
            'integrations.pickup-manager.title' => 'string',
            'integrations.pickup-manager.logo' => 'NULL',
            'integrations.pickup-manager.svg' => 'string',
            'integrations.pickup-manager.setup_class' => 'NULL',
            'integrations.pickup-manager.show_slug' => 'string',
            'integrations.pickup-manager.edit_slug' => 'string',
            'integrations.pickup-manager.app_slug' => 'string',
            'integrations.pickup-manager.vendor' => 'string',
            'integrations.pickup-manager.short_description' => 'string',
            'integrations.pickup-manager.long_description' => 'string',
            'integrations.pickup-manager.categories' => 'array',
            'integrations.pickup-manager.highlights' => 'array',
            'integrations.pickup-manager.image' => 'string',
            'integrations.pickup-manager.screenshots' => 'array',
            'integrations.pickup-manager.support_url' => 'string',
            'integrations.pickup-manager.price_description' => 'string',
            'livewire.class_namespace' => 'string',
            'livewire.view_path' => 'string',
            'livewire.layout' => 'string',
            'livewire.lazy_placeholder' => 'NULL',
            'livewire.temporary_file_upload.disk' => 'NULL',
            'livewire.temporary_file_upload.rules' => 'NULL',
            'livewire.temporary_file_upload.directory' => 'NULL',
            'livewire.temporary_file_upload.middleware' => 'NULL',
            'livewire.temporary_file_upload.preview_mimes' => 'array',
            'livewire.temporary_file_upload.max_upload_time' => 'integer',
            'livewire.temporary_file_upload.cleanup' => 'boolean',
            'livewire.render_on_redirect' => 'boolean',
            'livewire.legacy_model_binding' => 'boolean',
            'livewire.inject_assets' => 'boolean',
            'livewire.navigate.show_progress_bar' => 'boolean',
            'livewire.navigate.progress_bar_color' => 'string',
            'livewire.inject_morph_markers' => 'boolean',
            'livewire.pagination_theme' => 'string',
            'logging.default' => 'string',
            'logging.deprecations.channel' => 'NULL',
            'logging.deprecations.trace' => 'boolean',
            'logging.channels.stack.driver' => 'string',
            'logging.channels.stack.channels' => 'array',
            'logging.channels.stack.ignore_exceptions' => 'boolean',
            'logging.channels.single.driver' => 'string',
            'logging.channels.single.path' => 'string',
            'logging.channels.single.level' => 'string',
            'logging.channels.single.replace_placeholders' => 'boolean',
            'logging.channels.daily.driver' => 'string',
            'logging.channels.daily.path' => 'string',
            'logging.channels.daily.level' => 'string',
            'logging.channels.daily.days' => 'string',
            'logging.channels.daily.replace_placeholders' => 'boolean',
            'logging.channels.slack.driver' => 'string',
            'logging.channels.slack.url' => 'string',
            'logging.channels.slack.username' => 'string',
            'logging.channels.slack.emoji' => 'string',
            'logging.channels.slack.level' => 'string',
            'logging.channels.slack.replace_placeholders' => 'boolean',
            'logging.channels.papertrail.driver' => 'string',
            'logging.channels.papertrail.level' => 'string',
            'logging.channels.papertrail.handler' => 'string',
            'logging.channels.papertrail.handler_with.host' => 'NULL',
            'logging.channels.papertrail.handler_with.port' => 'NULL',
            'logging.channels.papertrail.handler_with.connectionString' => 'string',
            'logging.channels.papertrail.processors' => 'array',
            'logging.channels.stderr.driver' => 'string',
            'logging.channels.stderr.level' => 'string',
            'logging.channels.stderr.handler' => 'string',
            'logging.channels.stderr.handler_with.stream' => 'string',
            'logging.channels.stderr.formatter' => 'NULL',
            'logging.channels.stderr.processors' => 'array',
            'logging.channels.syslog.driver' => 'string',
            'logging.channels.syslog.level' => 'string',
            'logging.channels.syslog.facility' => 'integer',
            'logging.channels.syslog.replace_placeholders' => 'boolean',
            'logging.channels.errorlog.driver' => 'string',
            'logging.channels.errorlog.level' => 'string',
            'logging.channels.errorlog.replace_placeholders' => 'boolean',
            'logging.channels.null.driver' => 'string',
            'logging.channels.null.handler' => 'string',
            'logging.channels.emergency.path' => 'string',
            'logging.channels.bugsnag.driver' => 'string',
            'logging.channels.deprecations.driver' => 'string',
            'logging.channels.deprecations.handler' => 'string',
            'mail.default' => 'string',
            'mail.mailers.smtp.transport' => 'string',
            'mail.mailers.smtp.url' => 'NULL',
            'mail.mailers.smtp.host' => 'string',
            'mail.mailers.smtp.port' => 'string',
            'mail.mailers.smtp.encryption' => 'string',
            'mail.mailers.smtp.username' => 'string',
            'mail.mailers.smtp.password' => 'string',
            'mail.mailers.smtp.timeout' => 'NULL',
            'mail.mailers.smtp.local_domain' => 'string',
            'mail.mailers.ses.transport' => 'string',
            'mail.mailers.postmark.transport' => 'string',
            'mail.mailers.resend.transport' => 'string',
            'mail.mailers.sendmail.transport' => 'string',
            'mail.mailers.sendmail.path' => 'string',
            'mail.mailers.log.transport' => 'string',
            'mail.mailers.log.channel' => 'NULL',
            'mail.mailers.array.transport' => 'string',
            'mail.mailers.failover.transport' => 'string',
            'mail.mailers.failover.mailers' => 'array',
            'mail.mailers.roundrobin.transport' => 'string',
            'mail.mailers.roundrobin.mailers' => 'array',
            'mail.mailers.mailgun.transport' => 'string',
            'mail.from.address' => 'string',
            'mail.from.name' => 'string',
            'mail.markdown.theme' => 'string',
            'mail.markdown.paths' => 'array',
            'mail.to' => 'array',
            'mail.no_reply.address' => 'string',
            'mail.marketing_from.address' => 'string',
            'mail.marketing_from.name' => 'string',
            'mail.notifications.new_registration' => 'array',
            'mail.notifications.subscription_demand_report' => 'array',
            'mail.notifications.order_credit_report' => 'array',
            'queue.default' => 'string',
            'queue.connections.sync.driver' => 'string',
            'queue.connections.database.driver' => 'string',
            'queue.connections.database.connection' => 'NULL',
            'queue.connections.database.table' => 'string',
            'queue.connections.database.queue' => 'string',
            'queue.connections.database.retry_after' => 'integer',
            'queue.connections.database.after_commit' => 'boolean',
            'queue.connections.beanstalkd.driver' => 'string',
            'queue.connections.beanstalkd.host' => 'string',
            'queue.connections.beanstalkd.queue' => 'string',
            'queue.connections.beanstalkd.retry_after' => 'integer',
            'queue.connections.beanstalkd.block_for' => 'integer',
            'queue.connections.beanstalkd.after_commit' => 'boolean',
            'queue.connections.sqs.driver' => 'string',
            'queue.connections.sqs.key' => 'NULL',
            'queue.connections.sqs.secret' => 'NULL',
            'queue.connections.sqs.prefix' => 'string',
            'queue.connections.sqs.queue' => 'string',
            'queue.connections.sqs.suffix' => 'NULL',
            'queue.connections.sqs.region' => 'string',
            'queue.connections.sqs.after_commit' => 'boolean',
            'queue.connections.redis.driver' => 'string',
            'queue.connections.redis.connection' => 'string',
            'queue.connections.redis.queue' => 'string',
            'queue.connections.redis.retry_after' => 'integer',
            'queue.connections.redis.block_for' => 'NULL',
            'queue.connections.redis.after_commit' => 'boolean',
            'queue.connections.central.driver' => 'string',
            'queue.connections.central.table' => 'string',
            'queue.connections.central.queue' => 'string',
            'queue.connections.central.retry_after' => 'integer',
            'queue.connections.central.after_commit' => 'boolean',
            'queue.connections.central.central' => 'boolean',
            'queue.batching.database' => 'string',
            'queue.batching.table' => 'string',
            'queue.failed.driver' => 'string',
            'queue.failed.database' => 'string',
            'queue.failed.table' => 'string',
            'sanctum.stateful' => 'array',
            'sanctum.guard' => 'array',
            'sanctum.expiration' => 'NULL',
            'sanctum.token_prefix' => 'string',
            'sanctum.middleware.authenticate_session' => 'string',
            'sanctum.middleware.encrypt_cookies' => 'string',
            'sanctum.middleware.validate_csrf_token' => 'string',
            'scout.driver' => 'string',
            'scout.prefix' => 'string',
            'scout.queue' => 'boolean',
            'scout.after_commit' => 'boolean',
            'scout.chunk.searchable' => 'integer',
            'scout.chunk.unsearchable' => 'integer',
            'scout.soft_delete' => 'boolean',
            'scout.identify' => 'boolean',
            'scout.algolia.id' => 'string',
            'scout.algolia.secret' => 'string',
            'scout.meilisearch.host' => 'string',
            'scout.meilisearch.key' => 'string',
            'scout.meilisearch.index-settings.App\Models\Product.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Product.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.enabled' => 'boolean',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.minWordSizeForTypos.oneTypo' => 'integer',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.minWordSizeForTypos.twoTypos' => 'integer',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.disableOnWords' => 'array',
            'scout.meilisearch.index-settings.App\Models\Product.typoTolerance.disableOnAttributes' => 'array',
            'scout.typesense.client-settings.api_key' => 'string',
            'scout.typesense.client-settings.nodes.0.host' => 'string',
            'scout.typesense.client-settings.nodes.0.port' => 'string',
            'scout.typesense.client-settings.nodes.0.path' => 'string',
            'scout.typesense.client-settings.nodes.0.protocol' => 'string',
            'scout.typesense.client-settings.nearest_node.host' => 'string',
            'scout.typesense.client-settings.nearest_node.port' => 'string',
            'scout.typesense.client-settings.nearest_node.path' => 'string',
            'scout.typesense.client-settings.nearest_node.protocol' => 'string',
            'scout.typesense.client-settings.connection_timeout_seconds' => 'integer',
            'scout.typesense.client-settings.healthcheck_interval_seconds' => 'integer',
            'scout.typesense.client-settings.num_retries' => 'integer',
            'scout.typesense.client-settings.retry_interval_seconds' => 'integer',
            'scout.typesense.model-settings' => 'array',
            'services.postmark.token' => 'NULL',
            'services.ses.key' => 'NULL',
            'services.ses.secret' => 'NULL',
            'services.ses.region' => 'string',
            'services.resend.key' => 'NULL',
            'services.slack.hook' => 'string',
            'services.mailgun.central_domain' => 'string',
            'services.mailgun.domain' => 'string',
            'services.mailgun.marketing_domain' => 'string',
            'services.mailgun.secret' => 'string',
            'services.mailgun.endpoint' => 'string',
            'services.mailgun.scheme' => 'string',
            'services.stripe.key' => 'string',
            'services.stripe.secret' => 'string',
            'services.stripe.account' => 'string',
            'services.stripe.managed' => 'boolean',
            'services.stripe.webhook.secret' => 'string',
            'services.stripe.webhook.tolerance' => 'integer',
            'services.stripe.debug' => 'boolean',
            'services.geocodio.key' => 'string',
            'services.geocoderCa.key' => 'string',
            'services.drip.key' => 'string',
            'services.drip.account_id' => 'string',
            'services.facebook.pixel_id' => 'NULL',
            'services.facebook.access_token' => 'string',
            'services.facebook.test_event_code' => 'string',
            'services.google.geocoder_api_key' => 'string',
            'services.google.places_js_api_key' => 'string',
            'services.google.sheets_api_key' => 'string',
            'services.google.spreadsheet_id' => 'string',
            'services.google.worksheet_name' => 'string',
            'services.google.place_id' => 'string',
            'services.google.analytics_id' => 'NULL',
            'services.google.ads_id' => 'NULL',
            'services.twilio.default' => 'string',
            'services.twilio.connections.twilio.sid' => 'string',
            'services.twilio.connections.twilio.token' => 'string',
            'services.twilio.connections.twilio.from' => 'string',
            'services.horizon.secret' => 'string',
            'services.segment.write_key' => 'string',
            'services.stax.partner_api_key' => 'string',
            'services.pay_fac.api_key' => 'string',
            'services.pay_fac.company_id' => 'string',
            'services.pay_fac.js_client_key' => 'string',
            'services.pay_fac.stripe_key' => 'NULL',
            'services.launch_darkly.sdk_key' => 'NULL',
            'services.tolstoy.app_key' => 'string',
            'services.cloudfront.url' => 'string',
            'sluggable.source' => 'NULL',
            'sluggable.maxLength' => 'NULL',
            'sluggable.maxLengthKeepWords' => 'boolean',
            'sluggable.method' => 'NULL',
            'sluggable.separator' => 'string',
            'sluggable.unique' => 'boolean',
            'sluggable.uniqueSuffix' => 'NULL',
            'sluggable.firstUniqueSuffix' => 'integer',
            'sluggable.includeTrashed' => 'boolean',
            'sluggable.reserved' => 'NULL',
            'sluggable.onUpdate' => 'boolean',
            'sluggable.slugEngineOptions' => 'array',
            'telescope.enabled' => 'boolean',
            'telescope.domain' => 'NULL',
            'telescope.path' => 'string',
            'telescope.driver' => 'string',
            'telescope.storage.database.connection' => 'string',
            'telescope.storage.database.chunk' => 'integer',
            'telescope.queue.connection' => 'NULL',
            'telescope.queue.queue' => 'NULL',
            'telescope.queue.delay' => 'integer',
            'telescope.middleware' => 'array',
            'telescope.only_paths' => 'array',
            'telescope.ignore_paths' => 'array',
            'telescope.ignore_commands' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\CacheWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CommandWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CommandWatcher.ignore' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\DumpWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\EventWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ExceptionWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\JobWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\LogWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\MailWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.events' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\NotificationWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.ignore_packages' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.slow' => 'integer',
            'telescope.watchers.Laravel\Telescope\Watchers\RedisWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.size_limit' => 'integer',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_abilities' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_packages' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ScheduleWatcher' => 'boolean',
            'theme.id' => 'string',
            'theme.title' => 'string',
            'theme.description' => 'string',
            'theme.view_path' => 'string',
            'theme.settings' => 'NULL',
            'theme.css' => 'NULL',
            'theme.css_preview' => 'NULL',
            'theme.custom_css' => 'NULL',
            'theme.theme_builder' => 'string',
            'theme.google_web_fonts' => 'array',
            'tinker.commands' => 'array',
            'tinker.alias' => 'array',
            'tinker.dont_alias' => 'array',
            'twilio-notification-channel.enabled' => 'boolean',
            'twilio-notification-channel.username' => 'NULL',
            'twilio-notification-channel.password' => 'NULL',
            'twilio-notification-channel.auth_token' => 'string',
            'twilio-notification-channel.account_sid' => 'string',
            'twilio-notification-channel.from' => 'string',
            'twilio-notification-channel.alphanumeric_sender' => 'NULL',
            'twilio-notification-channel.shorten_urls' => 'boolean',
            'twilio-notification-channel.sms_service_sid' => 'NULL',
            'twilio-notification-channel.debug_to' => 'NULL',
            'twilio-notification-channel.ignored_error_codes' => 'array',
            'twilio-notification-channel.message_size_limit' => 'integer',
            'hashing.driver' => 'string',
            'hashing.bcrypt.rounds' => 'string',
            'hashing.bcrypt.verify' => 'boolean',
            'hashing.bcrypt.limit' => 'NULL',
            'hashing.argon.memory' => 'integer',
            'hashing.argon.threads' => 'integer',
            'hashing.argon.time' => 'integer',
            'hashing.argon.verify' => 'boolean',
            'hashing.rehash_on_login' => 'boolean',
            'session.driver' => 'string',
            'session.lifetime' => 'integer',
            'session.expire_on_close' => 'boolean',
            'session.encrypt' => 'boolean',
            'session.files' => 'string',
            'session.connection' => 'string',
            'session.table' => 'string',
            'session.store' => 'NULL',
            'session.lottery' => 'array',
            'session.cookie' => 'string',
            'session.path' => 'string',
            'session.domain' => 'NULL',
            'session.secure' => 'NULL',
            'session.http_only' => 'boolean',
            'session.same_site' => 'string',
            'session.partitioned' => 'boolean',
            'concurrency.default' => 'string',
            'broadcasting.default' => 'string',
            'broadcasting.connections.reverb.driver' => 'string',
            'broadcasting.connections.reverb.key' => 'NULL',
            'broadcasting.connections.reverb.secret' => 'NULL',
            'broadcasting.connections.reverb.app_id' => 'NULL',
            'broadcasting.connections.reverb.options.host' => 'NULL',
            'broadcasting.connections.reverb.options.port' => 'integer',
            'broadcasting.connections.reverb.options.scheme' => 'string',
            'broadcasting.connections.reverb.options.useTLS' => 'boolean',
            'broadcasting.connections.reverb.client_options' => 'array',
            'broadcasting.connections.pusher.driver' => 'string',
            'broadcasting.connections.pusher.key' => 'NULL',
            'broadcasting.connections.pusher.secret' => 'NULL',
            'broadcasting.connections.pusher.app_id' => 'NULL',
            'broadcasting.connections.pusher.options.cluster' => 'NULL',
            'broadcasting.connections.pusher.options.host' => 'string',
            'broadcasting.connections.pusher.options.port' => 'integer',
            'broadcasting.connections.pusher.options.scheme' => 'string',
            'broadcasting.connections.pusher.options.encrypted' => 'boolean',
            'broadcasting.connections.pusher.options.useTLS' => 'boolean',
            'broadcasting.connections.pusher.client_options' => 'array',
            'broadcasting.connections.ably.driver' => 'string',
            'broadcasting.connections.ably.key' => 'NULL',
            'broadcasting.connections.log.driver' => 'string',
            'broadcasting.connections.null.driver' => 'string',
            'view.paths' => 'array',
            'view.compiled' => 'string',
            'debugbar.enabled' => 'boolean',
            'debugbar.hide_empty_tabs' => 'boolean',
            'debugbar.except' => 'array',
            'debugbar.storage.enabled' => 'boolean',
            'debugbar.storage.open' => 'NULL',
            'debugbar.storage.driver' => 'string',
            'debugbar.storage.path' => 'string',
            'debugbar.storage.connection' => 'NULL',
            'debugbar.storage.provider' => 'string',
            'debugbar.storage.hostname' => 'string',
            'debugbar.storage.port' => 'integer',
            'debugbar.editor' => 'string',
            'debugbar.remote_sites_path' => 'NULL',
            'debugbar.local_sites_path' => 'NULL',
            'debugbar.include_vendors' => 'boolean',
            'debugbar.capture_ajax' => 'boolean',
            'debugbar.add_ajax_timing' => 'boolean',
            'debugbar.ajax_handler_auto_show' => 'boolean',
            'debugbar.ajax_handler_enable_tab' => 'boolean',
            'debugbar.defer_datasets' => 'boolean',
            'debugbar.error_handler' => 'boolean',
            'debugbar.clockwork' => 'boolean',
            'debugbar.collectors.phpinfo' => 'boolean',
            'debugbar.collectors.messages' => 'boolean',
            'debugbar.collectors.time' => 'boolean',
            'debugbar.collectors.memory' => 'boolean',
            'debugbar.collectors.exceptions' => 'boolean',
            'debugbar.collectors.log' => 'boolean',
            'debugbar.collectors.db' => 'boolean',
            'debugbar.collectors.views' => 'boolean',
            'debugbar.collectors.route' => 'boolean',
            'debugbar.collectors.auth' => 'boolean',
            'debugbar.collectors.gate' => 'boolean',
            'debugbar.collectors.session' => 'boolean',
            'debugbar.collectors.symfony_request' => 'boolean',
            'debugbar.collectors.mail' => 'boolean',
            'debugbar.collectors.laravel' => 'boolean',
            'debugbar.collectors.events' => 'boolean',
            'debugbar.collectors.default_request' => 'boolean',
            'debugbar.collectors.logs' => 'boolean',
            'debugbar.collectors.files' => 'boolean',
            'debugbar.collectors.config' => 'boolean',
            'debugbar.collectors.cache' => 'boolean',
            'debugbar.collectors.models' => 'boolean',
            'debugbar.collectors.livewire' => 'boolean',
            'debugbar.collectors.jobs' => 'boolean',
            'debugbar.collectors.pennant' => 'boolean',
            'debugbar.options.time.memory_usage' => 'boolean',
            'debugbar.options.messages.trace' => 'boolean',
            'debugbar.options.messages.capture_dumps' => 'boolean',
            'debugbar.options.memory.reset_peak' => 'boolean',
            'debugbar.options.memory.with_baseline' => 'boolean',
            'debugbar.options.memory.precision' => 'integer',
            'debugbar.options.auth.show_name' => 'boolean',
            'debugbar.options.auth.show_guards' => 'boolean',
            'debugbar.options.db.with_params' => 'boolean',
            'debugbar.options.db.exclude_paths' => 'array',
            'debugbar.options.db.backtrace' => 'boolean',
            'debugbar.options.db.backtrace_exclude_paths' => 'array',
            'debugbar.options.db.timeline' => 'boolean',
            'debugbar.options.db.duration_background' => 'boolean',
            'debugbar.options.db.explain.enabled' => 'boolean',
            'debugbar.options.db.hints' => 'boolean',
            'debugbar.options.db.show_copy' => 'boolean',
            'debugbar.options.db.slow_threshold' => 'boolean',
            'debugbar.options.db.memory_usage' => 'boolean',
            'debugbar.options.db.soft_limit' => 'integer',
            'debugbar.options.db.hard_limit' => 'integer',
            'debugbar.options.mail.timeline' => 'boolean',
            'debugbar.options.mail.show_body' => 'boolean',
            'debugbar.options.views.timeline' => 'boolean',
            'debugbar.options.views.data' => 'boolean',
            'debugbar.options.views.group' => 'integer',
            'debugbar.options.views.exclude_paths' => 'array',
            'debugbar.options.route.label' => 'boolean',
            'debugbar.options.session.hiddens' => 'array',
            'debugbar.options.symfony_request.label' => 'boolean',
            'debugbar.options.symfony_request.hiddens' => 'array',
            'debugbar.options.events.data' => 'boolean',
            'debugbar.options.logs.file' => 'NULL',
            'debugbar.options.cache.values' => 'boolean',
            'debugbar.inject' => 'boolean',
            'debugbar.route_prefix' => 'string',
            'debugbar.route_middleware' => 'array',
            'debugbar.route_domain' => 'NULL',
            'debugbar.theme' => 'string',
            'debugbar.debug_backtrace_limit' => 'integer',
            'cookie-consent.enabled' => 'boolean',
            'cookie-consent.cookie_name' => 'string',
            'cookie-consent.cookie_lifetime' => 'integer',
        ]));


    override(\Illuminate\Foundation\Testing\Concerns\InteractsWithContainer::mock(0), map(["" => "@&\Mockery\MockInterface"]));
    override(\Illuminate\Foundation\Testing\Concerns\InteractsWithContainer::partialMock(0), map(["" => "@&\Mockery\MockInterface"]));
    override(\Illuminate\Foundation\Testing\Concerns\InteractsWithContainer::instance(0), type(1));
    override(\Illuminate\Foundation\Testing\Concerns\InteractsWithContainer::spy(0), map(["" => "@&\Mockery\MockInterface"]));
    override(\Illuminate\Support\Arr::add(0), type(0));
    override(\Illuminate\Support\Arr::except(0), type(0));
    override(\Illuminate\Support\Arr::first(0), elementType(0));
    override(\Illuminate\Support\Arr::last(0), elementType(0));
    override(\Illuminate\Support\Arr::get(0), elementType(0));
    override(\Illuminate\Support\Arr::only(0), type(0));
    override(\Illuminate\Support\Arr::prepend(0), type(0));
    override(\Illuminate\Support\Arr::pull(0), elementType(0));
    override(\Illuminate\Support\Arr::set(0), type(0));
    override(\Illuminate\Support\Arr::shuffle(0), type(0));
    override(\Illuminate\Support\Arr::sort(0), type(0));
    override(\Illuminate\Support\Arr::sortRecursive(0), type(0));
    override(\Illuminate\Support\Arr::where(0), type(0));
    override(\array_add(0), type(0));
    override(\array_except(0), type(0));
    override(\array_first(0), elementType(0));
    override(\array_last(0), elementType(0));
    override(\array_get(0), elementType(0));
    override(\array_only(0), type(0));
    override(\array_prepend(0), type(0));
    override(\array_pull(0), elementType(0));
    override(\array_set(0), type(0));
    override(\array_sort(0), type(0));
    override(\array_sort_recursive(0), type(0));
    override(\array_where(0), type(0));
    override(\head(0), elementType(0));
    override(\last(0), elementType(0));
    override(\with(0), type(0));
    override(\tap(0), type(0));
    override(\optional(0), type(0));

            registerArgumentsSet('auth', 
'viewTelescope','admin-access','editor-access','update-billing','update-teams',
'viewHorizon',);
        registerArgumentsSet('configs', 
'app.name','app.env','app.debug','app.url','app.frontend_url',
'app.asset_url','app.timezone','app.locale','app.fallback_locale','app.faker_locale',
'app.cipher','app.key','app.previous_keys','app.maintenance.driver','app.maintenance.store',
'app.providers','app.aliases.App','app.aliases.Arr','app.aliases.Artisan','app.aliases.Auth',
'app.aliases.Blade','app.aliases.Broadcast','app.aliases.Bus','app.aliases.Cache','app.aliases.Concurrency',
'app.aliases.Config','app.aliases.Context','app.aliases.Cookie','app.aliases.Crypt','app.aliases.Date',
'app.aliases.DB','app.aliases.Eloquent','app.aliases.Event','app.aliases.File','app.aliases.Gate',
'app.aliases.Hash','app.aliases.Http','app.aliases.Js','app.aliases.Lang','app.aliases.Log',
'app.aliases.Mail','app.aliases.Notification','app.aliases.Number','app.aliases.Password','app.aliases.Process',
'app.aliases.Queue','app.aliases.RateLimiter','app.aliases.Redirect','app.aliases.Request','app.aliases.Response',
'app.aliases.Route','app.aliases.Schedule','app.aliases.Schema','app.aliases.Session','app.aliases.Storage',
'app.aliases.Str','app.aliases.URL','app.aliases.Uri','app.aliases.Validator','app.aliases.View',
'app.aliases.Vite','app.aliases.Bugsnag','app.aliases.Image','app.aliases.Inspiring','app.aliases.Redis',
'app.aliases.Socialite','app.domains.root','app.domains.admin','app.domains.api','app.deleted_user_email',
'app.inventory.spreadsheet_id','app.inventory.worksheet_name','app.homepage_featured_bundle_ids','auth.defaults.guard','auth.defaults.passwords',
'auth.guards.web.driver','auth.guards.web.provider','auth.guards.api.driver','auth.guards.api.provider','auth.guards.api.hash',
'auth.guards.sanctum.driver','auth.guards.sanctum.provider','auth.providers.users.driver','auth.providers.users.model','auth.passwords.users.provider',
'auth.passwords.users.table','auth.passwords.users.expire','auth.passwords.users.throttle','auth.password_timeout','bugsnag.api_key',
'bugsnag.app_type','bugsnag.app_version','bugsnag.batch_sending','bugsnag.endpoint','bugsnag.filters',
'bugsnag.hostname','bugsnag.proxy','bugsnag.project_root','bugsnag.project_root_regex','bugsnag.strip_path',
'bugsnag.strip_path_regex','bugsnag.query','bugsnag.bindings','bugsnag.octane_breadcrumbs','bugsnag.release_stage',
'bugsnag.notify_release_stages','bugsnag.send_code','bugsnag.callbacks','bugsnag.user','bugsnag.logger_notify_level',
'bugsnag.auto_capture_sessions','bugsnag.session_endpoint','bugsnag.build_endpoint','bugsnag.discard_classes','bugsnag.redacted_keys',
'bugsnag.feature_flags','bugsnag.max_breadcrumbs','bugsnag.attach_hidden_context','cache.default','cache.stores.array.driver',
'cache.stores.array.serialize','cache.stores.database.driver','cache.stores.database.connection','cache.stores.database.table','cache.stores.database.lock_connection',
'cache.stores.database.lock_table','cache.stores.file.driver','cache.stores.file.path','cache.stores.file.lock_path','cache.stores.memcached.driver',
'cache.stores.memcached.persistent_id','cache.stores.memcached.sasl','cache.stores.memcached.options','cache.stores.memcached.servers.0.host','cache.stores.memcached.servers.0.port',
'cache.stores.memcached.servers.0.weight','cache.stores.redis.driver','cache.stores.redis.connection','cache.stores.redis.lock_connection','cache.stores.dynamodb.driver',
'cache.stores.dynamodb.key','cache.stores.dynamodb.secret','cache.stores.dynamodb.region','cache.stores.dynamodb.table','cache.stores.dynamodb.endpoint',
'cache.stores.octane.driver','cache.prefix','cache.limiter','canny.app_id','cart.default.type',
'cart.default.id','cart.default.purchase_type','cart.default.date_id','cart.default.delivery_method_id','cart.default.items',
'cart.default.subscription','cart.default.notes','cart.default.is_gift','cart.default.recipient_email','cart.default.recipient_notes',
'cart.default.discounts.coupons','cart.default.discounts.gift_card.name','cart.default.discounts.gift_card.code','cart.default.discounts.gift_card.amount','cart.default.discounts.store_credit.amount',
'cart.default.customer.id','cart.default.customer.first_name','cart.default.customer.last_name','cart.default.customer.email','cart.default.customer.phone',
'cart.default.customer.save_for_later','cart.default.customer.opt_in_to_sms','cart.default.customer.subscribed_to_sms','cart.default.shipping.address_id','cart.default.shipping.street',
'cart.default.shipping.street_2','cart.default.shipping.city','cart.default.shipping.state','cart.default.shipping.zip','cart.default.shipping.country',
'cart.default.shipping.save_for_later','cart.default.billing.method','cart.default.billing.source_id','cart.default.billing.save_for_later','cart.database.date_id',
'cart.database.delivery_method_id','cart.database.items','cart.database.subscription','cart.database.notes','cart.database.is_gift',
'cart.database.recipient_email','cart.database.recipient_notes','cart.database.discounts.coupons','cart.database.discounts.gift_card.name','cart.database.discounts.gift_card.code',
'cart.database.discounts.gift_card.amount','cart.database.discounts.store_credit.amount','cart.database.customer.id','cart.database.customer.first_name','cart.database.customer.last_name',
'cart.database.customer.email','cart.database.customer.phone','cart.database.customer.save_for_later','cart.database.customer.opt_in_to_sms','cart.database.customer.subscribed_to_sms',
'cart.database.shipping.address_id','cart.database.shipping.street','cart.database.shipping.street_2','cart.database.shipping.city','cart.database.shipping.state',
'cart.database.shipping.zip','cart.database.shipping.country','cart.database.shipping.save_for_later','cart.database.billing.method','cart.database.billing.source_id',
'cart.database.billing.save_for_later','cart.preorder.type','cart.preorder.date_id','cart.preorder.delivery_method_id','cart.preorder.items',
'cart.preorder.notes','cart.preorder.is_gift','cart.preorder.recipient_email','cart.preorder.recipient_notes','cart.preorder.discounts.coupons',
'cart.preorder.discounts.gift_card.name','cart.preorder.discounts.gift_card.code','cart.preorder.discounts.gift_card.amount','cart.preorder.discounts.store_credit.amount','cart.preorder.customer.id',
'cart.preorder.customer.first_name','cart.preorder.customer.last_name','cart.preorder.customer.email','cart.preorder.customer.phone','cart.preorder.customer.save_for_later',
'cart.preorder.customer.opt_in_to_sms','cart.preorder.customer.subscribed_to_sms','cart.preorder.shipping.street','cart.preorder.shipping.street_2','cart.preorder.shipping.city',
'cart.preorder.shipping.state','cart.preorder.shipping.zip','cart.preorder.shipping.country','cart.preorder.shipping.save_for_later','cart.preorder.billing.method',
'cart.preorder.billing.source_id','cart.preorder.billing.save_for_later','cart.gift-card.type','cart.gift-card.items','cart.gift-card.notes',
'cart.gift-card.discounts.coupons','cart.gift-card.discounts.gift_card.name','cart.gift-card.discounts.gift_card.code','cart.gift-card.discounts.gift_card.amount','cart.gift-card.discounts.store_credit.amount',
'cart.gift-card.customer.id','cart.gift-card.customer.first_name','cart.gift-card.customer.last_name','cart.gift-card.customer.email','cart.gift-card.customer.phone',
'cart.gift-card.customer.save_for_later','cart.gift-card.customer.opt_in_to_sms','cart.gift-card.customer.subscribed_to_sms','cart.gift-card.shipping.street','cart.gift-card.shipping.street_2',
'cart.gift-card.shipping.city','cart.gift-card.shipping.state','cart.gift-card.shipping.zip','cart.gift-card.shipping.country','cart.gift-card.shipping.save_for_later',
'cart.gift-card.billing.method','cart.gift-card.billing.source_id','cart.gift-card.billing.save_for_later','clockwork.enable','clockwork.collect_data_always',
'clockwork.storage','clockwork.storage_files_path','clockwork.storage_sql_database','clockwork.storage_sql_table','clockwork.filter',
'clockwork.filter_uris','clockwork.additional_data_sources','clockwork.headers','compile.files','compile.providers',
'cors.paths','cors.allowed_methods','cors.allowed_origins','cors.allowed_origins_patterns','cors.allowed_headers',
'cors.exposed_headers','cors.max_age','cors.supports_credentials','database.default','database.connections.sqlite.driver',
'database.connections.sqlite.url','database.connections.sqlite.database','database.connections.sqlite.prefix','database.connections.sqlite.foreign_key_constraints','database.connections.sqlite.busy_timeout',
'database.connections.sqlite.journal_mode','database.connections.sqlite.synchronous','database.connections.mysql.driver','database.connections.mysql.url','database.connections.mysql.host',
'database.connections.mysql.port','database.connections.mysql.database','database.connections.mysql.username','database.connections.mysql.password','database.connections.mysql.unix_socket',
'database.connections.mysql.charset','database.connections.mysql.collation','database.connections.mysql.prefix','database.connections.mysql.prefix_indexes','database.connections.mysql.strict',
'database.connections.mysql.engine','database.connections.mysql.options','database.connections.mariadb.driver','database.connections.mariadb.url','database.connections.mariadb.host',
'database.connections.mariadb.port','database.connections.mariadb.database','database.connections.mariadb.username','database.connections.mariadb.password','database.connections.mariadb.unix_socket',
'database.connections.mariadb.charset','database.connections.mariadb.collation','database.connections.mariadb.prefix','database.connections.mariadb.prefix_indexes','database.connections.mariadb.strict',
'database.connections.mariadb.engine','database.connections.mariadb.options','database.connections.pgsql.driver','database.connections.pgsql.url','database.connections.pgsql.host',
'database.connections.pgsql.port','database.connections.pgsql.database','database.connections.pgsql.username','database.connections.pgsql.password','database.connections.pgsql.charset',
'database.connections.pgsql.prefix','database.connections.pgsql.prefix_indexes','database.connections.pgsql.search_path','database.connections.pgsql.sslmode','database.connections.sqlsrv.driver',
'database.connections.sqlsrv.url','database.connections.sqlsrv.host','database.connections.sqlsrv.port','database.connections.sqlsrv.database','database.connections.sqlsrv.username',
'database.connections.sqlsrv.password','database.connections.sqlsrv.charset','database.connections.sqlsrv.prefix','database.connections.sqlsrv.prefix_indexes','database.connections.failed_jobs.driver',
'database.connections.failed_jobs.host','database.connections.failed_jobs.port','database.connections.failed_jobs.database','database.connections.failed_jobs.username','database.connections.failed_jobs.password',
'database.connections.failed_jobs.charset','database.connections.failed_jobs.collation','database.connections.failed_jobs.prefix','database.connections.failed_jobs.strict','database.migrations.table',
'database.migrations.update_date_on_publish','database.redis.client','database.redis.options.cluster','database.redis.options.prefix','database.redis.default.url',
'database.redis.default.host','database.redis.default.username','database.redis.default.password','database.redis.default.port','database.redis.default.database',
'database.redis.default.read_write_timeout','database.redis.cache.url','database.redis.cache.host','database.redis.cache.username','database.redis.cache.password',
'database.redis.cache.port','database.redis.cache.database','database.redis.cache.read_write_timeout','database.redis.horizon.url','database.redis.horizon.host',
'database.redis.horizon.username','database.redis.horizon.password','database.redis.horizon.port','database.redis.horizon.database','database.redis.horizon.read_write_timeout',
'database.redis.horizon.options.prefix','disposable-email.sources','disposable-email.fetcher','disposable-email.storage','disposable-email.whitelist',
'disposable-email.include_subdomains','disposable-email.cache.enabled','disposable-email.cache.store','disposable-email.cache.key','eloquentfilter.namespace',
'eloquentfilter.paginate_limit','excel.exports.chunk_size','excel.exports.pre_calculate_formulas','excel.exports.strict_null_comparison','excel.exports.csv.delimiter',
'excel.exports.csv.enclosure','excel.exports.csv.line_ending','excel.exports.csv.use_bom','excel.exports.csv.include_separator_line','excel.exports.csv.excel_compatibility',
'excel.exports.csv.output_encoding','excel.exports.properties.creator','excel.exports.properties.lastModifiedBy','excel.exports.properties.title','excel.exports.properties.description',
'excel.exports.properties.subject','excel.exports.properties.keywords','excel.exports.properties.category','excel.exports.properties.manager','excel.exports.properties.company',
'excel.imports.read_only','excel.imports.ignore_empty','excel.imports.heading_row.formatter','excel.imports.csv.delimiter','excel.imports.csv.enclosure',
'excel.imports.csv.escape_character','excel.imports.csv.contiguous','excel.imports.csv.input_encoding','excel.imports.properties.creator','excel.imports.properties.lastModifiedBy',
'excel.imports.properties.title','excel.imports.properties.description','excel.imports.properties.subject','excel.imports.properties.keywords','excel.imports.properties.category',
'excel.imports.properties.manager','excel.imports.properties.company','excel.extension_detector.xlsx','excel.extension_detector.xlsm','excel.extension_detector.xltx',
'excel.extension_detector.xltm','excel.extension_detector.xls','excel.extension_detector.xlt','excel.extension_detector.ods','excel.extension_detector.ots',
'excel.extension_detector.slk','excel.extension_detector.xml','excel.extension_detector.gnumeric','excel.extension_detector.htm','excel.extension_detector.html',
'excel.extension_detector.csv','excel.extension_detector.tsv','excel.extension_detector.pdf','excel.value_binder.default','excel.cache.driver',
'excel.cache.batch.memory_limit','excel.cache.illuminate.store','excel.transactions.handler','excel.transactions.db.connection','excel.temporary_files.local_path',
'excel.temporary_files.remote_disk','excel.temporary_files.remote_prefix','excel.temporary_files.force_resync_remote','filesystems.default','filesystems.disks.local.driver',
'filesystems.disks.local.root','filesystems.disks.local.serve','filesystems.disks.local.throw','filesystems.disks.local.report','filesystems.disks.public.driver',
'filesystems.disks.public.root','filesystems.disks.public.url','filesystems.disks.public.visibility','filesystems.disks.public.throw','filesystems.disks.public.report',
'filesystems.disks.s3.driver','filesystems.disks.s3.key','filesystems.disks.s3.secret','filesystems.disks.s3.region','filesystems.disks.s3.bucket',
'filesystems.disks.s3.options.CacheControl','filesystems.disks.s3.url','filesystems.disks.s3.endpoint','filesystems.disks.s3.use_path_style_endpoint','filesystems.disks.s3.throw',
'filesystems.disks.tenants.driver','filesystems.disks.tenants.root','filesystems.disks.client.driver','filesystems.disks.client.root','filesystems.disks.pages.driver',
'filesystems.disks.pages.root','filesystems.disks.seeds.driver','filesystems.disks.seeds.root','filesystems.disks.stubs.driver','filesystems.disks.stubs.root',
'filesystems.links./Users/<USER>/Documents/GitHub/sevensonsfarms/public/storage','filesystems.file_upload_prefix','filesystems.cloud','grazecart.sale_keywords','grazecart.default_map_center',
'grazecart.countries.USA','grazecart.countries.Canada','grazecart.states.AL','grazecart.states.AK','grazecart.states.AZ',
'grazecart.states.AR','grazecart.states.CA','grazecart.states.CO','grazecart.states.CT','grazecart.states.DE',
'grazecart.states.DC','grazecart.states.FL','grazecart.states.GA','grazecart.states.HI','grazecart.states.ID',
'grazecart.states.IL','grazecart.states.IN','grazecart.states.IA','grazecart.states.KS','grazecart.states.KY',
'grazecart.states.LA','grazecart.states.ME','grazecart.states.MD','grazecart.states.MA','grazecart.states.MI',
'grazecart.states.MN','grazecart.states.MS','grazecart.states.MO','grazecart.states.MT','grazecart.states.NE',
'grazecart.states.NV','grazecart.states.NH','grazecart.states.NJ','grazecart.states.NM','grazecart.states.NY',
'grazecart.states.NC','grazecart.states.ND','grazecart.states.OH','grazecart.states.OK','grazecart.states.OR',
'grazecart.states.PA','grazecart.states.RI','grazecart.states.SC','grazecart.states.SD','grazecart.states.TN',
'grazecart.states.TX','grazecart.states.UT','grazecart.states.VT','grazecart.states.VA','grazecart.states.WA',
'grazecart.states.WV','grazecart.states.WI','grazecart.states.WY','grazecart.states.PR','grazecart.provinces.NL',
'grazecart.provinces.PE','grazecart.provinces.NS','grazecart.provinces.NB','grazecart.provinces.QB','grazecart.provinces.ON',
'grazecart.provinces.MB','grazecart.provinces.SK','grazecart.provinces.AB','grazecart.provinces.BC','grazecart.provinces.YT',
'grazecart.provinces.NT','grazecart.provinces.NU','grazecart.referrals.none-selected','grazecart.referrals.Stockman','grazecart.referrals.Dirt to Soil',
'grazecart.referrals.3 Cow Marketing','grazecart.referrals.Graze Magazine','grazecart.referrals.IndependentProcessor','grazecart.referrals.Friend','grazecart.referrals.Online Search',
'grazecart.referrals.Other','grazecart.yearly_revenue_questions.none-selected','grazecart.yearly_revenue_questions.not selling online','grazecart.yearly_revenue_questions.0 to 50k','grazecart.yearly_revenue_questions.50k to 250k',
'grazecart.yearly_revenue_questions.250k to 1m','grazecart.yearly_revenue_questions.1m+','grazecart.new_storefront_enabled','grazecart.welcome_coupon_code','horizon.domain',
'horizon.path','horizon.use','horizon.prefix','horizon.middleware','horizon.waits.redis:default',
'horizon.waits.redis:low','horizon.trim.recent','horizon.trim.pending','horizon.trim.completed','horizon.trim.recent_failed',
'horizon.trim.failed','horizon.trim.monitored','horizon.silenced','horizon.metrics.trim_snapshots.job','horizon.metrics.trim_snapshots.queue',
'horizon.fast_termination','horizon.memory_limit','horizon.defaults.supervisor-1.connection','horizon.defaults.supervisor-1.queue','horizon.defaults.supervisor-1.balance',
'horizon.defaults.supervisor-1.autoScalingStrategy','horizon.defaults.supervisor-1.minProcesses','horizon.defaults.supervisor-1.maxProcesses','horizon.defaults.supervisor-1.maxTime','horizon.defaults.supervisor-1.maxJobs',
'horizon.defaults.supervisor-1.memory','horizon.defaults.supervisor-1.tries','horizon.defaults.supervisor-1.timeout','horizon.defaults.supervisor-1.nice','horizon.environments.production.supervisor-1.maxProcesses',
'horizon.environments.production.supervisor-1.balanceMaxShift','horizon.environments.production.supervisor-1.balanceCooldown','horizon.environments.production.supervisor-1.tries','horizon.environments.staging.supervisor-1.maxProcesses','horizon.environments.staging.supervisor-1.balanceMaxShift',
'horizon.environments.staging.supervisor-1.balanceCooldown','horizon.environments.local.supervisor-1.maxProcesses','horizon.environments.local.supervisor-1.balanceMaxShift','horizon.environments.local.supervisor-1.balanceCooldown','horizon.environments.test.supervisor-1.maxProcesses',
'horizon.environments.test.supervisor-1.balanceMaxShift','horizon.environments.test.supervisor-1.balanceCooldown','ide-helper.filename','ide-helper.models_filename','ide-helper.meta_filename',
'ide-helper.include_fluent','ide-helper.include_factory_builders','ide-helper.write_model_magic_where','ide-helper.write_model_external_builder_methods','ide-helper.write_model_relation_count_properties',
'ide-helper.write_eloquent_model_mixins','ide-helper.include_helpers','ide-helper.helper_files','ide-helper.model_locations','ide-helper.ignored_models',
'ide-helper.model_hooks','ide-helper.extra.Eloquent','ide-helper.extra.Session','ide-helper.magic','ide-helper.interfaces',
'ide-helper.model_camel_case_properties','ide-helper.type_overrides.integer','ide-helper.type_overrides.boolean','ide-helper.include_class_docblocks','ide-helper.force_fqn',
'ide-helper.use_generics_annotations','ide-helper.additional_relation_types','ide-helper.additional_relation_return_types','ide-helper.enforce_nullable_relationships','ide-helper.post_migrate',
'ide-helper.macroable_traits','ide-helper.custom_db_types','image.driver','inertia.ssr.enabled','inertia.ssr.url',
'inertia.testing.ensure_pages_exist','inertia.testing.page_paths','inertia.testing.page_extensions','inertia.history.encrypt','integrations.drip.id',
'integrations.drip.enabled','integrations.drip.api_key','integrations.drip.account_id','integrations.drip.default_subscriber_tags','integrations.drip.title',
'integrations.drip.logo','integrations.drip.svg','integrations.drip.setup_class','integrations.drip.show_slug','integrations.drip.edit_slug',
'integrations.drip.app_slug','integrations.drip.vendor','integrations.drip.short_description','integrations.drip.long_description','integrations.drip.categories',
'integrations.drip.highlights','integrations.drip.image','integrations.drip.screenshots','integrations.drip.support_url','integrations.drip.price_description',
'integrations.drip.product_id','integrations.pickup-manager.id','integrations.pickup-manager.enabled','integrations.pickup-manager.title','integrations.pickup-manager.logo',
'integrations.pickup-manager.svg','integrations.pickup-manager.setup_class','integrations.pickup-manager.show_slug','integrations.pickup-manager.edit_slug','integrations.pickup-manager.app_slug',
'integrations.pickup-manager.vendor','integrations.pickup-manager.short_description','integrations.pickup-manager.long_description','integrations.pickup-manager.categories','integrations.pickup-manager.highlights',
'integrations.pickup-manager.image','integrations.pickup-manager.screenshots','integrations.pickup-manager.support_url','integrations.pickup-manager.price_description','livewire.class_namespace',
'livewire.view_path','livewire.layout','livewire.lazy_placeholder','livewire.temporary_file_upload.disk','livewire.temporary_file_upload.rules',
'livewire.temporary_file_upload.directory','livewire.temporary_file_upload.middleware','livewire.temporary_file_upload.preview_mimes','livewire.temporary_file_upload.max_upload_time','livewire.temporary_file_upload.cleanup',
'livewire.render_on_redirect','livewire.legacy_model_binding','livewire.inject_assets','livewire.navigate.show_progress_bar','livewire.navigate.progress_bar_color',
'livewire.inject_morph_markers','livewire.pagination_theme','logging.default','logging.deprecations.channel','logging.deprecations.trace',
'logging.channels.stack.driver','logging.channels.stack.channels','logging.channels.stack.ignore_exceptions','logging.channels.single.driver','logging.channels.single.path',
'logging.channels.single.level','logging.channels.single.replace_placeholders','logging.channels.daily.driver','logging.channels.daily.path','logging.channels.daily.level',
'logging.channels.daily.days','logging.channels.daily.replace_placeholders','logging.channels.slack.driver','logging.channels.slack.url','logging.channels.slack.username',
'logging.channels.slack.emoji','logging.channels.slack.level','logging.channels.slack.replace_placeholders','logging.channels.papertrail.driver','logging.channels.papertrail.level',
'logging.channels.papertrail.handler','logging.channels.papertrail.handler_with.host','logging.channels.papertrail.handler_with.port','logging.channels.papertrail.handler_with.connectionString','logging.channels.papertrail.processors',
'logging.channels.stderr.driver','logging.channels.stderr.level','logging.channels.stderr.handler','logging.channels.stderr.handler_with.stream','logging.channels.stderr.formatter',
'logging.channels.stderr.processors','logging.channels.syslog.driver','logging.channels.syslog.level','logging.channels.syslog.facility','logging.channels.syslog.replace_placeholders',
'logging.channels.errorlog.driver','logging.channels.errorlog.level','logging.channels.errorlog.replace_placeholders','logging.channels.null.driver','logging.channels.null.handler',
'logging.channels.emergency.path','logging.channels.bugsnag.driver','logging.channels.deprecations.driver','logging.channels.deprecations.handler','mail.default',
'mail.mailers.smtp.transport','mail.mailers.smtp.url','mail.mailers.smtp.host','mail.mailers.smtp.port','mail.mailers.smtp.encryption',
'mail.mailers.smtp.username','mail.mailers.smtp.password','mail.mailers.smtp.timeout','mail.mailers.smtp.local_domain','mail.mailers.ses.transport',
'mail.mailers.postmark.transport','mail.mailers.resend.transport','mail.mailers.sendmail.transport','mail.mailers.sendmail.path','mail.mailers.log.transport',
'mail.mailers.log.channel','mail.mailers.array.transport','mail.mailers.failover.transport','mail.mailers.failover.mailers','mail.mailers.roundrobin.transport',
'mail.mailers.roundrobin.mailers','mail.mailers.mailgun.transport','mail.from.address','mail.from.name','mail.markdown.theme',
'mail.markdown.paths','mail.to','mail.no_reply.address','mail.marketing_from.address','mail.marketing_from.name',
'mail.notifications.new_registration','mail.notifications.subscription_demand_report','mail.notifications.order_credit_report','queue.default','queue.connections.sync.driver',
'queue.connections.database.driver','queue.connections.database.connection','queue.connections.database.table','queue.connections.database.queue','queue.connections.database.retry_after',
'queue.connections.database.after_commit','queue.connections.beanstalkd.driver','queue.connections.beanstalkd.host','queue.connections.beanstalkd.queue','queue.connections.beanstalkd.retry_after',
'queue.connections.beanstalkd.block_for','queue.connections.beanstalkd.after_commit','queue.connections.sqs.driver','queue.connections.sqs.key','queue.connections.sqs.secret',
'queue.connections.sqs.prefix','queue.connections.sqs.queue','queue.connections.sqs.suffix','queue.connections.sqs.region','queue.connections.sqs.after_commit',
'queue.connections.redis.driver','queue.connections.redis.connection','queue.connections.redis.queue','queue.connections.redis.retry_after','queue.connections.redis.block_for',
'queue.connections.redis.after_commit','queue.connections.central.driver','queue.connections.central.table','queue.connections.central.queue','queue.connections.central.retry_after',
'queue.connections.central.after_commit','queue.connections.central.central','queue.batching.database','queue.batching.table','queue.failed.driver',
'queue.failed.database','queue.failed.table','sanctum.stateful','sanctum.guard','sanctum.expiration',
'sanctum.token_prefix','sanctum.middleware.authenticate_session','sanctum.middleware.encrypt_cookies','sanctum.middleware.validate_csrf_token','scout.driver',
'scout.prefix','scout.queue','scout.after_commit','scout.chunk.searchable','scout.chunk.unsearchable',
'scout.soft_delete','scout.identify','scout.algolia.id','scout.algolia.secret','scout.meilisearch.host',
'scout.meilisearch.key','scout.meilisearch.index-settings.App\\Models\\Product.filterableAttributes','scout.meilisearch.index-settings.App\\Models\\Product.sortableAttributes','scout.meilisearch.index-settings.App\\Models\\Product.typoTolerance.enabled','scout.meilisearch.index-settings.App\\Models\\Product.typoTolerance.minWordSizeForTypos.oneTypo',
'scout.meilisearch.index-settings.App\\Models\\Product.typoTolerance.minWordSizeForTypos.twoTypos','scout.meilisearch.index-settings.App\\Models\\Product.typoTolerance.disableOnWords','scout.meilisearch.index-settings.App\\Models\\Product.typoTolerance.disableOnAttributes','scout.typesense.client-settings.api_key','scout.typesense.client-settings.nodes.0.host',
'scout.typesense.client-settings.nodes.0.port','scout.typesense.client-settings.nodes.0.path','scout.typesense.client-settings.nodes.0.protocol','scout.typesense.client-settings.nearest_node.host','scout.typesense.client-settings.nearest_node.port',
'scout.typesense.client-settings.nearest_node.path','scout.typesense.client-settings.nearest_node.protocol','scout.typesense.client-settings.connection_timeout_seconds','scout.typesense.client-settings.healthcheck_interval_seconds','scout.typesense.client-settings.num_retries',
'scout.typesense.client-settings.retry_interval_seconds','scout.typesense.model-settings','services.postmark.token','services.ses.key','services.ses.secret',
'services.ses.region','services.resend.key','services.slack.hook','services.mailgun.central_domain','services.mailgun.domain',
'services.mailgun.marketing_domain','services.mailgun.secret','services.mailgun.endpoint','services.mailgun.scheme','services.stripe.key',
'services.stripe.secret','services.stripe.account','services.stripe.managed','services.stripe.webhook.secret','services.stripe.webhook.tolerance',
'services.stripe.debug','services.geocodio.key','services.geocoderCa.key','services.drip.key','services.drip.account_id',
'services.facebook.pixel_id','services.facebook.access_token','services.facebook.test_event_code','services.google.geocoder_api_key','services.google.places_js_api_key',
'services.google.sheets_api_key','services.google.spreadsheet_id','services.google.worksheet_name','services.google.place_id','services.google.analytics_id',
'services.google.ads_id','services.twilio.default','services.twilio.connections.twilio.sid','services.twilio.connections.twilio.token','services.twilio.connections.twilio.from',
'services.horizon.secret','services.segment.write_key','services.stax.partner_api_key','services.pay_fac.api_key','services.pay_fac.company_id',
'services.pay_fac.js_client_key','services.pay_fac.stripe_key','services.launch_darkly.sdk_key','services.tolstoy.app_key','services.cloudfront.url',
'sluggable.source','sluggable.maxLength','sluggable.maxLengthKeepWords','sluggable.method','sluggable.separator',
'sluggable.unique','sluggable.uniqueSuffix','sluggable.firstUniqueSuffix','sluggable.includeTrashed','sluggable.reserved',
'sluggable.onUpdate','sluggable.slugEngineOptions','telescope.enabled','telescope.domain','telescope.path',
'telescope.driver','telescope.storage.database.connection','telescope.storage.database.chunk','telescope.queue.connection','telescope.queue.queue',
'telescope.queue.delay','telescope.middleware','telescope.only_paths','telescope.ignore_paths','telescope.ignore_commands',
'telescope.watchers.Laravel\\Telescope\\Watchers\\CacheWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\CommandWatcher.enabled','telescope.watchers.Laravel\\Telescope\\Watchers\\CommandWatcher.ignore','telescope.watchers.Laravel\\Telescope\\Watchers\\DumpWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\EventWatcher',
'telescope.watchers.Laravel\\Telescope\\Watchers\\ExceptionWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\JobWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\LogWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\MailWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\ModelWatcher.enabled',
'telescope.watchers.Laravel\\Telescope\\Watchers\\ModelWatcher.events','telescope.watchers.Laravel\\Telescope\\Watchers\\NotificationWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\QueryWatcher.enabled','telescope.watchers.Laravel\\Telescope\\Watchers\\QueryWatcher.ignore_packages','telescope.watchers.Laravel\\Telescope\\Watchers\\QueryWatcher.slow',
'telescope.watchers.Laravel\\Telescope\\Watchers\\RedisWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\RequestWatcher.enabled','telescope.watchers.Laravel\\Telescope\\Watchers\\RequestWatcher.size_limit','telescope.watchers.Laravel\\Telescope\\Watchers\\GateWatcher.enabled','telescope.watchers.Laravel\\Telescope\\Watchers\\GateWatcher.ignore_abilities',
'telescope.watchers.Laravel\\Telescope\\Watchers\\GateWatcher.ignore_packages','telescope.watchers.Laravel\\Telescope\\Watchers\\ScheduleWatcher','theme.id','theme.title','theme.description',
'theme.view_path','theme.settings','theme.css','theme.css_preview','theme.custom_css',
'theme.theme_builder','theme.google_web_fonts','tinker.commands','tinker.alias','tinker.dont_alias',
'twilio-notification-channel.enabled','twilio-notification-channel.username','twilio-notification-channel.password','twilio-notification-channel.auth_token','twilio-notification-channel.account_sid',
'twilio-notification-channel.from','twilio-notification-channel.alphanumeric_sender','twilio-notification-channel.shorten_urls','twilio-notification-channel.sms_service_sid','twilio-notification-channel.debug_to',
'twilio-notification-channel.ignored_error_codes','twilio-notification-channel.message_size_limit','hashing.driver','hashing.bcrypt.rounds','hashing.bcrypt.verify',
'hashing.bcrypt.limit','hashing.argon.memory','hashing.argon.threads','hashing.argon.time','hashing.argon.verify',
'hashing.rehash_on_login','session.driver','session.lifetime','session.expire_on_close','session.encrypt',
'session.files','session.connection','session.table','session.store','session.lottery',
'session.cookie','session.path','session.domain','session.secure','session.http_only',
'session.same_site','session.partitioned','concurrency.default','broadcasting.default','broadcasting.connections.reverb.driver',
'broadcasting.connections.reverb.key','broadcasting.connections.reverb.secret','broadcasting.connections.reverb.app_id','broadcasting.connections.reverb.options.host','broadcasting.connections.reverb.options.port',
'broadcasting.connections.reverb.options.scheme','broadcasting.connections.reverb.options.useTLS','broadcasting.connections.reverb.client_options','broadcasting.connections.pusher.driver','broadcasting.connections.pusher.key',
'broadcasting.connections.pusher.secret','broadcasting.connections.pusher.app_id','broadcasting.connections.pusher.options.cluster','broadcasting.connections.pusher.options.host','broadcasting.connections.pusher.options.port',
'broadcasting.connections.pusher.options.scheme','broadcasting.connections.pusher.options.encrypted','broadcasting.connections.pusher.options.useTLS','broadcasting.connections.pusher.client_options','broadcasting.connections.ably.driver',
'broadcasting.connections.ably.key','broadcasting.connections.log.driver','broadcasting.connections.null.driver','view.paths','view.compiled',
'debugbar.enabled','debugbar.hide_empty_tabs','debugbar.except','debugbar.storage.enabled','debugbar.storage.open',
'debugbar.storage.driver','debugbar.storage.path','debugbar.storage.connection','debugbar.storage.provider','debugbar.storage.hostname',
'debugbar.storage.port','debugbar.editor','debugbar.remote_sites_path','debugbar.local_sites_path','debugbar.include_vendors',
'debugbar.capture_ajax','debugbar.add_ajax_timing','debugbar.ajax_handler_auto_show','debugbar.ajax_handler_enable_tab','debugbar.defer_datasets',
'debugbar.error_handler','debugbar.clockwork','debugbar.collectors.phpinfo','debugbar.collectors.messages','debugbar.collectors.time',
'debugbar.collectors.memory','debugbar.collectors.exceptions','debugbar.collectors.log','debugbar.collectors.db','debugbar.collectors.views',
'debugbar.collectors.route','debugbar.collectors.auth','debugbar.collectors.gate','debugbar.collectors.session','debugbar.collectors.symfony_request',
'debugbar.collectors.mail','debugbar.collectors.laravel','debugbar.collectors.events','debugbar.collectors.default_request','debugbar.collectors.logs',
'debugbar.collectors.files','debugbar.collectors.config','debugbar.collectors.cache','debugbar.collectors.models','debugbar.collectors.livewire',
'debugbar.collectors.jobs','debugbar.collectors.pennant','debugbar.options.time.memory_usage','debugbar.options.messages.trace','debugbar.options.messages.capture_dumps',
'debugbar.options.memory.reset_peak','debugbar.options.memory.with_baseline','debugbar.options.memory.precision','debugbar.options.auth.show_name','debugbar.options.auth.show_guards',
'debugbar.options.db.with_params','debugbar.options.db.exclude_paths','debugbar.options.db.backtrace','debugbar.options.db.backtrace_exclude_paths','debugbar.options.db.timeline',
'debugbar.options.db.duration_background','debugbar.options.db.explain.enabled','debugbar.options.db.hints','debugbar.options.db.show_copy','debugbar.options.db.slow_threshold',
'debugbar.options.db.memory_usage','debugbar.options.db.soft_limit','debugbar.options.db.hard_limit','debugbar.options.mail.timeline','debugbar.options.mail.show_body',
'debugbar.options.views.timeline','debugbar.options.views.data','debugbar.options.views.group','debugbar.options.views.exclude_paths','debugbar.options.route.label',
'debugbar.options.session.hiddens','debugbar.options.symfony_request.label','debugbar.options.symfony_request.hiddens','debugbar.options.events.data','debugbar.options.logs.file',
'debugbar.options.cache.values','debugbar.inject','debugbar.route_prefix','debugbar.route_middleware','debugbar.route_domain',
'debugbar.theme','debugbar.debug_backtrace_limit','cookie-consent.enabled','cookie-consent.cookie_name','cookie-consent.cookie_lifetime',);
        registerArgumentsSet('middleware', 
'web','api','tenant','public_api','webCsrfExempt',
'webhook','css','horizon','pos','auth',
'auth.basic','auth.session','cache.headers','can','guest',
'password.confirm','precognitive','signed','throttle','verified',
'ForceSeoStoreUrls','UnconfirmedAccount','admin','auth.admin','auth.customer',
'auth.editor','central.auth','central.guest','detour','order',
'track-view',);
        registerArgumentsSet('routes', 
'livewire.update','debugbar.openhandler','debugbar.clockwork','debugbar.telescope','debugbar.assets.css',
'debugbar.assets.js','debugbar.cache.delete','debugbar.queries.explain','horizon.stats.index','horizon.workload.index',
'horizon.masters.index','horizon.monitoring.index','horizon.monitoring.store','horizon.monitoring-tag.paginate','horizon.monitoring-tag.destroy',
'horizon.jobs-metrics.index','horizon.jobs-metrics.show','horizon.queues-metrics.index','horizon.queues-metrics.show','horizon.jobs-batches.index',
'horizon.jobs-batches.show','horizon.jobs-batches.retry','horizon.pending-jobs.index','horizon.completed-jobs.index','horizon.silenced-jobs.index',
'horizon.failed-jobs.index','horizon.failed-jobs.show','horizon.retry-jobs.show','horizon.jobs.show','horizon.index',
'sanctum.csrf-cookie','livewire.upload-file','livewire.preview-file','telescope',
'api.v1.heartbeat','products.inventory.update',
'api.v2.customers.index','api.v2.customers.show','api.v2.delivery-methods.index','api.v2.delivery-methods.show','api.v2.heartbeat.show',
'api.v2.orders.index','api.v2.orders.show','api.v2.sales-channels.index','api.v2.subscriptions.index','api.v2.subscriptions.show',
'api.v2.users.current.show','api.v2.webhooks.store','api.v2.webhooks.destroy','twilio.webhook.no-reply','api.carts.shipping-address.store',
'api.carts.confirm','api.carts.show','api.carts.promotions.store','api.carts.shipping.update','api.customers.update',
'api.locations.address-eligibility','api.locations.cart-eligibility','api.products.checkout.store','api.setup-intents.store','api.users.payment-methods.store',
'api.user.setup-intents.store','api.collections.index','api.coupons.index','api.coupons.store',
'coupons.show','api.coupons.update','api.coupons.destroy','api.detours.index','api.detours.store',
'detours.show','api.detours.update','api.detours.destroy','api.duplicate-pages.store','api.gift-card-codes.store',
'api.leads.index','api.locations.delivery.index','api.locations.delivery.create','api.locations.delivery.store','api.locations.delivery.show',
'api.locations.delivery.edit','api.locations.delivery.update','api.locations.delivery.destroy','api.delivery-zones.postal-codes.index','api.delivery-zones.postal-codes.create',
'api.delivery-zones.postal-codes.store','api.delivery-zones.postal-codes.show','api.delivery-zones.postal-codes.edit','api.delivery-zones.postal-codes.update','api.delivery-zones.postal-codes.destroy','api.orders.show','api.orders.show','api.orders.update','api.order-events.index',
'api.orders.items.index','api.order-statuses.index','api.pages.content.index',
'api.pages.update','admin.pages.widgets.store','admin.pages.widgets.update',
'admin.pages.widgets.destroy','api.pages.widgets.duplicate',
'api.pickup-manager.orders.index','api.orders.charge-customer.store','api.pickups.index','api.pickups.show','api.products.index',
'products.prices.index','products.prices.create','products.prices.store','products.prices.show','products.prices.edit',
'products.prices.update','products.prices.destroy','price-groups.index','price-groups.create','price-groups.store',
'price-groups.show','price-groups.edit','price-groups.update','price-groups.destroy','templates.index','templates.create','templates.store',
'templates.show','templates.edit','templates.update','templates.destroy','templates.segments.index',
'templates.segments.create','templates.segments.store','templates.segments.show','templates.segments.edit','templates.segments.update',
'templates.segments.destroy','api.users.index',
'api.users.store','api.dashboard.index','api.photos.index','api.photos.store',
'photos.show','api.photos.update','api.photos.destroy','api.theme.categories.products.index','api.theme.fulfillment-options.index','api.theme.pickups.index','api.theme.phone-number-lookups.store',
'products.index','api.theme.lead.store','api.theme.redeemed-gift-certificates.store',
'admin.login','admin.authenticate','admin.logout','dashboard','photos.tags.index','photos.tags.store','photos.tags.edit','photos.tags.update',
'photos.tags.destroy','admin.posts.index','admin.posts.store','admin.posts.show','admin.posts.edit',
'admin.posts.update','admin.posts.destroy','admin.recipes.tags.store','admin.recipes.index','admin.recipes.create','admin.recipes.store',
'admin.recipes.show','admin.recipes.edit','admin.recipes.update','admin.recipes.destroy','admin.tags.index',
'admin.tags.create','admin.tags.store','admin.tags.show','admin.tags.edit','admin.tags.update',
'admin.tags.destroy','api-keys.index','api-keys.create','api-keys.store','api-keys.show',
'api-keys.edit','api-keys.update','api-keys.destroy',
'admin.account.api-tokens.show','admin.account.api-tokens.store','admin.account.api-tokens.destroy','admin.collections.index',
'admin.collections.create','admin.collections.store','admin.collections.show','admin.collections.edit','admin.collections.update',
'admin.collections.destroy','admin.subcollections.index','admin.subcollections.create','admin.subcollections.store','admin.subcollections.show',
'admin.subcollections.edit','admin.subcollections.update','admin.subcollections.destroy','coupons.index','admin.integrations.drip.update',
'admin.integrations.subscribe-save.update','admin.footer.widgets.store','admin.footer.widgets.update',
'admin.footer.widgets.destroy','admin.locations.index','admin.locations.index','admin.locations.create','admin.locations.store',
'admin.locations.show','admin.locations.edit','admin.locations.update','admin.locations.destroy','admin.delivery.index',
'admin.delivery.create','admin.delivery.store','admin.delivery.show','admin.delivery.edit','admin.delivery.update',
'admin.delivery.destroy','admin.pickups.index','admin.pickups.create','admin.pickups.store','admin.pickups.show',
'admin.pickups.edit','admin.pickups.update','admin.pickups.destroy','admin.pickups.fees.index','admin.pickups.fees.store',
'admin.pickups.fees.update','admin.pickups.fees.destroy','admin.menus.edit','admin.menu-items','admin.menu-items.store','admin.menu-items.update','admin.orders.index',
'admin.orders.create','admin.orders.store','admin.orders.show','admin.orders.edit','admin.orders.update',
'admin.orders.destroy','admin.orders.bulk-cancel','admin.orders.notifications.bulk','admin.orders.bulk-update.status',
'admin.orders.bulk-update.print','admin.orders.bulk-update.export','admin.orders.bulk-update.tag','admin.orders.bulk-process','admin.orders.notifications.confirmed','admin.orders.notifications.custom','admin.orders.items.index','admin.orders.items.create','admin.orders.items.store',
'admin.orders.items.show','admin.orders.items.edit','admin.orders.items.update','admin.orders.items.destroy',
'admin.orders.fees.index','admin.orders.fees.store','admin.orders.fees.update','admin.orders.fees.destroy','orders.discounts.store',
'orders.discounts.destroy','orders.payments.store','orders.payments.update','orders.payments.destroy','admin.orders.card-payments',
'admin.payments.refunds.store','admin.orders.process',
'admin.orders.confirm.store','admin.orders.cancel.store','admin.pages.widget-sort.store','admin.pages.index','admin.pages.create',
'admin.pages.store','admin.pages.show','admin.pages.edit','admin.pages.update','admin.pages.destroy','admin.payments.edit','admin.products.import.store','admin.products.sync-reserve-inventory.store',
'admin.products.index','admin.products.create','admin.products.store','admin.products.show','admin.products.edit',
'admin.products.update','admin.products.destroy','admin.products.description.update','admin.products.update.seo','admin.products.duplicate',
'admin.gift-cards.index','gift-cards.store','admin.gift-cards.edit','admin.gift-cards.update','gift-cards.destroy',
'admin.gift-cards.codes.show','admin.gift-cards.codes.update','admin.gift-cards-codes.send.store','admin.protocols.index','admin.protocols.create',
'admin.protocols.store','admin.protocols.show','admin.protocols.edit','admin.protocols.update','admin.protocols.destroy',
'admin.proposals.index','admin.proposals.create','admin.proposals.store','admin.proposals.show','admin.proposals.edit',
'admin.proposals.update','admin.proposals.destroy','admin.reports.stock-out','admin.reports.delivery-sales','admin.reports.income-analysis',
'admin.reports.product-sales','admin.reports.inventory','admin.reports.harvest','admin.reports.unused-balances',
'admin.reports.subscription-inventory','admin.reports.restock-log','admin.reports.account-credit-applied','admin.reports.first-time-renewal','admin.settings',
'admin.settings.customer.update','admin.apps.index','admin.apps.show','admin.apps.store','admin.apps.destroy',
'admin.apps.edit','admin.settings.edit','admin.settings.update','admin.schedules.index','admin.schedules.create',
'admin.schedules.store','admin.schedules.show','admin.schedules.edit','admin.schedules.update','admin.schedules.destroy',
'admin.schedules.activate.update','admin.schedules.dates.index','admin.schedules.dates.create','admin.schedules.dates.store',
'admin.schedules.dates.show','admin.schedules.dates.edit','admin.schedules.dates.update','admin.schedules.dates.destroy','admin.schedules.reminders.store',
'team.index','team.update','admin.templates.index','admin.templates.create','admin.templates.store',
'admin.templates.show','admin.templates.edit','admin.templates.update','admin.templates.destroy','admin.templates.preview.store',
'templates.segments.index','templates.segments.store','templates.segments.edit','templates.segments.update','templates.segments.destroy',
'admin.users.resetPassword','admin.users.index','admin.users.create','admin.users.store',
'admin.users.show','admin.users.edit','admin.users.update','admin.users.destroy','admin.users.cards.store',
'admin.users.cards.destroy',
'admin.subscriptions.index','admin.subscriptions.edit','admin.subscriptions.update','admin.categories.index','admin.categories.edit',
'admin.packing-groups.index','admin.vendors.index','admin.vendors.create','admin.vendors.store',
'admin.vendors.show','admin.vendors.edit','admin.vendors.update','admin.vendors.destroy','admin.pickup-manager.index','css.theme-variables.show','theme.pages.styles.show','theme.newsletter.store','accessibility-statement',
'privacy-policy','terms-of-service','checkout.index','checkout.confirm.show','checkout.confirm.store',
'checkout.complete.show','customer.notifications.sms-marketing.store','customer.profile',
'customer.update','customer.destroy','customer.addresses','customer.notifications','customer.notifications.update',
'customer.password','customer.update.password','customer.orders','customer.orders.show','customer.orders.reorder',
'customer.orders.destroy','account.cards.index','account.cards.create','account.cards.store','account.cards.edit',
'account.cards.update','account.cards.destroy','account.gift-cards.show','account.referrals.show','customers.recurring.edit',
'customers.recurring.update','customers.promo-item.update','customers.recurring.cancel','customers.recurring.skip','customers.recurring.get-it-sooner',
'customers.recurring.frequency.update','login','logout','registration',
'choose-password.show','choose-password.store','homepage.show','login.store','register',
'messaging.contact','page.contact','register.lead.show','register.lead.store','cart.show',
'cart.update','cart.items.store','cart.items.update','cart.items.destroy','cart.short-stock.update',
'checkout.offers.show','order.show','order.update','theme.orders.coupons.index','theme.orders.coupons.store',
'theme.orders.coupons.destroy','order.item.store','subscriptions.update','subscriptions.skip','store.index',
'store.show','store.products.checkout.show','store.collections.show','store.subcollection.index','store.vendor',
'store.protocol','store.categories.show','store.subcategories.show','blog.index','blog.authors.show',
'blog.tags.show','blog.show','recipes.index','recipes.show','vendor.index',
'vendor.show','page.protocols',
'page.show','storage.local',);
        registerArgumentsSet('views', 
'account.api-tokens','account.notifications','account.partials.invoice','account.partials.navigation','account.partials.plans',
'account.profile','account.security','apps.edit.drip','apps.edit.pickup-manager','apps.edit.pos',
'apps.index','apps.layout','apps.partials.uninstall-app-modal','apps.show','categories.edit',
'categories.index','collections.edit','collections.index','collections.partials.collection','collections.partials.create-collection-modal',
'collections.partials.delete-collection-modal','collections.partials.index-table','collections.partials.products','collections.partials.settings','collections.partials.toolbar',
'components.form.category-select','components.form.channel-select','components.form.collection-select','components.form.color-picker','components.form.confirmation-status-select',
'components.form.country-select','components.form.date-range-dropdown','components.form.deadline-hour-select','components.form.delivery-fee-select','components.form.delivery-frequency-select',
'components.form.delivery-method-by-state-select','components.form.delivery-method-select','components.form.delivery-method-status-select','components.form.email-template-select','components.form.map-zoom-select',
'components.form.month-select','components.form.order-item-sort-select','components.form.order-status-select','components.form.order-tag-select','components.form.packing-group-select',
'components.form.page-select','components.form.payment-method-select','components.form.payment-status-select','components.form.pikaday-input','components.form.preferred-domain-select',
'components.form.pricing-group-select','components.form.product-type-select','components.form.proposal-rating-select','components.form.proposal-status-select','components.form.reminder-hour-select',
'components.form.reminder-meridiem-select','components.form.reminder-minute-select','components.form.sale-status-select','components.form.schedule-select','components.form.sms-template-select',
'components.form.staff-select','components.form.state-select','components.form.store-sort-select','components.form.store-template','components.form.storefront-select',
'components.form.tag-select','components.form.unit-of-issue-select','components.form.vendor-select','components.form.visibility-select','components.form.year-select',
'components.modal','components.select','components.select-table','components.select-table.bulk-selector','components.select-table.row-checkbox',
'coupons.index','dashboard.default.index','dashboard.default.partials.inventory','dashboard.default.partials.orders','dashboard.default.partials.schedules',
'dashboard.editor.index','dashboard.index','dashboard.partials.inventory','dashboard.partials.orders','dashboard.partials.schedules',
'dashboard.welcome','dates.edit','dates.partials.delete-date-modal','emails.blank','emails.custom',
'emails.default.gift-card-issued','emails.default.gift-card-issued-plaintext','emails.default.partials._gift-card-header','emails.default.partials._header','emails.default.password-reset',
'emails.default.password-reset-plaintext','emails.default.pos-order-confirmation','emails.default.pos-order-confirmation-plaintext','emails.default.recurring-order-confirmation','emails.default.recurring-order-confirmation-plaintext',
'emails.default.reorder-confirmation','emails.default.reorder-confirmation-plaintext','emails.default.subscription-deadline-reminder','emails.default.subscription-deadline-reminder-plaintext','emails.marketing-html',
'emails.marketing-text','emails.notification','emails.notifications.completed-export','emails.notifications.layout','emails.notifications.marketing-review',
'emails.notifications.order-canceled','emails.notifications.order-comment','emails.notifications.order-confirmed','emails.notifications.order-confirmed_text','emails.notifications.password-reminder',
'emails.notifications.password-reset','emails.notifications.proposal','emails.notifications.recurring-order-canceled-feedback','emails.notifications.support','emails.notifications.support_text',
'emails.partials.custom','emails.partials.footer','emails.partials.footer-plain-text','emails.partials.header','emails.partials.html-template',
'emails.partials.order-items','emails.partials.order-items-plain-text','emails.partials.order-payments','emails.partials.order-payments-text','emails.partials.order-summary',
'emails.partials.order-summary-text','emails.partials.password-reminder','emails.partials.password-reminder-text','emails.partials.text-template','emails.password',
'emails.transactional-html','emails.transactional-text','emails.users.reset-password','errors.401','errors.403',
'errors.404','errors.405','errors.419','errors.429','errors.500',
'errors.503','errors.illustrated-layout','errors.layout','errors.minimal','errors.theme.404',
'errors.theme.405','errors.theme.503','filters.edit','filters.index','filters.partials.delete-filter-modal',
'footer.index','get-started.complete','get-started.show','integrations.index','layouts.account',
'layouts.full_screen','layouts.inertia','layouts.login','layouts.main','layouts.marketing',
'layouts.sidebar.navigation.admin','layouts.sidebar.navigation.editor','livewire.accounts.registration.complete','livewire.accounts.registration.index','livewire.accounts.registration.partials.business',
'livewire.accounts.registration.partials.complete','livewire.accounts.registration.partials.optional','livewire.accounts.registration.partials.personal','livewire.addresses-table','livewire.admin-notifications',
'livewire.bundle-store-template','livewire.category-show','livewire.category-table','livewire.first-time-renewal','livewire.forms.payment-source-select',
'livewire.modals.add-address','livewire.modals.add-category','livewire.modals.add-packing-group','livewire.modals.add-price-tier','livewire.modals.add-product-to-bundle',
'livewire.modals.add-subscription-item','livewire.modals.add-widget','livewire.modals.bulk-edit-subscriptions','livewire.modals.cancel-subscription-confirmation','livewire.modals.delete-address-confirmation',
'livewire.modals.delete-bundle-product-confirmation','livewire.modals.delete-delivery-method-confirmation','livewire.modals.delete-page-confirmation','livewire.modals.delete-product-confirmation','livewire.modals.delete-subscription-item-confirmation',
'livewire.modals.edit-address','livewire.modals.edit-bundle-product','livewire.modals.edit-price-tier','livewire.modals.edit-subscription-item','livewire.modals.edit-widget',
'livewire.modals.modal','livewire.modals.order-payment','livewire.modals.order-replacement','livewire.modals.preview-page','livewire.modals.preview-widget',
'livewire.modals.product-inventory-history','livewire.order-info-boxes','livewire.packing-group-table','livewire.pages.show','livewire.pages.widgets.banner',
'livewire.pages.widgets.cta','livewire.pages.widgets.faq','livewire.pages.widgets.grid','livewire.pages.widgets.product','livewire.pages.widgets.reviews',
'livewire.pages.widgets.side-by-side','livewire.payment-options.card','livewire.payment-options.gateways.stripe','livewire.payment-options.invoice','livewire.payment-options.pay-at-pickup',
'livewire.product-inventory-settings','livewire.product-photos','livewire.product-prices','livewire.product-select','livewire.product-store-template',
'livewire.restock-log','livewire.settings.site','livewire.standard-store-template','livewire.subscription-events','livewire.subscription-orders',
'livewire.subscription-product-select','livewire.subscription-products','livewire.subscriptions-table','livewire.user-profile-show','livewire.users.credit-cards',
'locations.edit','locations.market.index','locations.partials.address','locations.partials.contact','locations.partials.create-location-modal',
'locations.partials.delete-location-modal','locations.partials.description','locations.partials.index-table','locations.partials.navigation','locations.partials.settings',
'locations.restaurant.index','locations.retail.index','logistics.delivery.edit','logistics.delivery.index','logistics.delivery.partials.description',
'logistics.delivery.partials.fees','logistics.delivery.partials.filter-form','logistics.delivery.partials.index-table','logistics.delivery.partials.index-table-disabled','logistics.delivery.partials.messages',
'logistics.delivery.partials.settings','logistics.delivery.partials.zone','logistics.pickup.edit','logistics.pickup.index','logistics.pickup.partials.address',
'logistics.pickup.partials.create-pickup-modal','logistics.pickup.partials.description','logistics.pickup.partials.fees','logistics.pickup.partials.filter-form','logistics.pickup.partials.index-table',
'logistics.pickup.partials.messages','logistics.pickup.partials.seo','logistics.pickup.partials.settings','map-marker-popup','media.index',
'menus.edit','menus.index','menus.partials.categories','menus.partials.collections','menus.partials.custom-links',
'menus.partials.custom-pages','menus.partials.locations','menus.partials.pages','menus.partials.subcategories','menus.partials.submenu',
'messages.index','no-site-found','notifications::email','orders.edit','orders.index',
'orders.partials.charge-modal','orders.partials.confirm-order-modal','orders.partials.contact-order-modal','orders.partials.custom-order-message-modal','orders.partials.delete-order-modal',
'orders.partials.filter-form','orders.partials.index-table','orders.partials.info-boxes','orders.partials.notify-modal','orders.partials.payment-order-modal',
'orders.partials.pickup-order-modal','orders.partials.reorder-modal','orders.partials.toolbars.confirmed','orders.partials.toolbars.unconfirmed','orders.show',
'packing-groups.edit','pages.index','pages.legacy-edit','pages.partials.create-page-modal','pages.partials.duplicate-page-modal',
'pages.partials.filter-form','pages.partials.index-table','pages.partials.layout','pages.partials.scripts','pages.partials.seo',
'pages.partials.settings','pages.promo-edit','pagination.livewire-tailwind','pagination::bootstrap-4','pagination::bootstrap-5',
'pagination::default','pagination::semantic-ui','pagination::simple-bootstrap-4','pagination::simple-bootstrap-5','pagination::simple-default',
'pagination::simple-tailwind','pagination::tailwind','partials.admin-google-analytics-tag','partials.applied-filters','partials.billing.stripe',
'partials.check-for-unsaved-changes','partials.errors','partials.messenger','partials.modal','partials.navigation.admin',
'partials.navigation.editor','partials.saved-filters','partials.support','partials.toolbar','partials.top-navigation',
'passwords.forgot','passwords.reset','payment-options.index','payments.edit.card','payments.edit.card.stripe',
'payments.edit.invoice','payments.edit.pickup','payments.index','payments.partials.gateways.card','payments.partials.gateways.invoice',
'payments.partials.gateways.pickup','payments.partials.stripe-connect','pickupManager::layout','pickupManager::partials.header','pickupManager::pickup',
'pickupManager::schedule','pickupManager::schedules','pos.app','posts.edit','posts.index',
'posts.partials.create-post-modal','posts.partials.delete-post-modal','posts.partials.draft-toolbar','posts.partials.filter-form','posts.partials.index-table',
'posts.partials.published-toolbar','print_templates.gift-card.default.index','print_templates.invoice.default._header','print_templates.invoice.default._items','print_templates.invoice.default.index',
'print_templates.invoice.legacy.index','print_templates.invoice.sevensonsfarms.index','print_templates.layout','print_templates.packing-label.default.index','print_templates.packing.compact._item_row',
'print_templates.packing.compact.index','print_templates.packing.consolidated._items','print_templates.packing.consolidated.index','print_templates.packing.default._item_row','print_templates.packing.default.index',
'print_templates.packing.sevensonsfarms._items','print_templates.packing.sevensonsfarms.index','print_templates.packing.sevensonsfarms_custom._items','print_templates.packing.sevensonsfarms_custom.index','print_templates.product-label.default.index',
'print_templates.shipping-label.default.index','privacy-policy','products.edit','products.gift-cards.codes.show','products.gift-cards.edit',
'products.gift-cards.index','products.gift-cards.partials.create-gift-card-modal','products.gift-cards.partials.description','products.gift-cards.partials.index-table','products.gift-cards.partials.issued',
'products.index','products.partials.checkout','products.partials.collection','products.partials.create-product-modal','products.partials.delete-product-modal',
'products.partials.description','products.partials.duplicate-product-modal','products.partials.filter-form','products.partials.import-product-modal','products.partials.index-table',
'products.partials.inventory','products.partials.items','products.partials.photos','products.partials.price','products.partials.protocols',
'products.partials.quick-edit.accounting_class','products.partials.quick-edit.barcode','products.partials.quick-edit.custom_sort','products.partials.quick-edit.fulfillment_instructions','products.partials.quick-edit.inventory',
'products.partials.quick-edit.item_cost','products.partials.quick-edit.notes','products.partials.quick-edit.oos_threshold_inventory','products.partials.quick-edit.other_inventory','products.partials.quick-edit.processor_inventory',
'products.partials.quick-edit.sale','products.partials.quick-edit.sale_unit_price','products.partials.quick-edit.sku','products.partials.quick-edit.stock_out_inventory','products.partials.quick-edit.taxable',
'products.partials.quick-edit.unit_description','products.partials.quick-edit.unit_price','products.partials.quick-edit.visible','products.partials.quick-edit.weight','products.partials.seo',
'products.partials.settings','products.partials.tags','products.partials.template','proposals.edit','proposals.index',
'proposals.partials.create-proposal-modal','proposals.partials.delete-proposal-modal','proposals.partials.filter-form','proposals.partials.list','proposals.partials.map',
'proposals.partials.marker-popup','protocols.edit','protocols.index','protocols.partials.create-protocol-modal','protocols.partials.delete-protocol-modal',
'protocols.partials.edit-form','protocols.partials.index-table','recipes.edit','recipes.index','recipes.partials.create-recipe-modal',
'recipes.partials.delete-recipe-modal','recipes.partials.draft-toolbar','recipes.partials.filter-form','recipes.partials.index-table','recipes.partials.published-toolbar',
'reports.account-credit-applied.index','reports.account-credit-applied.partials.filter-form','reports.account-credit-applied.partials.index-table','reports.delivery-sales.index','reports.delivery-sales.partials.filter-form',
'reports.delivery-sales.partials.index-table','reports.first-time-renewal.index','reports.first-time-renewal.partials.filter-form','reports.first-time-renewal.partials.index-table','reports.harvest.index',
'reports.harvest.partials.filter-form','reports.harvest.partials.index-table','reports.income-analysis.index','reports.income-analysis.partials.filter-form','reports.income-analysis.partials.index-table',
'reports.inventory.index','reports.inventory.partials.filter-form','reports.inventory.partials.index-table','reports.product-sales.index','reports.product-sales.partials.filter-form',
'reports.product-sales.partials.index-table','reports.restock-log.index','reports.stock-out.index','reports.stock-out.partials.index-table','reports.subscription-inventory.index',
'reports.subscription-inventory.partials.filter-form','reports.subscription-inventory.partials.index-table','schedules.edit','schedules.index','schedules.partials.activate-schedule-modal',
'schedules.partials.activate-schedule-multi-day-modal','schedules.partials.create-date-modal','schedules.partials.create-date-override-modal','schedules.partials.create-schedule-modal','schedules.partials.dates',
'schedules.partials.delete-schedule-modal','schedules.partials.delivery-frequency','schedules.partials.delivery-methods','schedules.partials.index-table','schedules.partials.notification-modal',
'schedules.partials.notifications','schedules.partials.settings','schedules.print','sessions.login','settings.advanced',
'settings.apps','settings.blog','settings.customers','settings.general','settings.index',
'settings.layout','settings.my-site','settings.notifications','settings.orders','settings.price-groups',
'settings.redirects','settings.store','settings.subscribe-and-save.incentives','settings.subscribe-and-save.notifications','settings.subscribe-and-save.products',
'settings.subscribe-save','settings.terms','sitemap','subcollections.edit','subcollections.index',
'subcollections.partials.collection','subcollections.partials.create-collection-modal','subcollections.partials.delete-collection-modal','subcollections.partials.index-table','subcollections.partials.settings',
'subcollections.partials.toolbar','subscriptions.edit','subscriptions.index','subscriptions.partials.events','subscriptions.partials.orders',
'subscriptions.partials.products','tags.edit','tags.index','tags.partials.create-tag-modal','tags.partials.delete-tag-modal',
'tags.partials.edit-form','tags.partials.index-table','team.index','templates.edit','templates.index',
'templates.layout','templates.partials.create-sms-template-modal','templates.partials.create-template-modal','templates.partials.edit-sms-template-modal','templates.partials.index-table',
'templates.partials.merge-tags.order','templates.partials.merge-tags.pickup','templates.partials.merge-tags.sms','templates.partials.merge-tags.user','templates.partials.sms-index-table',
'templates.render','templates.segments.ButtonSegment','templates.segments.DividerSegment','templates.segments.EmailHeader','templates.segments.HTML',
'templates.segments.HtmlCode','templates.segments.Image','templates.segments.Photo','templates.segments.RichText','templates.sms',
'theme::_layouts.checkout','theme::_layouts.livewire','theme::_layouts.main','theme::_layouts.one_page_checkout','theme::_layouts.page_preview',
'theme::_layouts.policy','theme::_layouts.promo_page_preview','theme::_partials.admin_toolbar','theme::_partials.bugsnag','theme::_partials.errors',
'theme::_partials.fb-pixel','theme::_partials.footer','theme::_partials.ga-tracking-script','theme::_partials.header.checkout','theme::_partials.header.minimal-header',
'theme::_partials.header.partials.account_menu','theme::_partials.header.partials.announcement-bar','theme::_partials.header.partials.bottom-nav','theme::_partials.header.partials.flyout-menu','theme::_partials.header.partials.main-menu-item',
'theme::_partials.header.partials.store-menu-item','theme::_partials.header.partials.top-nav','theme::_partials.header.standard','theme::_partials.legal-links','theme::_partials.mobile-nav',
'theme::_partials.old_registration_form','theme::_partials.payment_gateways.stripe.add_card_form','theme::_partials.payment_gateways.stripe.add_card_script','theme::_partials.social-share-buttons','theme::_partials.tolstoy-conversion-script',
'theme::authentication.choose-password','theme::authentication.forgot-password','theme::authentication.forgot-password-confirmation','theme::authentication.login','theme::authentication.register',
'theme::authentication.reset-password','theme::blog._partials.comments.disqus.body','theme::blog._partials.comments.disqus.header','theme::blog._partials.comments.facebook.body','theme::blog._partials.comments.facebook.header',
'theme::blog._partials.grid-post','theme::blog.index','theme::blog.show','theme::cart.checkout-summary','theme::cart.partials.fees--pickup',
'theme::cart.partials.items-closed','theme::cart.partials.items-open','theme::cart.partials.summary','theme::cart.show','theme::cart.unconfirmed',
'theme::checkout._partials.checkout_sms_opt_in','theme::checkout.complete','theme::checkout.gift-card.complete','theme::checkout.preorder.complete','theme::components.blog-post-card',
'theme::components.breadcrumbs','theme::components.cart-item','theme::components.cart-summary','theme::components.customer-reviews','theme::components.delivery-method-closed',
'theme::components.empty-cart','theme::components.form.add-product-button','theme::components.form.add-product-counter-button','theme::components.form.delivery-method-select','theme::components.form.recipe-tag-select',
'theme::components.free-shipping-calculator','theme::components.legacy-modal','theme::components.modal','theme::components.new-customer-modal','theme::components.order-item',
'theme::components.order-summary','theme::components.page-widget','theme::components.page-widgets','theme::components.pages.widgets.banner','theme::components.pages.widgets.cta',
'theme::components.pages.widgets.faq','theme::components.pages.widgets.grid','theme::components.pages.widgets.product','theme::components.pages.widgets.reviews','theme::components.pages.widgets.side-by-side',
'theme::components.product-card','theme::components.product-incentive-item','theme::components.product-score','theme::components.recipe-card','theme::components.subscribe-and-save-cart-toggle',
'theme::components.subscription-item','theme::components.subscription-summary','theme::components.svg.cart','theme::components.unfulfilled-order-item-alert','theme::customers.addresses',
'theme::customers.cards._partials.cards','theme::customers.cards.create','theme::customers.cards.index','theme::customers.gift-certificate','theme::customers.notifications',
'theme::customers.order','theme::customers.orders','theme::customers.partials.address-form','theme::customers.partials.cancel_recurring_order','theme::customers.partials.cancellation_feedback_modal',
'theme::customers.partials.get_it_sooner_modal','theme::customers.partials.layout','theme::customers.partials.navigation','theme::customers.partials.skip_next_order_delivery_modal','theme::customers.password',
'theme::customers.profile','theme::customers.recurring-orders','theme::customers.referrals','theme::customers.subscription','theme::emails.contact',
'theme::emails.daily-report','theme::emails.password-reset','theme::errors.404','theme::gift-cards.index','theme::homepage',
'theme::livewire.add-product-button','theme::livewire.add-product-by-comparison-button','theme::livewire.add-product-counter-button','theme::livewire.add-variant-button','theme::livewire.authentication.login',
'theme::livewire.authentication.registration.email-entry','theme::livewire.authentication.registration.index','theme::livewire.authentication.registration.needs-password','theme::livewire.cart-items','theme::livewire.cart-show',
'theme::livewire.cart-side-panel','theme::livewire.category-product-list','theme::livewire.checkout-offers','theme::livewire.collection-product-list','theme::livewire.customer.addresses-table',
'theme::livewire.customer.credit-cards','theme::livewire.delivery-method-toggle','theme::livewire.modal','theme::livewire.modals.add-address','theme::livewire.modals.confirm-delivery-method',
'theme::livewire.modals.delete-address-confirmation','theme::livewire.modals.edit-address','theme::livewire.modals.modal','theme::livewire.modals.product-quickview','theme::livewire.modals.register-and-add-product',
'theme::livewire.modals.subscription-resume','theme::livewire.offers.subscription','theme::livewire.offers.tripwire','theme::livewire.order-items','theme::livewire.order-show',
'theme::livewire.order-side-panel','theme::livewire.order-status-bar','theme::livewire.placeholders.category-product-list','theme::livewire.placeholders.collection-product-list','theme::livewire.placeholders.purchased-product-list',
'theme::livewire.product-search-command-palette','theme::livewire.purchased-product-list','theme::livewire.side-panel','theme::livewire.storefront-notifications','theme::livewire.subscription-items',
'theme::livewire.subscription-payment-method','theme::livewire.subscription-show','theme::livewire.subscription-side-panel','theme::order.confirmed','theme::order.partials.fees--order',
'theme::order.partials.fees--pickup','theme::order.partials.order-items-closed','theme::order.partials.order-items-open','theme::order.partials.summary','theme::order.show',
'theme::order.unconfirmed','theme::pages.contact','theme::pages.legal.accessibility-statement','theme::pages.legal.privacy-policy','theme::pages.legal.terms-of-service',
'theme::pages.page-preview','theme::pages.page-widget-preview','theme::pages.preview','theme::pages.protocols','theme::pages.show',
'theme::recipes.index','theme::recipes.show','theme::store._layouts.new-no-menu','theme::store._layouts.no-menu','theme::store._partials.add-product',
'theme::store._partials.add_to_cart_form','theme::store._partials.add_to_order_form','theme::store._partials.add_to_subscription_form','theme::store._partials.authenticate','theme::store._partials.legacy-price',
'theme::store._partials.price-gate','theme::store._partials.tags','theme::store.categories.new-show','theme::store.categories.show','theme::store.collections.new-show',
'theme::store.collections.show','theme::store.grid.new-product','theme::store.grid.price','theme::store.grid.product','theme::store.grid.product-list',
'theme::store.index','theme::store.landing','theme::store.new-index','theme::store.products.bundles.show','theme::store.products.default',
'theme::store.products.partials.farming-practices','theme::store.products.partials.farming-practices-beef','theme::store.products.partials.farming-practices-bison','theme::store.products.partials.farming-practices-chicken','theme::store.products.partials.farming-practices-lamb',
'theme::store.products.partials.farming-practices-mixed','theme::store.products.partials.farming-practices-pork','theme::store.products.partials.farming-practices-seafood','theme::store.products.price','theme::store.products.show',
'theme::store.products.standard.show','theme::store.show','theme::store.single.price','theme::store.single.product','theme::style',
'theme::style-variables','theme::subscriptions.show-active','theme::subscriptions.show-inactive','theme::vendor.index','theme::vendor.show',
'theme::widgets.Banner.Banner','theme::widgets.Banner.style','theme::widgets.ContactDetails.ContactDetails','theme::widgets.ContactForm.ContactForm','theme::widgets.ContactForm.style',
'theme::widgets.CtaButton.CtaButton','theme::widgets.CtaButton.style','theme::widgets.Divider.Divider','theme::widgets.Divider.style','theme::widgets.FeaturedPosts.FeaturedPosts',
'theme::widgets.FeaturedPosts.style','theme::widgets.FeaturedProducts.FeaturedProducts','theme::widgets.FeaturedProducts.price','theme::widgets.FeaturedProducts.style','theme::widgets.FeaturedProducts.style1',
'theme::widgets.FeaturedProducts.style2','theme::widgets.FeaturedProducts.style3','theme::widgets.FeaturedProducts.style4','theme::widgets.FeaturedRecipes.FeaturedRecipes','theme::widgets.FeaturedRecipes.style',
'theme::widgets.HTML.HTML','theme::widgets.HTML.style','theme::widgets.HowItWorks.HowItWorks','theme::widgets.HowItWorks.style','theme::widgets.LinkList.LinkList',
'theme::widgets.Newsletter.Newsletter','theme::widgets.Newsletter.style','theme::widgets.PhotoGrid.PhotoGrid','theme::widgets.PhotoGrid.style','theme::widgets.ProductProtocols.ProductProtocols',
'theme::widgets.ProductProtocols.style','theme::widgets.RichText.RichText','theme::widgets.RichText.style','theme::widgets.SocialNetworks.SocialNetworks','theme::widgets.Spacer.Spacer',
'theme::widgets.Team.Team','theme::widgets.Team.style','theme::widgets.TextHeader.TextHeader','theme::widgets.TextHeader.style','theme::widgets.Vendors.Vendors',
'theme::widgets.Vendors.style','theme::widgets.image_widget.image_widget','theme::widgets.image_widget.style','theme::widgets.image_with_text.image_with_text','theme::widgets.image_with_text.style',
'theme::widgets.photo_gallery.photo_gallery','theme::widgets.photo_gallery.style','theme::widgets.testimonials.style','theme::widgets.testimonials.testimonials','themes.edit',
'themes.index','themes.layout','tos','trial-expired','under-maintenance',
'users.edit','users.index','users.partials.addresses','users.partials.billing','users.partials.billing.gravity',
'users.partials.billing.stripe','users.partials.create-order-modal','users.partials.create-user-modal','users.partials.credit','users.partials.credit_card',
'users.partials.delete-user-modal','users.partials.edit-promo-item-modal','users.partials.filter-form','users.partials.index-table','users.partials.name',
'users.partials.notes','users.partials.orders','users.partials.profile','users.partials.referrals','users.partials.reset-password-modal',
'users.partials.settings','users.partials.subscription','vendor.cashier.receipt','vendor.flash.message','vendor.flash.modal',
'vendor.mail.html.button','vendor.mail.html.footer','vendor.mail.html.header','vendor.mail.html.layout','vendor.mail.html.message',
'vendor.mail.html.panel','vendor.mail.html.promotion','vendor.mail.html.promotion.button','vendor.mail.html.subcopy','vendor.mail.html.table',
'vendor.mail.text.button','vendor.mail.text.footer','vendor.mail.text.header','vendor.mail.text.layout','vendor.mail.text.message',
'vendor.mail.text.panel','vendor.mail.text.promotion','vendor.mail.text.promotion.button','vendor.mail.text.subcopy','vendor.mail.text.table',
'vendor.notifications.email','vendor.nova.partials.footer','vendor.nova.partials.logo','vendor.nova.partials.meta','vendor.nova.partials.user',
'vendor.pagination.bootstrap-4','vendor.pagination.bootstrap-5','vendor.pagination.default','vendor.pagination.semantic-ui','vendor.pagination.simple-bootstrap-4',
'vendor.pagination.simple-bootstrap-5','vendor.pagination.simple-default','vendor.pagination.simple-tailwind','vendor.pagination.tailwind','vendors.edit',
'vendors.index','vendors.partials.create-vendor-modal','vendors.partials.delete-vendor-modal','vendors.partials.hide-vendor-modal','vendors.partials.index-table',
'widgets.banner','widgets.featured-products','widgets.how-it-works','widgets.text','cookie-consent::dialogContents',
'cookie-consent::index','horizon::layout','laravel-exceptions-renderer::components.card','laravel-exceptions-renderer::components.context','laravel-exceptions-renderer::components.editor',
'laravel-exceptions-renderer::components.header','laravel-exceptions-renderer::components.icons.chevron-down','laravel-exceptions-renderer::components.icons.chevron-up','laravel-exceptions-renderer::components.icons.computer-desktop','laravel-exceptions-renderer::components.icons.moon',
'laravel-exceptions-renderer::components.icons.sun','laravel-exceptions-renderer::components.layout','laravel-exceptions-renderer::components.navigation','laravel-exceptions-renderer::components.theme-switcher','laravel-exceptions-renderer::components.trace',
'laravel-exceptions-renderer::components.trace-and-editor','laravel-exceptions-renderer::show','laravel-exceptions::401','laravel-exceptions::402','laravel-exceptions::403',
'laravel-exceptions::404','laravel-exceptions::419','laravel-exceptions::429','laravel-exceptions::500','laravel-exceptions::503',
'laravel-exceptions::layout','laravel-exceptions::minimal','livewire::bootstrap','livewire::simple-bootstrap','livewire::simple-tailwind',
'livewire::tailwind','notifications::email','pagination::bootstrap-4','pagination::bootstrap-5','pagination::default',
'pagination::semantic-ui','pagination::simple-bootstrap-4','pagination::simple-bootstrap-5','pagination::simple-default','pagination::simple-tailwind',
'pagination::tailwind','telescope::layout',);
        registerArgumentsSet('translations', 
'auth.failed','auth.password','auth.throttle','pagination.previous','pagination.next',
'passwords.reset','passwords.sent','passwords.throttled','passwords.token','passwords.user',
'passwords.password','validation.accepted','validation.accepted_if','validation.active_url','validation.after',
'validation.after_or_equal','validation.alpha','validation.alpha_dash','validation.alpha_num','validation.any_of',
'validation.array','validation.ascii','validation.before','validation.before_or_equal','validation.between.array',
'validation.between.file','validation.between.numeric','validation.between.string','validation.boolean','validation.can',
'validation.confirmed','validation.contains','validation.current_password','validation.date','validation.date_equals',
'validation.date_format','validation.decimal','validation.declined','validation.declined_if','validation.different',
'validation.digits','validation.digits_between','validation.dimensions','validation.distinct','validation.doesnt_end_with',
'validation.doesnt_start_with','validation.email','validation.ends_with','validation.enum','validation.exists',
'validation.extensions','validation.file','validation.filled','validation.gt.array','validation.gt.file',
'validation.gt.numeric','validation.gt.string','validation.gte.array','validation.gte.file','validation.gte.numeric',
'validation.gte.string','validation.hex_color','validation.image','validation.in','validation.in_array',
'validation.integer','validation.ip','validation.ipv4','validation.ipv6','validation.json',
'validation.list','validation.lowercase','validation.lt.array','validation.lt.file','validation.lt.numeric',
'validation.lt.string','validation.lte.array','validation.lte.file','validation.lte.numeric','validation.lte.string',
'validation.mac_address','validation.max.array','validation.max.file','validation.max.numeric','validation.max.string',
'validation.max_digits','validation.mimes','validation.mimetypes','validation.min.array','validation.min.file',
'validation.min.numeric','validation.min.string','validation.min_digits','validation.missing','validation.missing_if',
'validation.missing_unless','validation.missing_with','validation.missing_with_all','validation.multiple_of','validation.not_in',
'validation.not_regex','validation.numeric','validation.password.letters','validation.password.mixed','validation.password.numbers',
'validation.password.symbols','validation.password.uncompromised','validation.present','validation.present_if','validation.present_unless',
'validation.present_with','validation.present_with_all','validation.prohibited','validation.prohibited_if','validation.prohibited_if_accepted',
'validation.prohibited_if_declined','validation.prohibited_unless','validation.prohibits','validation.regex','validation.required',
'validation.required_array_keys','validation.required_if','validation.required_if_accepted','validation.required_if_declined','validation.required_unless',
'validation.required_with','validation.required_with_all','validation.required_without','validation.required_without_all','validation.same',
'validation.size.array','validation.size.file','validation.size.numeric','validation.size.string','validation.starts_with',
'validation.string','validation.timezone','validation.unique','validation.uploaded','validation.uppercase',
'validation.url','validation.ulid','validation.uuid','validation.custom.attribute-name.rule-name','validation.custom.contact_website.url',
'validation.custom.website.url','validation.custom.indisposable','validation.attributes.unit_price','validation.attributes.sale_unit_price','messages.free',
'messages.registration_zipcode_label','messages.edit_profile','messages.redeem_gift_card','messages.order_history','messages.referrals',
'messages.create_account','messages.sign_in','messages.sign_out','messages.change_password','messages.edit_address',
'messages.manage_address','messages.manage_subscription','messages.credit_cards','messages.your_account','messages.default_location',
'messages.email_sub_message','messages.alt_email_sub_message','messages.stripe_security_message','messages.referral_instructions','messages.referral_clicks',
'messages.referral_referrals','messages.referral_rewards','messages.referral_incentive','messages.update_delivery_preference','messages.store_access_availability',
'messages.blog_description','messages.my_savings','messages.my_subscription_savings','messages.change_fulfillment_cta','messages.location_not_found',
'messages.notifications.sms_marketing','messages.notifications.newsletter','messages.notifications.reminders','messages.password.change','messages.password.current',
'messages.password.new','messages.password.confirm','messages.checkout.payment_heading','messages.checkout.free_item_heading','messages.checkout.promotional_item_heading',
'messages.checkout.cta_button_text','messages.checkout.address_error','messages.cart.your_cart','messages.cart.your_order','messages.cart.your_subscription',
'messages.cart.checkout_cta','messages.cart.checkout_cta_short','messages.cart.reschedule','messages.cart.keep_shopping','messages.cart.add_to_cart',
'messages.cart.add_to_order','messages.cart.add_to_subscription','messages.recurring.heading','messages.recurring.your_next_shipment','messages.recurring.order_status_bar',
'messages.recurring.get_it_sooner','messages.recurring.skip','messages.recurring.reorder_frequencies.7','messages.recurring.reorder_frequencies.14','messages.recurring.reorder_frequencies.28',
'messages.recurring.reorder_frequencies.42','messages.recurring.reorder_frequencies.56','messages.recurring.reorder_frequencies_formatted.7','messages.recurring.reorder_frequencies_formatted.14','messages.recurring.reorder_frequencies_formatted.28',
'messages.recurring.reorder_frequencies_formatted.42','messages.recurring.reorder_frequencies_formatted.56','messages.uom.pounds','messages.uom.kg','messages.custom_messages.locations_page_title',
'messages.custom_messages.locations_page_description','messages.custom_messages.order_summary','messages.custom_messages.checkout_heading','messages.custom_messages.checkout_subheading','messages.custom_messages.checkout_step1',
'messages.custom_messages.checkout_step2','messages.custom_messages.checkout_step3','messages.custom_messages.checkout_step4','messages.custom_messages.checkout_pickup_id','messages.custom_messages.checkout_pickup_date',
'messages.custom_messages.store_sidebar_collection_heading','messages.custom_messages.footer_message','messages.custom_messages.proposal_heading','messages.custom_messages.proposal_instructions','messages.custom_messages.location_next_date_heading',
'messages.custom_messages.location_times_heading','messages.custom_messages.schedule_table_heading_1','messages.custom_messages.schedule_table_heading_2','messages.custom_messages.schedule_table_heading_3','messages.custom_messages.fees_heading',
'messages.custom_messages.delivery_fee_title','messages.custom_messages.checkout_cta_button_text','messages.custom_messages.checkout_confirmation','messages.custom_messages.checkout_label_state','messages.custom_messages.checkout_label_zip',
'messages.custom_messages.delivery_default_title','messages.custom_messages.account_delivery_preference_title','messages.custom_messages.protocols_page_header','messages.custom_messages.referral_instructions','messages.custom_messages.order_date_tba',
'messages.custom_messages.checkout_agreement_intro','messages.custom_messages.checkout_agreement_heading','messages.custom_messages.checkout_agreement_confirmation','messages.custom_messages.newsletter_opt_in_message','messages.custom_messages.login_message',
'messages.custom_messages.registration_message','messages.custom_messages.order_status_bar_tba_unconfirmed','messages.custom_messages.order_status_bar_tba_confirmed','messages.custom_messages.account_status_page','messages.custom_messages.subscriptions_checkout_message',
'messages.custom_messages.subscribe_and_save','messages.custom_messages.pickup_no_dates','messages.custom_messages.reschedule_order','messages.custom_messages.subscriptions_confirmation_message','messages.custom_messages.subscriptions_promo_message',
'es.0','messages.send_me_your_newsletter','en.0','cookie-consent::texts.message','cookie-consent::texts.agree',);
        registerArgumentsSet('env', 
'APP_NAME','APP_ENV','APP_KEY','APP_DEBUG','APP_LOCALE',
'ROOT_DOMAIN','ACCOUNT_DOMAIN','API_DOMAIN','APP_URL','VITE_APP_NAME',
'APP_LOCALE','APP_FALLBACK_LOCALE','APP_FAKER_LOCALE','APP_MAINTENANCE_DRIVER','APP_MAINTENANCE_STORE',
'BCRYPT_ROUNDS','LOG_CHANNEL','LOG_SLACK_WEBHOOK_URL','LOG_STACK','LOG_DEPRECATIONS_CHANNEL',
'LOG_LEVEL','LOG_DAILY_DAYS','DB_CONNECTION','DB_HOST','DB_DATABASE',
'DB_USERNAME','DB_PASSWORD','DB_PORT','SESSION_DRIVER','SESSION_LIFETIME',
'SESSION_ENCRYPT','SESSION_PATH','SESSION_DOMAIN','SESSION_CONNECTION','HORIZON_PREFIX',
'HORIZON_SECRET','BROADCAST_CONNECTION','FILESYSTEM_DISK','CACHE_STORE','CACHE_PREFIX',
'MEMCACHED_HOST','QUEUE_DRIVER','QUEUE_CONNECTION','REDIS_PORT','MAIL_MAILER',
'MAIL_HOST','MAIL_PORT','MAIL_USERNAME','MAIL_PASSWORD','MAIL_FROM_ADDRESS',
'MAIL_FROM_NAME','MAIL_MARKETING_FROM_ADDRESS','MAILGUN_DOMAIN','MAILGUN_MARKETING_DOMAIN','MAILGUN_SECRET',
'MAILGUN_TESTING_ENDPOINT','S3_KEY','S3_SECRET','S3_BUCKET','S3_ROOT',
'VITE_S3_BUCKET','STRIPE_SECRET','STRIPE_KEY','STRIPE_WEBHOOK_SECRET','STRIPE_CLIENT_ID',
'STRIPE_APPLICATION_FEE','STRIPE_MANAGED','STRIPE_DEBUG','STRIPE_DEFAULT_PLAN_ID','STRIPE_COURTESY_PRICE_ID',
'STRIPE_COURTESY_PRODUCT_ID','STRIPE_ORDER_USAGE_PRICE_ID','STRIPE_ORDER_USAGE_PRODUCT_ID','STRIPE_ESSENTIALS_PRICE_ID','STRIPE_ESSENTIALS_PRODUCT_ID',
'STRIPE_PRO_PRICE_ID','STRIPE_PRO_PRODUCT_ID','STRIPE_BUSINESS_PRICE_ID','STRIPE_BUSINESS_PRODUCT_ID','STRIPE_ENTERPRISE_PRICE_ID',
'STRIPE_ENTERPRISE_PRODUCT_ID','STRIPE_DATA_HOLDING_PRICE_ID','STRIPE_DATA_HOLDING_PRODUCT_ID','STRIPE_DELIVERY_MANAGER_PRODUCT_ID','STRIPE_DELIVERY_MANAGER_MAIN_PRICE_ID',
'STRIPE_DELIVERY_MANAGER_ORDERS_PRICE_ID','STRIPE_PACKING_MANAGER_PRODUCT_ID','STRIPE_PACKING_MANAGER_MAIN_PRICE_ID','STRIPE_PACKING_MANAGER_ORDERS_PRICE_ID','STRIPE_LEAD_CONVERTER_PRODUCT_ID',
'STRIPE_LEAD_CONVERTER_MAIN_PRICE_ID','STRIPE_LEAD_CONVERTER_ORDERS_PRICE_ID','STRIPE_PRODUCT_UPSELLER_PRODUCT_ID','STRIPE_PRODUCT_UPSELLER_MAIN_PRICE_ID','STRIPE_PRODUCT_UPSELLER_ORDERS_PRICE_ID',
'STRIPE_SUBSCRIPTIONS_PRODUCT_ID','STRIPE_SUBSCRIPTIONS_COURTESY_PRICE_ID','STRIPE_SUBSCRIPTIONS_ESSENTIALS_PRICE_ID','STRIPE_SUBSCRIPTIONS_PRO_PRICE_ID','STRIPE_SUBSCRIPTIONS_ENTERPRISE_PRICE_ID',
'STRIPE_POS_PRODUCT_ID','STRIPE_POS_MAIN_PRICE_ID','STRIPE_POS_COURTESY_PRICE_ID','BUGSNAG_API_KEY','VITE_BUGSNAG_VUE_API_KEY',
'SLACK_WEB_HOOK','SLACK_DEFAULT_ROOM','GEOCODIO_KEY','GEOCODERCA_KEY','GOOGLE_API_KEY',
'GOOGLE_MAPS_API_KEY','GOOGLE_GEOCODER_API_KEY','GOOGLE_PLACES_JS_API_KEY','GOOGLE_SHEETS_API_KEY','INVENTORY_SPREADSHEET_ID',
'INVENTORY_WORKSHEET_NAME','DRIP_API_KEY','DRIP_ACCOUNT_ID','DEBUGBAR_ENABLED','TELESCOPE_ENABLED',
'GRAVITY_ASSET_URL','GRAVITY_PAYMENTS_ENVIRONMENT_URL','GRAVITY_PAYMENTS_OID','GRAVITY_PAYMENTS_AUTH_TOKEN','TRIBE_PLAN_ID',
'FULL_ACCESS_DOMAINS','BETA_ACCESS_DOMAINS','TWILIO_AUTH_TOKEN','TWILIO_ACCOUNT_SID','TWILIO_FROM',
'CANNY_APP_ID','NOVA_LICENSE_KEY','SEGMENT_WRITE_KEY','STAX_PARTNER_API_KEY','PAY_FAC_API_KEY',
'PAY_FAC_COMPANY_ID','PAY_FAC_JS_CLIENT_KEY','NOTIFICATIONS_TENANT_REGISTRATION','SCOUT_DRIVER','SCOUT_PREFIX',
'SCOUT_QUEUE','MEILISEARCH_HOST','MEILISEARCH_KEY','TOLSTOY_APP_KEY','FILE_UPLOAD_PREFIX',
'SENTRY_LARAVEL_DSN','SENTRY_TRACES_SAMPLE_RATE','HOMEPAGE_FEATURED_BUNDLE_IDS','CLOUDFRONT_DISTRIBUTION_URL','VITE_CLOUDFRONT_DISTRIBUTION_URL',
'NODE_ENV','ORDER_CREDIT_REPORT_RECIPIENTS','NEW_STOREFRONT_ENABLED','META_ACCESS_TOKEN','META_TEST_EVENT_CODE',);
        
                expectedArguments(\Illuminate\Support\Facades\Gate::has(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Gate::allows(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Gate::denies(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Gate::check(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Gate::any(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Gate::none(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Gate::authorize(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Gate::inspect(), 0, argumentsSet('auth'));
                expectedArguments(\Illuminate\Support\Facades\Route::can(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Route::cannot(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Route::cant(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Auth::can(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Auth::cannot(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Auth::cant(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Foundation\Auth\Access\Authorizable::can(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Foundation\Auth\Access\Authorizable::cannot(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Foundation\Auth\Access\Authorizable::cant(), 0, argumentsSet('auth'));
                expectedArguments(\Illuminate\Contracts\Auth\Access\Authorizable::can(), 0, argumentsSet('auth'));
                expectedArguments(\Illuminate\Config\Repository::getMany(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::set(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::string(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::integer(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::boolean(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::float(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::array(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::prepend(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::push(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::getMany(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::set(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::string(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::integer(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::boolean(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::float(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::array(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::prepend(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::push(), 0, argumentsSet('configs'));
                expectedArguments(\Illuminate\Support\Facades\Route::middleware(), 0, argumentsSet('middleware'));
    expectedArguments(\Illuminate\Support\Facades\Route::withoutMiddleware(), 0, argumentsSet('middleware'));
    expectedArguments(\Illuminate\Routing\Router::middleware(), 0, argumentsSet('middleware'));
    expectedArguments(\Illuminate\Routing\Router::withoutMiddleware(), 0, argumentsSet('middleware'));
                expectedArguments(\route(), 0, argumentsSet('routes'));
    expectedArguments(\to_route(), 0, argumentsSet('routes'));
    expectedArguments(\signedRoute(), 0, argumentsSet('routes'));
                expectedArguments(\Illuminate\Support\Facades\Redirect::route(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Support\Facades\Redirect::signedRoute(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Support\Facades\Redirect::temporarySignedRoute(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Support\Facades\URL::route(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Support\Facades\URL::signedRoute(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Support\Facades\URL::temporarySignedRoute(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Routing\Redirector::route(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Routing\Redirector::signedRoute(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Routing\Redirector::temporarySignedRoute(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Routing\UrlGenerator::route(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Routing\UrlGenerator::signedRoute(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Routing\UrlGenerator::temporarySignedRoute(), 0, argumentsSet('routes'));
                expectedArguments(\view(), 0, argumentsSet('views'));
                expectedArguments(\Illuminate\Support\Facades\View::make(), 0, argumentsSet('views'));
    expectedArguments(\Illuminate\View\Factory::make(), 0, argumentsSet('views'));
                expectedArguments(\__(), 0, argumentsSet('translations'));
    expectedArguments(\trans(), 0, argumentsSet('translations'));
                expectedArguments(\Illuminate\Contracts\Translation\Translator::get(), 0, argumentsSet('translations'));
                expectedArguments(\env(), 0, argumentsSet('env'));
                expectedArguments(\Illuminate\Support\Env::get(), 0, argumentsSet('env'));
            
}
