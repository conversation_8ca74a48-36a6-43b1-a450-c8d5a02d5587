<?php

namespace Tests\Feature\Theme\Store;

use App\Models\Category;
use App\Models\Product;
use App\Support\Enums\ProductType;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CategoryTest extends TenantTestCase
{
    #[Test]
    public function it_cannot_navigates_to_an_invalid_category_page(): void
    {
        $this->get(route('store.categories.show', 'abcbcbabcbaba'))
            ->assertRedirect(route('store.index'));
    }

    #[Test]
    public function it_navigates_to_a_valid_category_page(): void
    {
        $category = Category::factory()->create();

        $view = config('grazecart.new_storefront_enabled')
            ? 'theme::store.categories.new-show'
            : 'theme::store.categories.show';

        $this->get(route('store.categories.show', $category->slug))
            ->assertOk()
            ->assertViewIs($view);
    }

    #[Test]
    public function it_renders_the_category_details(): void
    {
        $category = Category::factory()->create([
            'name' => 'cat name',
            'description' => 'cat description',
            'extra_attributes' => [
                'display_name' => 'cat display name',
                'summary' => 'cat summary',
                'cover_photo' => 'https://test.com',
                'seo_meta_title' => 'cat seo meta title',
                'seo_meta_description' => 'cat seo meta description',
            ]
        ]);

        $this->get(route('store.categories.show', $category->slug))
            ->assertOk()
            ->assertSee($category->extra_attributes->display_name)
            ->assertSee($category->extra_attributes->summary)
            ->assertSee($category->extra_attributes->seo_meta_title)
            ->assertSee($category->extra_attributes->seo_meta_description)
            ->assertSee($category->description);
    }

    #[Test]
    public function it_renders_the_category_products_that_are_public(): void
    {
        $category = Category::factory()->create();

        $product_one = Product::factory()->create([
            'category_id' => $category->id,
            'visible' => false,
            'hide_from_search' => false
        ]);

        $product_two = Product::factory()->create([
            'category_id' => $category->id,
            'visible' => true,
            'hide_from_search' => true
        ]);

        $product_three = Product::factory()->create([
            'category_id' => $category->id,
            'visible' => true,
            'hide_from_search' => false
        ]);

        $this->get(route('store.categories.show', $category->slug))
            ->assertOk()
            ->assertDontSee($product_one->title)
            ->assertSee($product_two->title)
            ->assertSee($product_three->title);
    }

    #[Test]
    public function it_does_not_render_pre_order_category_products(): void
    {
        $category = Category::factory()->create();

        $product_one = Product::factory()->create([
            'category_id' => $category->id,
            'type_id' => ProductType::STANDARD->value
        ]);

        $product_two = Product::factory()->create([
            'category_id' => $category->id,
            'type_id' => ProductType::PREORDER->value
        ]);

        $this->get(route('store.categories.show', $category->slug))
            ->assertOk()
            ->assertSee($product_one->title)
            ->assertDontSee($product_two->title);
    }

    #[Test]
    public function it_renders_the_category_products_that_are_hidden_from_search(): void
    {
        $category = Category::factory()->create();

        $product_one = Product::factory()->create([
            'category_id' => $category->id,
            'hide_from_search' => false
        ]);

        $product_two = Product::factory()->create([
            'category_id' => $category->id,
            'hide_from_search' => true
        ]);

        $product_three = Product::factory()->create([
            'category_id' => $category->id,
        ]);

        $this->get(route('store.categories.show', $category->slug))
            ->assertOk()
            ->assertSee($product_one->title)
            ->assertSee($product_two->title)
            ->assertSee($product_three->title);
    }

    #[Test]
    public function it_only_renders_the_subcategory_products_when_viewing_a_subcategory(): void
    {
        $category = Category::factory()->create();
        $subcategory_one = Category::factory()->create(['category_id' => $category->id]);
        $subcategory_two = Category::factory()->create();

        $product_one = Product::factory()->create([
            'category_id' => $category->id,
            'visible' => true,
            'hide_from_search' => false
        ]);

        $product_two = Product::factory()->create([
            'category_id' => $subcategory_one->id,
            'visible' => true,
            'hide_from_search' => false
        ]);

        $product_three = Product::factory()->create([
            'category_id' => $subcategory_two->id,
            'visible' => true,
            'hide_from_search' => false
        ]);

        $this->get(route('store.subcategories.show', [$category->slug, $subcategory_one->slug]))
            ->assertOk()
            ->assertDontSee($product_one->title)
            ->assertSee($product_two->title)
            ->assertDontSee($product_three->title);
    }
}
