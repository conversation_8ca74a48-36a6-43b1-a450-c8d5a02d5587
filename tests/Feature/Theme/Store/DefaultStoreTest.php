<?php

namespace Tests\Feature\Theme\Store;

use App\Models\Product;
use App\Models\Setting;
use Illuminate\Support\Collection;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DefaultStoreTest extends TenantTestCase
{
    public function setup(): void
    {
        parent::setup();

        // Disable the ZipGate feature.
        Setting::updateOrCreate(['key' => 'require_shopper_local'],['value' => false]);

        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => null]);

        Product::query()->delete();
    }

    #[Test]
    public function it_can_load_the_configured_store_page_title(): void
    {
        Setting::updateOrCreate(['key' => 'require_shopper_local'],['value' => false]);

        Setting::updateOrCreate(['key' => 'store_page_title'], ['value' => 'Custom Title']);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('pageTitle', 'Custom Title');
    }

    #[Test]
    public function it_loads_store_index_page_variables(): void
    {
        Product::factory()->create();

        $view = config('grazecart.new_storefront_enabled')
            ? 'theme::store.new-index'
            : 'theme::store.index';

        $this->get(route('store.index'))
            ->assertOk()
            ->assertSessionHas('store_url')
            ->assertViewIs($view)
            ->assertViewHas('pageTitle', 'Shop Now')
            ->assertViewHas('heading', fn($value) => is_null($value))
            ->assertViewHas('subheading', fn($value) => is_null($value))
            ->assertViewHas('pageDescription', fn($value) => is_null($value))
            ->assertViewHas('headerPhoto', fn($value) => is_null($value))
            ->assertViewHas('tags', fn($value) => $value instanceof Collection)
            ->assertViewHas('pageCanonical', fn($value) => is_null($value));
    }
}
