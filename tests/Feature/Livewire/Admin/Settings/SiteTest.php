<?php

namespace Tests\Feature\Livewire\Admin\Settings;

use App\Livewire\Admin\Settings\Site;
use App\Models\Setting;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SiteTest extends TenantTestCase
{
    #[Test]
    public function it_renders_successfully(): void
    {
        Setting::updateOrCreate(['key' => 'homepage_promo'], ['value' => json_encode([
            'image' => 'https://example.com/promo.jpg',
            'cta' => [
                'label' => 'Test CTA',
                'url' => '/test-url',
            ],
        ])]);

        Livewire::test(Site::class)
            ->assertStatus(200)
            ->assertSet('homepage_promo_image', 'https://example.com/promo.jpg')
            ->assertSet('homepage_promo_cta_label', 'Test CTA')
            ->assertSet('homepage_promo_cta_url', '/test-url');
    }

    #[Test]
    public function it_can_set_the_promo_image(): void
    {
        Setting::updateOrCreate(['key' => 'homepage_promo'], ['value' => json_encode([
            'image' => 'https://example.com/promo.jpg',
            'cta' => [
                'label' => 'Test CTA',
                'url' => '/test-url',
            ],
        ])]);

        Livewire::test(Site::class)
            ->assertStatus(200)
            ->assertSet('homepage_promo_image', 'https://example.com/promo.jpg')
            ->call('setPromoImage', 'https://example.com/new-promo.jpg')
            ->assertSet('homepage_promo_image', 'https://example.com/new-promo.jpg');
    }

    #[Test]
    public function it_can_remove_the_promo_image(): void
    {
        Setting::updateOrCreate(['key' => 'homepage_promo'], ['value' => json_encode([
            'image' => 'https://example.com/promo.jpg',
            'cta' => [
                'label' => 'Test CTA',
                'url' => '/test-url',
            ],
        ])]);

        Livewire::test(Site::class)
            ->assertStatus(200)
            ->assertSet('homepage_promo_image', 'https://example.com/promo.jpg')
            ->call('removePromoImage')
            ->assertSet('homepage_promo_image', null);
    }

    #[Test]
    public function it_can_update_the_widget_settings(): void
    {
        Setting::updateOrCreate(['key' => 'homepage_promo'], ['value' => json_encode([
            'image' => 'https://example.com/promo.jpg',
            'cta' => [
                'label' => 'Test CTA',
                'url' => '/test-url',
            ],
        ])]);

        Livewire::test(Site::class)
            ->assertStatus(200)
            ->set('homepage_promo_image', 'https://example.com/new-promo.jpg')
            ->set('homepage_promo_cta_label', 'New CTA')
            ->set('homepage_promo_cta_url', '/new-url')
            ->call('save')
            ->assertHasNoErrors()
            ->assertSet('homepage_promo_image', 'https://example.com/new-promo.jpg')
            ->assertSet('homepage_promo_cta_label', 'New CTA')
            ->assertSet('homepage_promo_cta_url', '/new-url');

        $this->assertDatabaseHas(Setting::class, [
            'key' => 'homepage_promo',
            'value' => json_encode([
                'image' => 'https://example.com/new-promo.jpg',
                'cta' => [
                    'label' => 'New CTA',
                    'url' => '/new-url',
                ]
            ])
        ]);
    }

    #[Test]
    public function it_dispatches_an_event_on_successful_save(): void
    {
        Setting::updateOrCreate(['key' => 'homepage_promo'], ['value' => json_encode([
            'image' => 'https://example.com/promo.jpg',
            'cta' => [
                'label' => 'Test CTA',
                'url' => '/test-url',
            ],
        ])]);

        Livewire::test(Site::class)
            ->assertStatus(200)
            ->call('save')
            ->assertHasNoErrors()
            ->assertDispatched('admin-notification-sent', notification: [
                'level' => 'success',
                'title' => 'Site updated!',
                'message' => 'The site has been successfully updated.',
                'duration' => 3000
            ]);
    }
}
