<?php

namespace Tests\Feature\Livewire\Admin\Users;

use App\Actions\Billing\RemoveCard;
use App\Actions\Billing\SetDefaultCard;
use App\Billing\Gateway\PaymentMethod;
use App\Contracts\Billing;
use App\Livewire\Admin\Users\CreditCards;
use App\Models\Card;
use App\Models\User;
use Livewire\Livewire;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class CreditCardsTest extends LivewireTenantTestCase
{
    #[Test]
    public function it_can_render(): void
    {
        $user = User::factory()->create();

        $card_one = Card::factory()->create([
            'user_id' => $user->id,
            'source_id' => 'pm_123',
            'default' => false,
        ]);

        $card_two = Card::factory()->create([
            'user_id' => $user->id,
            'source_id' => 'pm_456',
            'default' => true,
        ]);

        $payment_method_one = new PaymentMethod(
            id: 'pm_123',
            customer_id: 'cus_123',
            customer_name: '<PERSON>',
            exp_month: '12',
            exp_year: '2022',
            brand: 'visa',
            last_four: '4242',
        );

        $payment_method_two = new PaymentMethod(
            id: 'pm_456',
            customer_id: 'cus_123',
            customer_name: 'John Doe',
            exp_month: '12',
            exp_year: '2028',
            brand: 'mastercard',
            last_four: '4444',
        );

        $expected_payment_methods = collect([$payment_method_one, $payment_method_two]);

        $this->mock(Billing::class, function (MockInterface $mock) use ($expected_payment_methods) {
            $mock->shouldReceive('retrieveUserPaymentMethods')->andReturn($expected_payment_methods);
        });

        Livewire::test(CreditCards::class, compact('user'))
            ->assertStatus(200)
            ->assertSet('default_source_id', $card_two->source_id)
            ->assertViewIs('livewire.users.credit-cards')
            ->assertViewHas('cards', $expected_payment_methods);
    }

    #[Test]
    public function it_can_a_source_as_a_default(): void
    {
        $user = User::factory()->create();

        $card_one = Card::factory()->create([
            'user_id' => $user->id,
            'source_id' => 'pm_123',
            'default' => false,
        ]);

        $card_two = Card::factory()->create([
            'user_id' => $user->id,
            'source_id' => 'pm_456',
            'default' => true,
        ]);

        $payment_method_one = new PaymentMethod(
            id: 'pm_123',
            customer_id: 'cus_123',
            customer_name: 'John Doe',
            exp_month: '12',
            exp_year: '2022',
            brand: 'visa',
            last_four: '4242',
        );

        $payment_method_two = new PaymentMethod(
            id: 'pm_456',
            customer_id: 'cus_123',
            customer_name: 'John Doe',
            exp_month: '12',
            exp_year: '2028',
            brand: 'mastercard',
            last_four: '4444',
        );

        $expected_payment_methods = collect([$payment_method_one, $payment_method_two]);

        $this->mock(Billing::class, function (MockInterface $mock) use ($expected_payment_methods) {
            $mock->shouldReceive('retrieveUserPaymentMethods')->andReturn($expected_payment_methods);
        });

        $this->mock(SetDefaultCard::class, function (MockInterface $mock) use ($card_one) {
            $mock->shouldReceive('handle')
                ->with(
                    \Mockery::on(fn (Card $card) => $card->id === $card_one->id)
                );
        });

        Livewire::test(CreditCards::class, compact('user'))
            ->assertStatus(200)
            ->assertSet('default_source_id', $card_two->source_id)
            ->call('setAsDefault', $card_one->source_id)
            ->assertSet('default_source_id', $card_one->source_id)
            ->assertDispatched('admin-notification-sent', notification: [
                'level' => 'success',
                'title' => 'Success!',
                'message' => 'The default card has been updated!',
                'duration' => 3000
            ]);
    }

    #[Test]
    public function it_sends_a_failure_notification_when_setting_default_fails(): void
    {
        $user = User::factory()->create();

        $card_one = Card::factory()->create([
            'user_id' => $user->id,
            'source_id' => 'pm_123',
            'default' => false,
        ]);

        $card_two = Card::factory()->create([
            'user_id' => $user->id,
            'source_id' => 'pm_456',
            'default' => true,
        ]);

        $payment_method_one = new PaymentMethod(
            id: 'pm_123',
            customer_id: 'cus_123',
            customer_name: 'John Doe',
            exp_month: '12',
            exp_year: '2022',
            brand: 'visa',
            last_four: '4242',
        );

        $payment_method_two = new PaymentMethod(
            id: 'pm_456',
            customer_id: 'cus_123',
            customer_name: 'John Doe',
            exp_month: '12',
            exp_year: '2028',
            brand: 'mastercard',
            last_four: '4444',
        );

        $expected_payment_methods = collect([$payment_method_one, $payment_method_two]);

        $this->mock(Billing::class, function (MockInterface $mock) use ($expected_payment_methods) {
            $mock->shouldReceive('retrieveUserPaymentMethods')->andReturn($expected_payment_methods);
        });

        $this->mock(SetDefaultCard::class, function (MockInterface $mock) use ($card_one) {
            $mock->shouldReceive('handle')
                ->with(
                    \Mockery::on(fn (Card $card) => $card->id === $card_one->id)
                )
                ->andThrow(new \Exception('Error setting default card'));
        });

        Livewire::test(CreditCards::class, compact('user'))
            ->assertStatus(200)
            ->assertSet('default_source_id', $card_two->source_id)
            ->call('setAsDefault', $card_one->source_id)
            ->assertSet('default_source_id', $card_two->source_id)
            ->assertDispatched('admin-notification-sent', notification: [
                'level' => 'error',
                'title' => 'Error!',
                'message' => 'The default card could not be updated.',
                'duration' => 3000
            ]);
    }

    #[Test]
    public function it_can_remove_a_card(): void
    {
        $user = User::factory()->create(['customer_id' => 'abc_123']);

        $card_one = Card::factory()->create([
            'user_id' => $user->id,
            'source_id' => 'pm_123',
            'default' => false,
        ]);

        $card_two = Card::factory()->create([
            'user_id' => $user->id,
            'source_id' => 'pm_456',
            'default' => true,
        ]);

        $payment_method_one = new PaymentMethod(
            id: 'pm_123',
            customer_id: 'cus_123',
            customer_name: 'John Doe',
            exp_month: '12',
            exp_year: '2022',
            brand: 'visa',
            last_four: '4242',
        );

        $payment_method_two = new PaymentMethod(
            id: 'pm_456',
            customer_id: 'cus_123',
            customer_name: 'John Doe',
            exp_month: '12',
            exp_year: '2028',
            brand: 'mastercard',
            last_four: '4444',
        );

        $expected_payment_methods = collect([$payment_method_one, $payment_method_two]);

        $this->mock(Billing::class, function (MockInterface $mock) use ($expected_payment_methods) {
            $mock->shouldReceive('retrieveUserPaymentMethods')->andReturn($expected_payment_methods);
        });

        $this->mock(RemoveCard::class, function (MockInterface $mock) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    \Mockery::on(fn(string $arg) => $arg === 'abc_123'),
                    \Mockery::on(fn(string $arg) => $arg === 'pm_456'),
                )
                ->andReturnUndefined();
        });

        Livewire::test(CreditCards::class, compact('user'))
            ->assertStatus(200)
            ->assertSet('default_source_id', $card_two->source_id)
            ->call('removeCard', $card_two->source_id)
            ->assertDispatched('admin-notification-sent', notification: [
                'level' => 'success',
                'title' => 'Card deleted!',
                'message' => 'The card has been deleted!',
                'duration' => 3000
            ]);
    }
}
