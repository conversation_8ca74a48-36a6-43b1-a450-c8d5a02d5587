<?php

namespace Tests\Feature\Livewire\Admin\Pages\Widgets;

use App\Livewire\Admin\Pages\Widgets\Faq;
use App\Models\Page;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class FaqTest extends TenantTestCase
{
    #[Test]
    public function it_renders_successfully(): void
    {
        Livewire::test(Faq::class, [
            'page_id' => 1,
            'widget' => [
                'id' => 'abc-def',
                'type' => 'faq',
                'settings' => [
                    'name' => 'FAQ Widget Name',
                    'html_id' => 'test-faq',
                    'max_width' => 'none',
                    'padding' => [
                        'top' => 'sm',
                        'bottom' => 'sm'
                    ],
                    'heading' => 'FAQ Heading',
                    'faqs' => [
                        ['question' => 'Question 1', 'answer' => 'Answer 1'],
                        ['question' => 'Question 2', 'answer' => 'Answer 2'],
                    ],
                ]
            ]
        ])
            ->assertStatus(200)
            ->assertSet('name', 'FAQ Widget Name')
            ->assertSet('html_id', 'test-faq')
            ->assertSet('max_width', 'none')
            ->assertSet('padding_top', 'sm')
            ->assertSet('padding_bottom', 'sm')
            ->assertSet('heading', 'FAQ Heading')
            ->assertSet('faqs', [
                ['question' => 'Question 1', 'answer' => 'Answer 1'],
                ['question' => 'Question 2', 'answer' => 'Answer 2'],
            ]);
    }

    #[Test]
    public function it_can_update_the_widget_settings(): void
    {
        $widget = [
            'id' => 'abc-def',
            'type' => 'faq',
            'settings' => [
                'name' => 'FAQ Widget Name',
                'html_id' => 'test-faq',
                'max_width' => 'none',
                'padding' => [
                    'top' => 'sm',
                    'bottom' => 'sm'
                ],
                'heading' => 'FAQ Heading',
                'faqs' => [
                    ['question' => 'Question 1', 'answer' => 'Answer 1'],
                    ['question' => 'Question 2', 'answer' => 'Answer 2'],
                ],
            ],
        ];

        $page = Page::factory()->create([
            'settings' => [
                'type' => 'promo',
                'content' => [$widget]
            ]
        ]);

        Livewire::test(Faq::class, [
            'page_id' => $page->id,
            'widget' => $widget,
        ])
            ->assertStatus(200)
            ->set('name', 'FAQ Widget Name 2')
            ->set('html_id', 'abc')
            ->set('max_width', 'lg')
            ->set('padding_top', 'lg')
            ->set('padding_bottom', 'lg')
            ->set('heading', 'New FAQ Heading')
            ->set('faqs.0.question', 'New Question 1')
            ->set('faqs.0.answer', 'New Answer 1')
            ->set('faqs.1.question', 'New Question 2')
            ->set('faqs.1.answer', 'New Answer 2')
            ->call('save')
            ->assertHasNoErrors();

        $this->assertDatabaseHas(Page::class, [
            'id' => $page->id,
            'settings' => json_encode([
                'type' => 'promo',
                'content' => [
                    [
                        'id' => 'abc-def',
                        'type' => 'faq',
                        'settings' => [
                            'name' => 'FAQ Widget Name 2',
                            'html_id' => 'abc',
                            'max_width' => 'lg',
                            'padding' => [
                                'top' => 'lg',
                                'bottom' => 'lg'
                            ],
                            'heading' => 'New FAQ Heading',
                            'faqs' => [
                                ['question' => 'New Question 1', 'answer' => 'New Answer 1'],
                                ['question' => 'New Question 2', 'answer' => 'New Answer 2'],
                            ],
                        ],
                    ]
                ]
            ])
        ]);
    }

    #[Test]
    public function it_dispatches_an_event_on_successful_save(): void
    {
        $widget = [
            'id' => 'abc-def',
            'type' => 'faq',
            'settings' => [
                'name' => 'FAQ Widget Name',
                'html_id' => 'test-faq',
                'max_width' => 'none',
                'padding' => [
                    'top' => 'sm',
                    'bottom' => 'sm'
                ],
                'heading' => 'FAQ Heading',
                'faqs' => [
                    ['question' => 'Question 1', 'answer' => 'Answer 1'],
                    ['question' => 'Question 2', 'answer' => 'Answer 2'],
                ],
            ],
        ];

        $page = Page::factory()->create([
            'settings' => [
                'type' => 'promo',
                'content' => [$widget]
            ]
        ]);

        Livewire::test(Faq::class, [
            'page_id' => $page->id,
            'widget' => $widget,
        ])
            ->assertStatus(200)
            ->call('save')
            ->assertHasNoErrors()
            ->assertDispatched('admin-notification-sent', notification: [
                'level' => 'success',
                'title' => 'Widget updated!',
                'message' => 'The widget settings have been successfully updated.',
                'duration' => 3000
            ]);
    }
}
