<?php

namespace Tests\Feature\Livewire\Admin\Pages\Widgets;

use App\Livewire\Admin\Pages\Widgets\Banner;
use App\Models\Page;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class BannerTest extends TenantTestCase
{
    #[Test]
    public function it_renders_successfully(): void
    {
        Livewire::test(Banner::class, [
            'page_id' => 1,
            'widget' => [
                'id' => 'abc-def',
                'type' => 'banner',
                'settings' => [
                    'name' => 'Banner Widget Name',
                    'html_id' => 'test-banner',
                    'max_width' => 'none',
                    'padding' => [
                        'top' => 'sm',
                        'bottom' => 'sm'
                    ],
                    'background' => [
                        'color' => '#ffffff',
                        'image' => 'https://example.com/background.jpg',
                        'effect' => 'parallax',
                        'overlay' => '#000',
                        'attachment' => 'scroll',
                        'vertical_position' => 'top',
                    ],
                    'heading' => [
                        'image' => 'https://example.com/heading.jpg',
                        'max_width' => 'none',
                    ],
                    'subheading' => [
                        'content' => 'Subheading content',
                    ],
                    'cta' => [
                        'label' => 'Call to Action',
                        'url' => 'https://example.com/cta',
                    ]
                ]
            ]
        ])
            ->assertStatus(200)
            ->assertSet('name', 'Banner Widget Name')
            ->assertSet('html_id', 'test-banner')
            ->assertSet('padding_top', 'sm')
            ->assertSet('padding_bottom', 'sm')
            ->assertSet('max_width', 'none')
            ->assertSet('background_color', '#ffffff')
            ->assertSet('background_image', 'https://example.com/background.jpg')
            ->assertSet('background_attachment', 'scroll')
            ->assertSet('background_vertical_position', 'top')
            ->assertSet('background_overlay', '#000')
            ->assertSet('heading_image', 'https://example.com/heading.jpg')
            ->assertSet('heading_max_width', 'none')
            ->assertSet('subheading_content', 'Subheading content')
            ->assertSet('cta_label', 'Call to Action')
            ->assertSet('cta_url', 'https://example.com/cta');
    }

    #[Test]
    public function it_can_update_the_widget_settings(): void
    {
        $widget = [
            'id' => 'abc-def',
            'type' => 'banner',
            'settings' => [
                'name' => 'Banner Widget Name',
                'html_id' => 'test-banner',
                'max_width' => 'none',
                'padding' => [
                    'top' => 'sm',
                    'bottom' => 'sm'
                ],
                'background' => [
                    'color' => '#ffffff',
                    'image' => 'https://example.com/background.jpg',
                    'effect' => 'parallax',
                    'overlay' => '#000',
                    'attachment' => 'scroll',
                    'vertical_position' => 'bottom',
                ],
                'heading' => [
                    'image' => 'https://example.com/heading.jpg',
                    'max_width' => 'none',
                ],
                'subheading' => [
                    'content' => 'Subheading content',
                ],
                'cta' => [
                    'label' => 'Call to Action',
                    'url' => 'https://example.com/cta',
                ]
            ]
        ];

        $page = Page::factory()->create([
            'settings' => [
                'type' => 'promo',
                'content' => [$widget]
            ]
        ]);

        Livewire::test(Banner::class, [
            'page_id' => $page->id,
            'widget' => $widget,
        ])
            ->assertStatus(200)
            ->set('name', 'Banner Widget Name 2')
            ->set('html_id', 'abc')
            ->set('max_width', 'lg')
            ->set('padding_top', 'lg')
            ->set('padding_bottom', 'lg')
            ->set('background_color', '#000000')
            ->set('background_image', 'https://example.com/new-background.jpg')
            ->set('background_attachment', 'fixed')
            ->set('background_vertical_position', 'top')
            ->set('background_overlay', '#ffffff')
            ->set('heading_image', 'https://example.com/new-heading.jpg')
            ->set('heading_max_width', 'lg')
            ->set('subheading_content', 'New subheading content')
            ->set('cta_label', 'New CTA')
            ->set('cta_url', 'https://example.com/new-cta')
            ->call('save')
            ->assertHasNoErrors();

        $this->assertDatabaseHas(Page::class, [
            'id' => $page->id,
            'settings' => json_encode([
                'type' => 'promo',
                'content' => [
                    [
                        'id' => 'abc-def',
                        'type' => 'banner',
                        'settings' => [
                            'name' => 'Banner Widget Name 2',
                            'html_id' => 'abc',
                            'max_width' => 'lg',
                            'padding' => [
                                'top' => 'lg',
                                'bottom' => 'lg'
                            ],

                            'background' => [
                                'color' => '#000000',
                                'image' => 'https://example.com/new-background.jpg',
                                'attachment' => 'fixed',
                                'vertical_position' => 'top',
                                'overlay' => '#ffffff',
                            ],
                            'heading' => [
                                'image' => 'https://example.com/new-heading.jpg',
                                'max_width' => 'lg',
                            ],
                            'subheading' => [
                                'content' => 'New subheading content',
                            ],
                            'cta' => [
                                'label' => 'New CTA',
                                'url' => 'https://example.com/new-cta',
                            ]
                        ],
                    ]
                ]
            ])
        ]);
    }

    #[Test]
    public function it_dispatches_an_event_on_successful_save(): void
    {
        $widget = [
            'id' => 'abc-def',
            'type' => 'banner',
            'settings' => [
                'name' => 'Banner Widget Name',
                'html_id' => 'test-banner',
                'padding' => [
                    'top' => 'sm',
                    'bottom' => 'sm'
                ],
                'max_width' => 'none',
                'background' => [
                    'color' => '#ffffff',
                    'image' => 'https://example.com/background.jpg',
                    'attachment' => 'scroll',
                    'vertical_position' => 'top',
                    'overlay' => '#000',
                ],
                'heading' => [
                    'image' => 'https://example.com/heading.jpg',
                    'max_width' => 'none',
                ],
                'subheading' => [
                    'content' => 'Subheading content',
                ],
                'cta' => [
                    'label' => 'Call to Action',
                    'url' => 'https://example.com/cta',
                ]
            ]
        ];

        $page = Page::factory()->create([
            'settings' => [
                'type' => 'promo',
                'content' => [$widget]
            ]
        ]);

        Livewire::test(Banner::class, [
            'page_id' => $page->id,
            'widget' => $widget,
        ])
            ->assertStatus(200)
            ->call('save')
            ->assertHasNoErrors()
            ->assertDispatched('admin-notification-sent', notification: [
                'level' => 'success',
                'title' => 'Widget updated!',
                'message' => 'The widget settings have been successfully updated.',
                'duration' => 3000
            ]);
    }
}
