<?php

namespace Tests\Feature\Livewire\Admin\Pages\Widgets;

use App\Livewire\Admin\Pages\Widgets\Product;
use App\Models\Page;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProductTest extends TenantTestCase
{
    #[Test]
    public function it_renders_successfully(): void
    {
        Livewire::test(Product::class, [
            'page_id' => 1,
            'widget' => [
                'id' => 'abc-def',
                'type' => 'product',
                'settings' => [
                    'name' => 'Product Widget Name',
                    'html_id' => 'abc',
                    'max_width' => 'none',
                    'padding' => [
                        'top' => 'sm',
                        'bottom' => 'sm'
                    ],
                    'background' => [
                        'color' => '#ffffff',
                    ],
                    'product_ids' => '1,3,5',
                    'cta' => [
                        'label' => 'Test label',
                        'action' => 'add_to_cart',
                    ],
                ]
            ]
        ])
            ->assertStatus(200)
            ->assertSet('name', 'Product Widget Name')
            ->assertSet('padding_top', 'sm')
            ->assertSet('padding_bottom', 'sm')
            ->assertSet('max_width', 'none')
            ->assertSet('cta_label', 'Test label')
            ->assertSet('cta_action', 'add_to_cart')
            ->assertSet('background_color', '#ffffff')
            ->assertSet('product_ids', '1,3,5');
    }

    #[Test]
    public function it_can_update_the_widget_settings(): void
    {
        $widget = [
            'id' => 'abc-def',
            'type' => 'product',
            'settings' => [
                'name' => 'Product Widget Name',
                'html_id' => 'abc',
                'max_width' => 'none',
                'padding' => [
                    'top' => 'sm',
                    'bottom' => 'sm'
                ],
                'background' => [
                    'color' => '#ffffff',
                ],
                'cta' => [
                    'label' => 'Test label',
                    'action' => 'add_to_cart',
                ],
                'product_ids' => '1,3,5',
            ],
        ];

        $page = Page::factory()->create([
            'settings' => [
                'type' => 'promo',
                'content' => [$widget]
            ]
        ]);

        Livewire::test(Product::class, [
            'page_id' => $page->id,
            'widget' => $widget,
        ])
            ->assertStatus(200)
            ->set('name', 'New Product Widget Name')
            ->set('padding_top', 'lg')
            ->set('padding_bottom', 'lg')
            ->set('max_width', 'lg')
            ->set('cta_label', 'New Test label')
            ->set('cta_action', 'show_details')
            ->set('background_color', '#eeeeee')
            ->set('product_ids', '7,8,9')
            ->call('save')
            ->assertHasNoErrors();

        $this->assertDatabaseHas(Page::class, [
            'id' => $page->id,
            'settings' => json_encode([
                'type' => 'promo',
                'content' => [
                    [
                        'id' => 'abc-def',
                        'type' => 'product',
                        'settings' => [
                            'name' => 'New Product Widget Name',
                            'html_id' => 'abc',
                            'max_width' => 'lg',
                            'padding' => [
                                'top' => 'lg',
                                'bottom' => 'lg'
                            ],
                            'background' => [
                                'color' => '#eeeeee',
                            ],
                            'product_ids' => '7,8,9',
                            'cta' => [
                                'label' => 'New Test label',
                                'action' => 'show_details',
                            ],
                        ],
                    ]
                ]
            ])
        ]);
    }

    #[Test]
    public function it_dispatches_an_event_on_successful_save(): void
    {
        $widget = [
            'id' => 'abc-def',
            'type' => 'product',
            'settings' => [
                'name' => 'Product Widget Name',
                'html_id' => 'abc',
                'max_width' => 'none',
                'padding' => [
                    'top' => 'sm',
                    'bottom' => 'sm'
                ],
                'cta' => [
                    'label' => 'Test label',
                    'action' => 'add_to_cart',
                ],
                'background_color' => '#ffffff',
                'product_ids' => '1,3,5',
            ],
        ];

        $page = Page::factory()->create(['settings' => ['type' => 'promo', 'content' => [$widget]]]);

        Livewire::test(Product::class, [
            'page_id' => $page->id,
            'widget' => $widget,
        ])
            ->assertStatus(200)
            ->call('save')
            ->assertHasNoErrors()
            ->assertDispatched('admin-notification-sent', notification: [
                'level' => 'success',
                'title' => 'Widget updated!',
                'message' => 'The widget settings have been successfully updated.',
                'duration' => 3000
            ]);
    }
}
